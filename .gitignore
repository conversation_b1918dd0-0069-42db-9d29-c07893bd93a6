### Go template
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# datafridge/

# Jetbrains IDE folder
/.idea/

# Local env files
.env.*

# Swagger docs
/docs/

##
## Visual Studio Code
##
.vscode/*

# Docker compose file
docker-compose.yaml

# Xlsx files
*.xlsx