CREATE TABLE IF NOT EXISTS orders
(
    id            bigserial
    CONSTRAINT orders_pk
    PRIMARY KEY,
    order_id      varchar(32),
    order_details text,
    timestamp     timestamp,
    is_delivered  boolean DEFAULT FALSE NOT NULL,
    store_id               varchar(100),
    user_id                varchar(100),
    user_friendly_id       varchar(100),
    user_first_name        varchar(100),
    user_last_name         varchar(100)
    );

CREATE UNIQUE INDEX IF NOT EXISTS uix_order_id
    ON orders (order_id);