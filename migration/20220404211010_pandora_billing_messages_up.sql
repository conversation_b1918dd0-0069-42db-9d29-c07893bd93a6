create table if not exists pandora_billing_messages
(
    id               bigserial
        constraint pandora_billing_messages_pk
            primary key,
    order_id         varchar(32),
    order_details    text,
    status           varchar(15),
    version          int,
    logo_message_id       varchar(36),
    logo_order_details text,
    timestamp        timestamp
);

create unique index if not exists  uix_pandora_billing_messages_logo_message_id
    on pandora_billing_messages (logo_message_id);
    
create unique index if not exists pandora_billing_messages_orderid_status_version_uindex 
    on pandora_billing_messages (order_id,status,version);