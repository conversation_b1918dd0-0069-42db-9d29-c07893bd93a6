CREATE TABLE IF NOT EXISTS order_refunds
(
    id                     bigserial
    CONSTRAINT order_refunds_pk
    PRIMARY KEY,
    order_id               varchar(36)                   NOT NULL,
    transaction_id         varchar(100)                  NOT NULL,
    refund_type_id         integer                       NOT NULL,
    refund_amount          numeric(18, 4) DEFAULT 0.0000 NOT NULL,
    refund_tip_amount      numeric(18, 4) DEFAULT 0.0000 NOT NULL,
    timestamp              timestamp                     NOT NULL,
    is_delivered           boolean        DEFAULT FALSE  NOT NULL,
    is_not_required_refund boolean        DEFAULT FALSE  NOT NULL
    );

CREATE UNIQUE INDEX IF NOT EXISTS uix_order_id_transaction_id
    ON order_refunds (order_id, transaction_id);