CREATE TABLE IF NOT EXISTS orders_payment
(
    id                          bigserial
        CONSTRAINT orders_payment_pk
            PRIMARY KEY,
    order_id                    varchar        NOT NULL,
    payment_type_id             integer        NOT NULL,
    payment_method_id           integer        NOT NULL,
    status                      integer        NOT NULL,
    payment_operation_type      integer        NOT NULL,
    payment_operation_type_date timestamp      NOT NULL,
    amount                      numeric(18, 4) NOT NULL,
    transaction_id              varchar(100)
);

CREATE UNIQUE INDEX uix_order_id_payment_operation_type_transaction_id
    ON orders_payment (order_id, payment_operation_type, transaction_id);