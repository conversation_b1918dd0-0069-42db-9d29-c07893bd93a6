CREATE TABLE IF NOT EXISTS server_configurations
(
    id           bigserial
        CONSTRAINT server_configurations_pk
            PRIMARY KEY,
    data_type_id integer      NOT NULL,
    data_key     varchar(100) NOT NULL,
    data_value   text         NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS uix_data_key
    ON server_configurations (data_key);

INSERT INTO server_configurations (data_type_id, data_key, data_value)
VALUES (2, 'IS_SQS_STORE_STOCK_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_PRODUCT_STREAM_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_ORDER_REFUND_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_SAMPLE_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_ORDER_FULFILLMENT_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_DC_ADAPTER_PO_INBOUND_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_IM_WAREHOUSE_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_PURCHASE_ORDER_DC_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_PURCHASE_ORDER_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_PURCHASE_ORDER_REFUND_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_SAMPLE_PROTO_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_SAMPLE_RECEIVER_OPEN', 'false'),
        (2, 'IS_PUB_STORE_MANAGEMENT_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_PANDORA_BILLING_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_ETTN_RECEIVER_OPEN','false'),
        (2, 'IS_SQS_ORDER_OFFLINE_PAYMENT_RECEIVER_OPEN','false'),
        (2, 'IS_SQS_SALESFORCE_CREATE_ACCOUNT_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_SALESFORCE_UPDATE_ACCOUNT_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_SALESFORCE_UPDATE_CONTACT_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_SALESFORCE_UPDATE_ADDRESS_RECEIVER_OPEN', 'false'),
        (2, 'PAYMENT_METHOD_MAPS', '{"creditcard_isbank": "OnlineCreditCard","creditcard_garanti": "OnlineCreditCard","yemekpay_creditcard": "OnlineCreditCard","PAID":"OnlineCreditCard","Yemekpay Credit Card":"OnlineCreditCard","CARD_ON_DELIVERY":"CreditCard","Yemek Pay Kart":"CreditCard","credit_card":"CreditCard","CASH_ON_DELIVERY":"Cash","no_payment": "Cash","Cash":"Cash","Nakit":"Cash"}'),
        (2, 'IS_SQS_ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_OPEN','false'),
        (2, 'IS_SQS_ORDER_FULFILLMENT_CANCELLATION_RECEIVER_OPEN', 'false'),
        (2, 'IS_SQS_ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_OPEN','false')