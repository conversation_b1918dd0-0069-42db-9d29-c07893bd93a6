CREATE TABLE IF NOT EXISTS purchase_orders
(
    id              bigserial
        CONSTRAINT purchase_orders_pk
            PRIMARY KEY,
    order_id        varchar   NOT NULL,
    order_reference varchar   NOT NULL,
    order_type      varchar   NOT NULL,
    event_type      varchar   NOT NULL,
    created_date    timestamp NOT NULL
);

CREATE UNIQUE INDEX uix_order_id_order_type_event_type
    ON purchase_orders (order_id, order_type, event_type);