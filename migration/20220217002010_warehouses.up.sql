CREATE TABLE IF NOT EXISTS warehouses
(
    id                 serial
        CONSTRAINT warehouses_pk
            PRIMARY KEY,
    salesforce_grid_id varchar(36),
    warehouse_id       varchar(36),
    warehouse_name     varchar(100),
    address            varchar(200),
    city               varchar(50),
    created_date       timestamp,
    modified_date      timestamp,
    is_delivered       boolean DEFAULT FALSE NOT NULL,
    payload_timestamp  timestamp,
    vendor_code        varchar(100)
);

CREATE UNIQUE INDEX IF NOT EXISTS uix_warehouse_id
    ON warehouses (warehouse_id);