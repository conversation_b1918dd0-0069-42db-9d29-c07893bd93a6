CREATE TABLE IF NOT EXISTS return_reasons
(
    id     bigserial
        CONSTRAINT return_reasons_pk
            PRIMARY KEY,
    reason varchar(256),
    code   varchar(256)
);

INSERT INTO return_reasons(id, reason, code)
VALUES (1, 'sku_expired', 'IADE00.001'),
       (2, 'supplier_induced', 'IADE00.002'),
       (3, 'warehouse_induced_damage', 'IADE00.003'),
       (4, 'infra_malfunction_overheating', 'IADE00.004'),
       (5, 'cabinet_malfunction', 'IADE00.005'),
       (6, 'product_deterioration_due_customer_order_cancellation', 'IADE00.006'),
       (7, 'empty_carboy_other_returns', 'IADE00.007'),
       (8, 'qc_fail', 'IADE00.008'),
       (9, 'supplier_defect', 'IADE00.009');
