CREATE TABLE IF NOT EXISTS product_stream_messages
(
    id      bigserial
            CONSTRAINT product_stream_messages_pk
            PRIMARY KEY,
    product_id      varchar(36) NOT NULL,
    sku             varchar(36) NOT NULL,
    message         text        NOT NULL,
    timestamp       timestamp   NOT NULL,
    message_date    timestamp   NOT NULL,
    name         varchar(100)   NOT NULL,
    price        numeric(18, 2) NOT NULL,
    list_price   numeric(18, 2) NOT NULL,
    stock_amount integer NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS uix_product_id
    ON product_stream_messages (product_id);

CREATE UNIQUE INDEX IF NOT EXISTS uix_sku
    ON product_stream_messages (sku);