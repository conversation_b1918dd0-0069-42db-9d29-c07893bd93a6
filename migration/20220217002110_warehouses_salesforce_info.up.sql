CREATE TABLE IF NOT EXISTS warehouses_salesforce_info
(
    id                 bigserial
        CONSTRAINT warehouse_types_pk
            PRIMARY KEY,
    salesforce_grid_id varchar(36) NOT NULL,
    warehouse_type     integer,
    created_date       timestamp,
    modified_date      timestamp,
    is_delivered bool DEFAULT FALSE NOT NULL,
    payload_timestamp timestamp,
    replay_id bigint,
    commercial_name varchar(300),
    commercial_type integer,
    tax_administration varchar(300),
    tax_number varchar(120),
    iban varchar(120),
    billing_address_line1 varchar(300),
    billing_address_line2 varchar(300),
    billing_address_town varchar(120),
    billing_address_city varchar(120),
    billing_address_country varchar(120),
    billing_address_postcode varchar(120),
    restaurant_address_line1 varchar(300),
    restaurant_address_line2 varchar(300),
    restaurant_address_town varchar(120),
    restaurant_address_city varchar(120),
    restaurant_address_country varchar(120),
    restaurant_address_postcode varchar(120),
    email_address varchar(120),
    mobile_phone_number varchar(120),
    phone_number varchar(120),
    store_site_name varchar(120),
    responsible_sales_representative varchar(120)
);

CREATE UNIQUE INDEX IF NOT EXISTS uix_salesforce_grid_id
    ON warehouses_salesforce_info (salesforce_grid_id);