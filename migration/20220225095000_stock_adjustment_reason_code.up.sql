CREATE TABLE IF NOT EXISTS stock_adjustment_reason_code
(
    id                        bigserial
    CONSTRAINT stock_adjustment_reason_code_pk
    PRIMARY KEY,
    global_reason_code        int          not null,
    global_reason_description varchar(200),
    logo_reason_code          varchar(200) not null,
    logo_reason_description   varchar(200),
    logo_slip_type_id         int          not null
    );

INSERT INTO stock_adjustment_reason_code (global_reason_code, global_reason_description, logo_reason_code, logo_reason_description, logo_slip_type_id)
VALUES (8, 'Product Expired', 'IMHA00.001', 'IMHA-SKT Doldu', 11),
       (7, 'Product Damaged', 'IMHA00.002', 'IMHA-Üretici kaynaklı bozulma/küflenme', 11),
       (5, 'Internal Vendor Subtraction', 'IMHA00.003', 'IMHA-Depo kaynaklı kırılma/hasar', 11),
       (4, 'Customer Replacement', 'IMHA00.004', 'IMHA-Depo tesisat/fiş arı<PERSON><PERSON> kaynaklı ısınma', 11),
       (13, 'Packs to Units', 'SAYEKS.003', '<PERSON><PERSON><PERSON> Eks<PERSON>ği-Dispatch Sayım Eksiği', 51),
       (1, 'Bubble Tea Sale', 'IMHA00.006', 'IMHA-Müşt. iptali kaynaklı ürün bozulması/küflenmesi', 11),
       (20, 'Antidote Coffee Subtraction', 'IMHA00.007', 'IMHA-Numune tadım/kalite kontrol', 11),
       (12, 'Supplier Return', 'IMHA00.009', 'IMHA-Merkez Depo Hasarlı Mutabık Kalınan Kayıt', 11),
       (10, 'Store Transfer Subtraction', 'IMHA00.010', 'IMHA-Merkez Depo Hasarlı Mutabakatsız Kayıt', 11),
       (11, 'System Error', 'IMHA00.014', 'IMHA-Kurye Kaynaklı Kaza', 11),
       (3, 'Rte Nhom Vendor Sale', 'IMHA00.013', 'Transfer Kaynaklı Hasar', 11),
       (9, 'Store Internal Use', 'SARF00.001', 'Sarf malzeme/İç tüketim', 12),
       (16, 'Stock Addition', 'SAYFAZ.001', 'Sayım Fazlası', 50),
       (17, 'Store Transfer Addition', 'SAYFAZ.003', 'Dispatch- Sayım Fazlası', 50),
       (6, 'Stock Subtraction', 'SAYEKS.001', 'Sayım Eksiği', 51)