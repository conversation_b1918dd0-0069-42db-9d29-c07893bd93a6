package main

import (
	"fmt"

	"logo-adapter/internal/data/pubsub/receiver/store"

	"logo-adapter/internal/data/pubsub/receiver/dc"
	supplierReceiver "logo-adapter/internal/data/pubsub/receiver/supplier"
	orderReceiver "logo-adapter/internal/data/sqs/receiver/order"
	"logo-adapter/internal/data/sqs/receiver/salesforce"

	"logo-adapter/internal/api/v1/controller/auth"
	"logo-adapter/internal/api/v1/controller/dispatch"
	"logo-adapter/internal/api/v1/controller/health"
	"logo-adapter/internal/api/v1/controller/order"
	productController "logo-adapter/internal/api/v1/controller/product"
	controllerV1 "logo-adapter/internal/api/v1/controller/sample"
	controllerV2 "logo-adapter/internal/api/v2/controller/sample"
	"logo-adapter/internal/data/pubsub/receiver/sample"
	"logo-adapter/internal/data/pubsub/receiver/warehouse"
	"logo-adapter/internal/data/sqs/receiver/product"
	sampleReceiver "logo-adapter/internal/data/sqs/receiver/sample"
	sqsReceiver "logo-adapter/internal/data/sqs/receiver/warehouse"
	"logo-adapter/internal/job"

	"github.com/robfig/cron/v3"

	"logo-adapter/docs"
	"logo-adapter/internal/api"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title                       QCommerce Logo Adapter
// @version                     1.0
// @description                 This is an adapter that connects to Logo accounting application.
// @termsOfService              http://swagger.io/terms/
// @contact.name                API Support
// @contact.url                 http://www.swagger.io/support
// @contact.email               <EMAIL>
// @license.name                Apache 2.0
// @license.url                 http://www.apache.org/licenses/LICENSE-2.0.html
// @host                        localhost:8080
// @BasePath                    /api
// @accept                      json
// @produce                     json
// @schemes                     http https
// @securityDefinitions.apikey  Bearer
// @in                          header
// @name                        Authorization
func main() {
	environment := env.New()
	environment.Init()
	loggr := logger.New(environment)
	defer loggr.Sync()
	cachr := cacher.New(environment)
	validatr := validator.New()

	// router := gin.Default()
	router := gin.New()
	router.Use(api.LoggingMiddleware(loggr))
	addRoutes(router, environment, loggr, validatr, cachr)
	addSwagger(router, environment)
	addJobs(environment, loggr, validatr, cachr)
	addReceivers(environment, loggr, validatr, cachr)

	// listen and serve on 0.0.0.0:8080 (for windows "localhost:8080")
	router.Run()
}

func addRoutes(router *gin.Engine, environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) {
	api := router.Group("api")
	health.NewHealthController(environment, loggr, validatr, cachr, nil).RegisterRoutes(api)

	v1 := api.Group("v1")
	v2 := api.Group("v2")
	auth.NewAuthController(environment, loggr, validatr, cachr, nil).RegisterRoutes(v1)
	controllerV1.NewSampleController(environment, loggr, validatr, cachr, nil).RegisterRoutes(v1)
	dispatch.NewDispatchController(environment, loggr, validatr, cachr, nil).RegisterRoutes(v1)
	order.NewOrderController(environment, loggr, validatr, cachr, nil).RegisterRoutes(v1)
	controllerV2.NewSampleController(environment, loggr, validatr, cachr).RegisterRoutes(v2)
	order.NewOrderPaymentController(environment, loggr, validatr, cachr, nil).RegisterRoutes(v1)
	productController.NewProductController(environment, loggr, validatr, cachr, nil).RegisterRoutes(v1)
}

func addSwagger(router *gin.Engine, environment env.IEnvironment) {
	docs.SwaggerInfo.Title = fmt.Sprintf("QCommerce Logo Adapter (%v)", environment.Get(env.AppEnvironment))
	docs.SwaggerInfo.Host = environment.Get(env.AppHost)
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}

func addJobs(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) {
	c := cron.New()
	sampleJob := job.NewGetGoogleSampleJob(environment, loggr, validatr, cachr, nil)
	c.AddFunc("@every 30m", sampleJob.GetGoogle)
	supplierJob := job.NewSendSupplierToLogoJob(environment, loggr, validatr, cachr, nil, nil)
	c.AddFunc("@every 10m", supplierJob.SendSuppliers)
	warehouseJob := job.NewSendWarehouseToLogoJob(environment, loggr, validatr, cachr, nil)
	c.AddFunc("@every 10m", warehouseJob.SendWarehouses)
	salesforceJob := job.NewSendSalesforceAccountsToLogoJob(environment, loggr, validatr, cachr, nil)
	c.AddFunc("@every 10m", salesforceJob.SendSalesforceAccounts)
	warehouseStockCountJob := job.NewSendWarehouseStockCountsToLogoJob(environment, loggr, validatr, cachr, nil, nil)
	c.AddFunc("*/30 * * * *", warehouseStockCountJob.SendWarehouseStockCounts)
	warehouseStockCountApprovalStatusJob := job.NewSendWarehouseStockCountApprovalStatusJob(environment, loggr, validatr, cachr, nil, nil)
	c.AddFunc("*/5 * * * *", warehouseStockCountApprovalStatusJob.SendWarehouseStockCountsApprovalStatus)
	sendPartialRefundOrdersToLogoJob := job.NewSendPartialRefundOrdersToLogoJob(environment, loggr, validatr, cachr, nil, nil)
	c.AddFunc("0 8 * * *", sendPartialRefundOrdersToLogoJob.SendPartialRefundOrders)
	c.Start()
}

func addReceivers(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) {
	if environment.Get(env.AppEnvironment) != "Production" {
		go sample.NewSampleReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
		go sample.NewSampleProtoReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
		go sampleReceiver.NewSampleReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	}
	go store.NewStoreManagementReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go dc.NewDcPurchaseOrderInboundReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go supplierReceiver.NewPurchaseOrderReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go supplierReceiver.NewPurchaseOrderRefundReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go product.NewProductStreamReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(5)
	go warehouse.NewWarehouseReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go sqsReceiver.NewWarehouseStockReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go orderReceiver.NewOrderFulfillmentReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(3)
	go orderReceiver.NewPandoraBillingReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go orderReceiver.NewEttnReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go orderReceiver.NewOrderOfflinePaymentReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go orderReceiver.NewOrderRefundOfflinePaymentReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go salesforce.NewSalesforceCreateAccountReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go salesforce.NewSalesforceUpdateAccountReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go salesforce.NewSalesforceUpdateContactReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go salesforce.NewSalesforceUpdateAddressReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go orderReceiver.NewOrderFulfillmentCancellationReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go orderReceiver.NewOrderFulfillmentReconciliationReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	//go orderReceiver.NewOrderStreamReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(3)
	//go orderReceiver.NewOrderStatusCancellationStreamReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(3)
	go supplierReceiver.NewProductReturnReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
	go orderReceiver.NewPandoraBillingSoftposReceiver(environment, loggr, validatr, cachr, nil, nil).InitReceivers(1)
}
