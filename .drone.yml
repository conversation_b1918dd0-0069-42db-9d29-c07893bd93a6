kind: pipeline
type: kubernetes
name: logo-adapter stg

trigger:
  branch:
    - staging
  event:
    - push

steps:
- name: publish stg
  image: plugins/ecr
  environment:
    GITHUB_USERNAME:
      from_secret: GH_READ_USERNAME
    GITHUB_TOKEN:
      from_secret: GH_READ_TOKEN
  settings:
    access_key:
      from_secret: QC_GLOBAL_AWS_SECRET_KEY_ID_STG
    secret_key:
      from_secret: QC_GLOBAL_AWS_SECRET_ACCESS_KEY_STG
    repo: stg-logo-adapter
    registry: 839959062732.dkr.ecr.us-east-2.amazonaws.com
    region: us-east-2
    dockerfile: Dockerfile
    tags:
      - ${DRONE_COMMIT_SHA}
    build_args_from_env:
      - GITHUB_USERNAME
      - GITHUB_TOKEN


- name: trigger spinnaker stg
  image: eu.gcr.io/dh-new-vertical-system-77026/nv-spinnaker-executor
  environment:
    G<PERSON><PERSON><PERSON><PERSON>_SPINNAKER_USERNAME:
      from_secret: QC_SPINNAKER_USER
    GLOBAL_SPINNAKER_PASSWORD:
      from_secret: QC_SPINNAKER_PASSWORD
    WEBHOOK_NAME: logo-adapter-staging
    PARAMETERS: '{"version":"${DRONE_COMMIT_SHA}", "branch":"${DRONE_COMMIT_BRANCH}"}'
  commands:
    - /trigger.sh

---

kind: pipeline
type: kubernetes
name: logo-adapter main

trigger:
  branch:
    - main
  event:
    - push

steps:
- name: publish live
  image: plugins/ecr
  environment:
    GITHUB_USERNAME:
      from_secret: GH_READ_USERNAME
    GITHUB_TOKEN:
      from_secret: GH_READ_TOKEN
  settings:
    access_key:
      from_secret: QC_GLOBAL_AWS_SECRET_KEY_ID_LIVE
    secret_key:
      from_secret: QC_GLOBAL_AWS_SECRET_ACCESS_KEY_LIVE
    repo: live-logo-adapter
    registry: 569581431544.dkr.ecr.me-south-1.amazonaws.com
    region: me-south-1
    dockerfile: Dockerfile
    tags:
      - ${DRONE_COMMIT_SHA}
    build_args_from_env:
      - GITHUB_USERNAME
      - GITHUB_TOKEN
