---
# Source: logo-adapter/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-live-me-01-logo-adapter
  name: logo-adapter
  namespace: logo-adapter
---
# Source: logo-adapter/templates/nlb-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: logo-adapter-me-service
  namespace: "logo-adapter"
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-private-ipv4-addresses: '*************, *************, *************'
    external-dns.alpha.kubernetes.io/hostname: logo-adapter.me.prod.aws.qcommerce.live
    service.beta.kubernetes.io/aws-load-balancer-internal: 'true'
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:me-south-1:************:certificate/1c065aac-ca4e-4672-a590-1cd4db0fc485
    service.beta.kubernetes.io/aws-load-balancer-scheme: internal
    service.beta.kubernetes.io/aws-load-balancer-type: nlb-ip
  labels:
    name: logo-adapter-me    
    helm.sh/chart: logo-adapter
    app.kubernetes.io/name: logo-adapter
    app.kubernetes.io/instance: RELEASE-NAME
    app.kubernetes.io/version: "${ parameters.version }"
    app.kubernetes.io/managed-by: Helm    
    country: "n-a"
    env: "live"
    projectName: "qcommerce-prod"
    region: "me"    
    dh_app: "logo-adapter"
    dh_cc_id: "**********"
    dh_env: "live"
    dh_platform: "qcommerce"
    dh_region: "me-south-1"
    dh_squad: "yemek"
    dh_tribe: "qc-operations"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    protocol: TCP
    targetPort: http
  - name: https
    port: 443
    targetPort: http
  selector:
    name: logo-adapter-me
---
# Source: logo-adapter/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logo-adapter-me
  namespace: "logo-adapter"
  labels:
    name: logo-adapter-me    
    helm.sh/chart: logo-adapter
    app.kubernetes.io/name: logo-adapter
    app.kubernetes.io/instance: RELEASE-NAME
    app.kubernetes.io/version: "${ parameters.version }"
    app.kubernetes.io/managed-by: Helm    
    country: "n-a"
    env: "live"
    projectName: "qcommerce-prod"
    region: "me"    
    dh_app: "logo-adapter"
    dh_cc_id: "**********"
    dh_env: "live"
    dh_platform: "qcommerce"
    dh_region: "me-south-1"
    dh_squad: "yemek"
    dh_tribe: "qc-operations"
    version: v1
spec:
  selector:
    matchLabels:
      name: logo-adapter-me
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-addr: "https://app.vault.inhouse.dh-darkstores-live.net"
        vault.security.banzaicloud.io/vault-role: "vault-role"
        vault.security.banzaicloud.io/vault-path: eks-live-me-01
        ad.datadoghq.com/logo-adapter.logs: '[{"source":"logo-adapter","service":"logo-adapter"}]'
      labels:        
        helm.sh/chart: logo-adapter
        app.kubernetes.io/name: logo-adapter
        app.kubernetes.io/instance: RELEASE-NAME
        app.kubernetes.io/version: "${ parameters.version }"
        app.kubernetes.io/managed-by: Helm        
        country: "n-a"
        env: "live"
        projectName: "qcommerce-prod"
        region: "me"        
        dh_app: "logo-adapter"
        dh_cc_id: "**********"
        dh_env: "live"
        dh_platform: "qcommerce"
        dh_region: "me-south-1"
        dh_squad: "yemek"
        dh_tribe: "qc-operations"
        name: logo-adapter-me
        version: v1
        tags.datadoghq.com/service: "logo-adapter"
    spec:
      serviceAccountName: logo-adapter
      securityContext:
        {}
      containers:
        - name: logo-adapter
          securityContext:
            {}
          image: "************.dkr.ecr.me-south-1.amazonaws.com/live-logo-adapter:${ parameters.version }"
          imagePullPolicy: IfNotPresent
          env:
            - name: APP_ENVIRONMENT
              value: Production
            - name: APP_HOST
              value: vault:yemeksepeti/data/logo-adapter/Production#APP_HOST
            - name: DATA_FRIDGE_VENDOR_PROXY_AUTH_TOKEN
              value: vault:yemeksepeti/data/logo-adapter/Production#DATA_FRIDGE_VENDOR_PROXY_AUTH_TOKEN
            - name: DATA_FRIDGE_VENDOR_PROXY_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#DATA_FRIDGE_VENDOR_PROXY_URL
            - name: DC_ADAPTER_PO_INBOUND_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#DC_ADAPTER_PO_INBOUND_RECEIVER_PROJECT_ID
            - name: DC_ADAPTER_PO_INBOUND_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#DC_ADAPTER_PO_INBOUND_RECEIVER_SUBSCRIPTION_ID
            - name: DEFAULT_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Production#DEFAULT_SA_JSON
            - name: IM_WAREHOUSE_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#IM_WAREHOUSE_RECEIVER_PROJECT_ID
            - name: IM_WAREHOUSE_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#IM_WAREHOUSE_RECEIVER_SUBSCRIPTION_ID
            - name: INVOICE_API_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#INVOICE_API_URL
            - name: JWT_SECRET
              value: vault:yemeksepeti/data/logo-adapter/Production#JWT_SECRET
            - name: LOGO_PROXY_SECRET
              value: vault:yemeksepeti/data/logo-adapter/Production#LOGO_PROXY_SECRET
            - name: LOGO_PROXY_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#LOGO_PROXY_URL
            - name: ORDER_FULFILLMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_FULFILLMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_FULFILLMENT_RECEIVER_SQS_REGION
            - name: POSTGRESQL_CONNECTION_STRING
              value: vault:yemeksepeti/data/logo-adapter/Production#POSTGRESQL_CONNECTION_STRING
            - name: PRODUCT_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#PRODUCT_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: PURCHASE_ORDER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#PURCHASE_ORDER_PROJECT_ID
            - name: PURCHASE_ORDER_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#PURCHASE_ORDER_RECEIVER_SUBSCRIPTION_ID
            - name: PURCHASE_ORDER_REFUND_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#PURCHASE_ORDER_REFUND_PROJECT_ID
            - name: PURCHASE_ORDER_REFUND_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#PURCHASE_ORDER_REFUND_SUBSCRIPTION_ID
            - name: REDIS_ADDRESS
              value: vault:yemeksepeti/data/logo-adapter/Production#REDIS_ADDRESS
            - name: REDIS_PASSWORD
              value: vault:yemeksepeti/data/logo-adapter/Production#REDIS_PASSWORD
            - name: SAMPLE_PROTO_PUBLISHER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Production#SAMPLE_PROTO_PUBLISHER_SA_JSON
            - name: SAMPLE_PROTO_RECEIVER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Production#SAMPLE_PROTO_RECEIVER_SA_JSON
            - name: SAMPLE_PUBLISHER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Production#SAMPLE_PUBLISHER_SA_JSON
            - name: SAMPLE_RECEIVER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Production#SAMPLE_RECEIVER_SA_JSON
            - name: SAMPLE_PUBLISHER_SNS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#SAMPLE_PUBLISHER_SNS_REGION
            - name: SAMPLE_PUBLISHER_SNS_TOPIC_ARN
              value: vault:yemeksepeti/data/logo-adapter/Production#SAMPLE_PUBLISHER_SNS_TOPIC_ARN
            - name: STORE_MANAGEMENT_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#STORE_MANAGEMENT_RECEIVER_PROJECT_ID
            - name: STORE_MANAGEMENT_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#STORE_MANAGEMENT_RECEIVER_SUBSCRIPTION_ID
            - name: SUPPLIER_PROXY_GRPC_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#SUPPLIER_PROXY_GRPC_URL
            - name: PRODUCT_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#PRODUCT_STREAM_RECEIVER_SQS_REGION
            - name: STORE_STOCK_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#STORE_STOCK_RECEIVER_SQS_REGION
            - name: STORE_STOCK_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#STORE_STOCK_RECEIVER_SQS_QUEUE_URL
            - name: LOGISTIC_POS_PROXY_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#LOGISTIC_POS_PROXY_URL
            - name: LOGISTIC_POS_PROXY_AUTH_TOKEN
              value: vault:yemeksepeti/data/logo-adapter/Production#LOGISTIC_POS_PROXY_AUTH_TOKEN
            - name: PANDORA_BILLING_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#PANDORA_BILLING_RECEIVER_SQS_QUEUE_URL
            - name: PANDORA_BILLING_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#PANDORA_BILLING_RECEIVER_SQS_REGION
            - name: ETTN_PUBLISHER_SNS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ETTN_PUBLISHER_SNS_REGION
            - name: ETTN_PUBLISHER_SNS_TOPIC_ARN
              value: vault:yemeksepeti/data/logo-adapter/Production#ETTN_PUBLISHER_SNS_TOPIC_ARN
            - name: ETTN_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ETTN_RECEIVER_SQS_QUEUE_URL
            - name: ETTN_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ETTN_RECEIVER_SQS_REGION
            - name: ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
            - name: ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
            - name: SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_REGION
            - name: ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_REGION
            - name: ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_REGION
            - name: ORDER_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_STREAM_RECEIVER_SQS_REGION
            - name: ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_REGION
            - name: EMAIL_SENDER_IDENTITY
              value: vault:yemeksepeti/data/logo-adapter/Production#EMAIL_SENDER_IDENTITY
            - name: EMAIL_SENDER_EMAIL
              value: vault:yemeksepeti/data/logo-adapter/Production#EMAIL_SENDER_EMAIL
            - name: EMAIL_SMTP_SERVER
              value: vault:yemeksepeti/data/logo-adapter/Production#EMAIL_SMTP_SERVER
            - name: EMAIL_SMTP_PORT
              value: vault:yemeksepeti/data/logo-adapter/Production#EMAIL_SMTP_PORT
            - name: EMAIL_SMTP_USER
              value: vault:yemeksepeti/data/logo-adapter/Production#EMAIL_SMTP_USER
            - name: EMAIL_SMTP_PASSWORD
              value: vault:yemeksepeti/data/logo-adapter/Production#EMAIL_SMTP_PASSWORD
            - name: EMAIL_CYCLE_COUNT_APPROVAL_STATUS_TO
              value: vault:yemeksepeti/data/logo-adapter/Production#EMAIL_CYCLE_COUNT_APPROVAL_STATUS_TO
            - name: PRODUCT_RETURN_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#PRODUCT_RETURN_PROJECT_ID
            - name: PRODUCT_RETURN_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#PRODUCT_RETURN_RECEIVER_SUBSCRIPTION_ID
            - name: PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Production#PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_QUEUE_URL
            - name: PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Production#PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_REGION
            - name: BIGQUERY_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#BIGQUERY_PROJECT_ID
            - name: STS_PK
              value: vault:yemeksepeti/data/logo-adapter/Production#STS_PK
            - name: STS_ISSUER
              value: vault:yemeksepeti/data/logo-adapter/Production#STS_ISSUER
            - name: STS_CLIENT_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#STS_CLIENT_ID
            - name: STS_KEY_ID
              value: vault:yemeksepeti/data/logo-adapter/Production#STS_KEY_ID
            - name: STS_VENDOR_API_SCOPE_NAME
              value: vault:yemeksepeti/data/logo-adapter/Production#STS_VENDOR_API_SCOPE_NAME
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/ping
              port: http
          readinessProbe:
            httpGet:
              path: /api/ping
              port: http
          resources:
            limits:
              cpu: 500m
              memory: 800Mi
            requests:
              cpu: 500m
              memory: 800Mi
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: topology.kubernetes.io/zone
                operator: In
                values:
                - me-south-1a
                - me-south-1b
---
# Source: logo-adapter/templates/hpa.yaml
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: logo-adapter-me
  namespace: "logo-adapter"
  labels:
    name: logo-adapter-me    
    helm.sh/chart: logo-adapter
    app.kubernetes.io/name: logo-adapter
    app.kubernetes.io/instance: RELEASE-NAME
    app.kubernetes.io/version: "${ parameters.version }"
    app.kubernetes.io/managed-by: Helm    
    country: "n-a"
    env: "live"
    projectName: "qcommerce-prod"
    region: "me"    
    dh_app: "logo-adapter"
    dh_cc_id: "**********"
    dh_env: "live"
    dh_platform: "qcommerce"
    dh_region: "me-south-1"
    dh_squad: "yemek"
    dh_tribe: "qc-operations"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: logo-adapter-me
  minReplicas: 3
  maxReplicas: 20
  metrics:
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: 80
