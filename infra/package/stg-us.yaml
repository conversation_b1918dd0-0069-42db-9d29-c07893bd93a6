---
# Source: logo-adapter/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-stg-us-01-logo-adapter
  name: logo-adapter
  namespace: logo-adapter
---
# Source: logo-adapter/templates/nlb-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: logo-adapter-us-service
  namespace: "logo-adapter"
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-private-ipv4-addresses: '*************, *************, ************'
    external-dns.alpha.kubernetes.io/hostname: logo-adapter.us.stg.aws.qcommerce.live
    service.beta.kubernetes.io/aws-load-balancer-internal: 'true'
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:us-east-2:************:certificate/e56c8655-8f0c-4ed7-b05e-97ed27e26562
    service.beta.kubernetes.io/aws-load-balancer-scheme: internal
    service.beta.kubernetes.io/aws-load-balancer-type: nlb-ip
  labels:
    name: logo-adapter-us    
    helm.sh/chart: logo-adapter
    app.kubernetes.io/name: logo-adapter
    app.kubernetes.io/instance: RELEASE-NAME
    app.kubernetes.io/version: "${ parameters.version }"
    app.kubernetes.io/managed-by: Helm    
    country: "n-a"
    env: "stg"
    projectName: "qcommerce-non-prod"
    region: "us"    
    dh_app: "logo-adapter"
    dh_cc_id: "**********"
    dh_env: "stg"
    dh_platform: "new-vertical"
    dh_region: "us-east-2"
    dh_squad: "yemek"
    dh_tribe: "qc-commercial"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    protocol: TCP
    targetPort: http
  - name: https
    port: 443
    targetPort: http
  selector:
    name: logo-adapter-us
---
# Source: logo-adapter/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logo-adapter-us
  namespace: "logo-adapter"
  labels:
    name: logo-adapter-us    
    helm.sh/chart: logo-adapter
    app.kubernetes.io/name: logo-adapter
    app.kubernetes.io/instance: RELEASE-NAME
    app.kubernetes.io/version: "${ parameters.version }"
    app.kubernetes.io/managed-by: Helm    
    country: "n-a"
    env: "stg"
    projectName: "qcommerce-non-prod"
    region: "us"    
    dh_app: "logo-adapter"
    dh_cc_id: "**********"
    dh_env: "stg"
    dh_platform: "new-vertical"
    dh_region: "us-east-2"
    dh_squad: "yemek"
    dh_tribe: "qc-commercial"
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      name: logo-adapter-us
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-addr: "https://app.vault.inhouse.dh-darkstores-live.net"
        vault.security.banzaicloud.io/vault-role: "vault-role"
        vault.security.banzaicloud.io/vault-path: eks-stg-us-01
        ad.datadoghq.com/logo-adapter.logs: '[{"source":"logo-adapter","service":"logo-adapter"}]'
      labels:        
        helm.sh/chart: logo-adapter
        app.kubernetes.io/name: logo-adapter
        app.kubernetes.io/instance: RELEASE-NAME
        app.kubernetes.io/version: "${ parameters.version }"
        app.kubernetes.io/managed-by: Helm        
        country: "n-a"
        env: "stg"
        projectName: "qcommerce-non-prod"
        region: "us"        
        dh_app: "logo-adapter"
        dh_cc_id: "**********"
        dh_env: "stg"
        dh_platform: "new-vertical"
        dh_region: "us-east-2"
        dh_squad: "yemek"
        dh_tribe: "qc-commercial"
        name: logo-adapter-us
        version: v1
        tags.datadoghq.com/service: "logo-adapter"
    spec:
      serviceAccountName: logo-adapter
      securityContext:
        {}
      containers:
        - name: logo-adapter
          securityContext:
            {}
          image: "************.dkr.ecr.us-east-2.amazonaws.com/stg-logo-adapter:${ parameters.version }"
          imagePullPolicy: IfNotPresent
          env:
            - name: APP_ENVIRONMENT
              value: Staging
            - name: APP_HOST
              value: vault:yemeksepeti/data/logo-adapter/Staging#APP_HOST
            - name: DATA_FRIDGE_VENDOR_PROXY_AUTH_TOKEN
              value: vault:yemeksepeti/data/logo-adapter/Staging#DATA_FRIDGE_VENDOR_PROXY_AUTH_TOKEN
            - name: DATA_FRIDGE_VENDOR_PROXY_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#DATA_FRIDGE_VENDOR_PROXY_URL
            - name: DC_ADAPTER_PO_INBOUND_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#DC_ADAPTER_PO_INBOUND_RECEIVER_PROJECT_ID
            - name: DC_ADAPTER_PO_INBOUND_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#DC_ADAPTER_PO_INBOUND_RECEIVER_SUBSCRIPTION_ID
            - name: DEFAULT_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Staging#DEFAULT_SA_JSON
            - name: IM_WAREHOUSE_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#IM_WAREHOUSE_RECEIVER_PROJECT_ID
            - name: IM_WAREHOUSE_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#IM_WAREHOUSE_RECEIVER_SUBSCRIPTION_ID
            - name: INVOICE_API_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#INVOICE_API_URL
            - name: JWT_SECRET
              value: vault:yemeksepeti/data/logo-adapter/Staging#JWT_SECRET
            - name: LOGO_PROXY_SECRET
              value: vault:yemeksepeti/data/logo-adapter/Staging#LOGO_PROXY_SECRET
            - name: LOGO_PROXY_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#LOGO_PROXY_URL
            - name: ORDER_FULFILLMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_FULFILLMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_FULFILLMENT_RECEIVER_SQS_REGION
            - name: POSTGRESQL_CONNECTION_STRING
              value: vault:yemeksepeti/data/logo-adapter/Staging#POSTGRESQL_CONNECTION_STRING
            - name: PRODUCT_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#PRODUCT_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: PURCHASE_ORDER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#PURCHASE_ORDER_PROJECT_ID
            - name: PURCHASE_ORDER_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#PURCHASE_ORDER_RECEIVER_SUBSCRIPTION_ID
            - name: PURCHASE_ORDER_REFUND_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#PURCHASE_ORDER_REFUND_PROJECT_ID
            - name: PURCHASE_ORDER_REFUND_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#PURCHASE_ORDER_REFUND_SUBSCRIPTION_ID
            - name: REDIS_ADDRESS
              value: vault:yemeksepeti/data/logo-adapter/Staging#REDIS_ADDRESS
            - name: REDIS_PASSWORD
              value: vault:yemeksepeti/data/logo-adapter/Staging#REDIS_PASSWORD
            - name: SAMPLE_PROTO_PUBLISHER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Staging#SAMPLE_PROTO_PUBLISHER_SA_JSON
            - name: SAMPLE_PROTO_RECEIVER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Staging#SAMPLE_PROTO_RECEIVER_SA_JSON
            - name: SAMPLE_PUBLISHER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Staging#SAMPLE_PUBLISHER_SA_JSON
            - name: SAMPLE_RECEIVER_SA_JSON
              value: vault:yemeksepeti/data/logo-adapter/Staging#SAMPLE_RECEIVER_SA_JSON
            - name: SAMPLE_PUBLISHER_SNS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#SAMPLE_PUBLISHER_SNS_REGION
            - name: SAMPLE_PUBLISHER_SNS_TOPIC_ARN
              value: vault:yemeksepeti/data/logo-adapter/Staging#SAMPLE_PUBLISHER_SNS_TOPIC_ARN
            - name: STORE_MANAGEMENT_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#STORE_MANAGEMENT_RECEIVER_PROJECT_ID
            - name: STORE_MANAGEMENT_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#STORE_MANAGEMENT_RECEIVER_SUBSCRIPTION_ID
            - name: SUPPLIER_PROXY_GRPC_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#SUPPLIER_PROXY_GRPC_URL
            - name: PRODUCT_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#PRODUCT_STREAM_RECEIVER_SQS_REGION
            - name: STORE_STOCK_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#STORE_STOCK_RECEIVER_SQS_REGION
            - name: STORE_STOCK_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#STORE_STOCK_RECEIVER_SQS_QUEUE_URL
            - name: LOGISTIC_POS_PROXY_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#LOGISTIC_POS_PROXY_URL
            - name: LOGISTIC_POS_PROXY_AUTH_TOKEN
              value: vault:yemeksepeti/data/logo-adapter/Staging#LOGISTIC_POS_PROXY_AUTH_TOKEN
            - name: PANDORA_BILLING_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#PANDORA_BILLING_RECEIVER_SQS_QUEUE_URL
            - name: PANDORA_BILLING_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#PANDORA_BILLING_RECEIVER_SQS_REGION
            - name: ETTN_PUBLISHER_SNS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ETTN_PUBLISHER_SNS_REGION
            - name: ETTN_PUBLISHER_SNS_TOPIC_ARN
              value: vault:yemeksepeti/data/logo-adapter/Staging#ETTN_PUBLISHER_SNS_TOPIC_ARN
            - name: ETTN_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ETTN_RECEIVER_SQS_QUEUE_URL
            - name: ETTN_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ETTN_RECEIVER_SQS_REGION
            - name: ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
            - name: ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
            - name: SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_REGION
            - name: ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_REGION
            - name: ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_REGION
            - name: ORDER_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_STREAM_RECEIVER_SQS_REGION
            - name: ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_REGION
            - name: EMAIL_SENDER_IDENTITY
              value: vault:yemeksepeti/data/logo-adapter/Staging#EMAIL_SENDER_IDENTITY
            - name: EMAIL_SENDER_EMAIL
              value: vault:yemeksepeti/data/logo-adapter/Staging#EMAIL_SENDER_EMAIL
            - name: EMAIL_SMTP_SERVER
              value: vault:yemeksepeti/data/logo-adapter/Staging#EMAIL_SMTP_SERVER
            - name: EMAIL_SMTP_PORT
              value: vault:yemeksepeti/data/logo-adapter/Staging#EMAIL_SMTP_PORT
            - name: EMAIL_SMTP_USER
              value: vault:yemeksepeti/data/logo-adapter/Staging#EMAIL_SMTP_USER
            - name: EMAIL_SMTP_PASSWORD
              value: vault:yemeksepeti/data/logo-adapter/Staging#EMAIL_SMTP_PASSWORD
            - name: EMAIL_CYCLE_COUNT_APPROVAL_STATUS_TO
              value: vault:yemeksepeti/data/logo-adapter/Staging#EMAIL_CYCLE_COUNT_APPROVAL_STATUS_TO
            - name: PRODUCT_RETURN_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#PRODUCT_RETURN_PROJECT_ID
            - name: PRODUCT_RETURN_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#PRODUCT_RETURN_RECEIVER_SUBSCRIPTION_ID
            - name: PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/logo-adapter/Staging#PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_QUEUE_URL
            - name: PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/logo-adapter/Staging#PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_REGION
            - name: BIGQUERY_PROJECT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#BIGQUERY_PROJECT_ID
            - name: STS_PK
              value: vault:yemeksepeti/data/logo-adapter/Staging#STS_PK
            - name: STS_ISSUER
              value: vault:yemeksepeti/data/logo-adapter/Staging#STS_ISSUER
            - name: STS_CLIENT_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#STS_CLIENT_ID
            - name: STS_KEY_ID
              value: vault:yemeksepeti/data/logo-adapter/Staging#STS_KEY_ID
            - name: STS_VENDOR_API_SCOPE_NAME
              value: vault:yemeksepeti/data/logo-adapter/Staging#STS_VENDOR_API_SCOPE_NAME
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/ping
              port: http
          readinessProbe:
            httpGet:
              path: /api/ping
              port: http
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 100m
              memory: 128Mi
