# Default values for logo-adapter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 3

certificate_arn: "arn:aws:acm:me-south-1:569581431544:certificate/1c065aac-ca4e-4672-a590-1cd4db0fc485"
static_loadbalancer_ips:
  - *************
  - *************
  - *************

image:
  repository: 569581431544.dkr.ecr.me-south-1.amazonaws.com/live-logo-adapter
  pullPolicy: IfNotPresent


imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  internal_dns: logo-adapter.me.prod.aws.qcommerce.live

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 500m
    memory: 800Mi
  requests:
    cpu: 500m
    memory: 800Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 20
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: topology.kubernetes.io/zone
              operator: In
              values:
                - me-south-1a
                - me-south-1b

tags:
  dh_region: "me-south-1"
  dh_tribe: "qc-operations"
  dh_platform: "qcommerce"
  dh_squad: "yemek"
  dh_env: "live"
  dh_app: "logo-adapter"
  dh_cc_id: "**********"

infra:
  projectName: qcommerce-prod
  env: live
  country: ""
  region: me

irsa:
  arn: arn:aws:iam::569581431544:role/eks-live-me-01-logo-adapter

vault:
  cluster_code: "eks-live-me-01"
  app: "logo-adapter"
  env: "Production"

namespace: "logo-adapter"

istio:
  enabled: false
  gateway: ingressgateway
  dns: logo-adapter.me.prod.aws.qcommerce.live
  jobs:
    injectSidecar: false

healthcheck:
  port: 80
  path: /health

env:
  app_environment: "Production"

