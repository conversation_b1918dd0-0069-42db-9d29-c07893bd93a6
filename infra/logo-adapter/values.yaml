# Default values for logo-adapter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# When istio(istio.enable) is disabled, service are exposed by using an NLB. In this case we have to provide certificate ARN and the
# static IPs (from each private subnets ) that need to be attached with the NLB
certificate_arn: ""
static_loadbalancer_ips: []


replicaCount: 1

image:
  repository: nginx
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: '${ parameters.version }'

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  containerPort: 8080

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 100m
    memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 20
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}


tags:
  dh_region: ""
  dh_tribe: qc-commercial
  dh_platform: new-vertical
  dh_squad: yemek
  dh_env: ""
  dh_app: logo-adapter
  dh_cc_id: "1001060110"

infra:
  projectName: dh-darkstores-live
  env: stg
  country: ""
  region: me

vault:
  cluster_code: ""
  env: "stg"
  region: "us"
  country: ""


namespace: "logo-adapter"

istio:
  enabled: false
  gateway: ingressgateway
  jobs:
    injectSidecar: false

env:
  app_environment: "Staging"
