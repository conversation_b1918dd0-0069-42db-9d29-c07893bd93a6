# Default values for logo-adapter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2

certificate_arn: "arn:aws:acm:us-east-2:839959062732:certificate/e56c8655-8f0c-4ed7-b05e-97ed27e26562"
static_loadbalancer_ips:
  - *************
  - *************
  - ************

image:
  repository: 839959062732.dkr.ecr.us-east-2.amazonaws.com/stg-logo-adapter
  pullPolicy: IfNotPresent

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  internal_dns: logo-adapter.us.stg.aws.qcommerce.live

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 20
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

tags:
  dh_region: "us-east-2"
  dh_tribe: qc-commercial
  dh_platform: new-vertical
  dh_squad: yemek
  dh_env: "stg"
  dh_app: logo-adapter
  dh_cc_id: "**********"

infra:
  projectName: qcommerce-non-prod
  env: stg
  country: ""
  region: us

irsa:
  arn: arn:aws:iam::839959062732:role/eks-stg-us-01-logo-adapter

vault:
  cluster_code: "eks-stg-us-01"
  app: "logo-adapter"
  env: "Staging"

namespace: "logo-adapter"

istio:
  enabled: false
  gateway: ingressgateway
  dns: logo-adapter.us.stg.aws.qcommerce.live
  jobs:
    injectSidecar: false

healthcheck:
  port: 80
  path: /health

env:
  app_environment: "Staging"
