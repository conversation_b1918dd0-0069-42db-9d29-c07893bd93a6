{{- if .Values.istio.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "fullname" . }}-service
  namespace: {{ .Values.namespace | quote }}
  labels:
    name: {{ include "fullname" . }}
    {{- include "labels.default" . | indent 4 }}
    {{- include "labels.infra" . | indent 4 }}
    {{- include "labels.tags" . | indent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    name: {{ template "fullname" . }}
{{- end }}
