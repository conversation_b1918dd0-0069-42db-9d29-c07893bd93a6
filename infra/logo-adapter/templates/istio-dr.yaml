{{- if .Values.istio.enabled }}
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{ include "fullname" . }}-dr
  namespace: {{ .Values.namespace | quote }}
  labels:
    name: {{ include "fullname" . }}-dr
    {{- include "labels.default" . | indent 4 }}
    {{- include "labels.infra" . | indent 4 }}
    {{- include "labels.tags" . | indent 4 }}
spec:
  host: {{ include "fullname" . }}-service.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL

{{- end }}
