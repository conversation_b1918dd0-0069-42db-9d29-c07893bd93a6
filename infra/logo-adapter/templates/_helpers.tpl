{{/*
Expand the name of the chart.
*/}}
{{- define "name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}

{{- define "fullname" -}}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if and .Values.infra.country .Values.infra.region }}
{{- printf "%s-%s-%s" $name .Values.infra.region .Values.infra.country | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- if .Values.infra.region }}
{{- printf "%s-%s" $name  .Values.infra.region | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s"  $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "labels.default" }}
helm.sh/chart: {{ include "name" . }}
{{ include "chart.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: "${ parameters.version }"
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "chart.selectorLabels" -}}
app.kubernetes.io/name: {{ include "name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{- define "labels.infra" }}
{{- range $key, $value := .Values.infra }}
{{ $key }}: {{ default "n-a" $value | quote }}
{{- end -}}
{{- end -}}

{{- define "labels.tags" }}
{{- range $key, $value := .Values.tags }}
{{ $key }}: {{ default "n-a" $value | quote }}
{{- end -}}
{{- end -}}
