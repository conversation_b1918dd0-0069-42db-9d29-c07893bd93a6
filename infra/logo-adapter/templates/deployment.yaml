apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fullname" . }}
  namespace: {{ .Values.namespace | quote }}
  labels:
    name: {{ include "fullname" . }}
    {{- include "labels.default" . | indent 4 }}
    {{- include "labels.infra" . | indent 4 }}
    {{- include "labels.tags" . | indent 4 }}
    version: v1
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      name: {{ include "fullname" . }}
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-addr: "https://app.vault.inhouse.dh-darkstores-live.net"
        vault.security.banzaicloud.io/vault-role: "vault-role"
        vault.security.banzaicloud.io/vault-path: {{ .Values.vault.cluster_code }}
        ad.datadoghq.com/{{ .Chart.Name }}.logs: '[{"source":"{{ template "name" . }}","service":"{{ template "name" . }}"}]'
      labels:
        {{- include "labels.default" . | indent 8 }}
        {{- include "labels.infra" . | indent 8 }}
        {{- include "labels.tags" . | indent 8 }}
        name: {{ include "fullname" . }}
        version: v1
        tags.datadoghq.com/service: "{{ include "name" . }}"
    spec:
      serviceAccountName: {{ include "name" . }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: APP_ENVIRONMENT
              value: {{ .Values.env.app_environment }}
            - name: APP_HOST
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#APP_HOST
            - name: DATA_FRIDGE_VENDOR_PROXY_AUTH_TOKEN
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#DATA_FRIDGE_VENDOR_PROXY_AUTH_TOKEN
            - name: DATA_FRIDGE_VENDOR_PROXY_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#DATA_FRIDGE_VENDOR_PROXY_URL
            - name: DC_ADAPTER_PO_INBOUND_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#DC_ADAPTER_PO_INBOUND_RECEIVER_PROJECT_ID
            - name: DC_ADAPTER_PO_INBOUND_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#DC_ADAPTER_PO_INBOUND_RECEIVER_SUBSCRIPTION_ID
            - name: DEFAULT_SA_JSON
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#DEFAULT_SA_JSON
            - name: IM_WAREHOUSE_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#IM_WAREHOUSE_RECEIVER_PROJECT_ID
            - name: IM_WAREHOUSE_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#IM_WAREHOUSE_RECEIVER_SUBSCRIPTION_ID
            - name: INVOICE_API_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#INVOICE_API_URL
            - name: JWT_SECRET
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#JWT_SECRET
            - name: LOGO_PROXY_SECRET
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#LOGO_PROXY_SECRET
            - name: LOGO_PROXY_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#LOGO_PROXY_URL
            - name: ORDER_FULFILLMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_FULFILLMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_FULFILLMENT_RECEIVER_SQS_REGION
            - name: POSTGRESQL_CONNECTION_STRING
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#POSTGRESQL_CONNECTION_STRING
            - name: PRODUCT_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PRODUCT_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: PURCHASE_ORDER_PROJECT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PURCHASE_ORDER_PROJECT_ID
            - name: PURCHASE_ORDER_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PURCHASE_ORDER_RECEIVER_SUBSCRIPTION_ID
            - name: PURCHASE_ORDER_REFUND_PROJECT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PURCHASE_ORDER_REFUND_PROJECT_ID
            - name: PURCHASE_ORDER_REFUND_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PURCHASE_ORDER_REFUND_SUBSCRIPTION_ID
            - name: REDIS_ADDRESS
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#REDIS_ADDRESS
            - name: REDIS_PASSWORD
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#REDIS_PASSWORD
            - name: SAMPLE_PROTO_PUBLISHER_SA_JSON
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SAMPLE_PROTO_PUBLISHER_SA_JSON
            - name: SAMPLE_PROTO_RECEIVER_SA_JSON
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SAMPLE_PROTO_RECEIVER_SA_JSON
            - name: SAMPLE_PUBLISHER_SA_JSON
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SAMPLE_PUBLISHER_SA_JSON
            - name: SAMPLE_RECEIVER_SA_JSON
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SAMPLE_RECEIVER_SA_JSON
            - name: SAMPLE_PUBLISHER_SNS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SAMPLE_PUBLISHER_SNS_REGION
            - name: SAMPLE_PUBLISHER_SNS_TOPIC_ARN
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SAMPLE_PUBLISHER_SNS_TOPIC_ARN
            - name: STORE_MANAGEMENT_RECEIVER_PROJECT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STORE_MANAGEMENT_RECEIVER_PROJECT_ID
            - name: STORE_MANAGEMENT_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STORE_MANAGEMENT_RECEIVER_SUBSCRIPTION_ID
            - name: SUPPLIER_PROXY_GRPC_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SUPPLIER_PROXY_GRPC_URL
            - name: PRODUCT_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PRODUCT_STREAM_RECEIVER_SQS_REGION
            - name: STORE_STOCK_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STORE_STOCK_RECEIVER_SQS_REGION
            - name: STORE_STOCK_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STORE_STOCK_RECEIVER_SQS_QUEUE_URL
            - name: LOGISTIC_POS_PROXY_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#LOGISTIC_POS_PROXY_URL
            - name: LOGISTIC_POS_PROXY_AUTH_TOKEN
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#LOGISTIC_POS_PROXY_AUTH_TOKEN
            - name: PANDORA_BILLING_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PANDORA_BILLING_RECEIVER_SQS_QUEUE_URL
            - name: PANDORA_BILLING_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PANDORA_BILLING_RECEIVER_SQS_REGION
            - name: ETTN_PUBLISHER_SNS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ETTN_PUBLISHER_SNS_REGION
            - name: ETTN_PUBLISHER_SNS_TOPIC_ARN
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ETTN_PUBLISHER_SNS_TOPIC_ARN
            - name: ETTN_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ETTN_RECEIVER_SQS_QUEUE_URL
            - name: ETTN_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ETTN_RECEIVER_SQS_REGION
            - name: ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
            - name: ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_REGION
            - name: SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_REGION
            - name: SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_QUEUE_URL
            - name: SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_REGION
            - name: ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_REGION
            - name: ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_REGION
            - name: ORDER_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_STREAM_RECEIVER_SQS_REGION
            - name: ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_QUEUE_URL
            - name: ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_REGION  
            - name: EMAIL_SENDER_IDENTITY
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#EMAIL_SENDER_IDENTITY
            - name: EMAIL_SENDER_EMAIL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#EMAIL_SENDER_EMAIL
            - name: EMAIL_SMTP_SERVER
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#EMAIL_SMTP_SERVER
            - name: EMAIL_SMTP_PORT
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#EMAIL_SMTP_PORT
            - name: EMAIL_SMTP_USER
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#EMAIL_SMTP_USER
            - name: EMAIL_SMTP_PASSWORD
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#EMAIL_SMTP_PASSWORD
            - name: EMAIL_CYCLE_COUNT_APPROVAL_STATUS_TO
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#EMAIL_CYCLE_COUNT_APPROVAL_STATUS_TO
            - name: PRODUCT_RETURN_PROJECT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PRODUCT_RETURN_PROJECT_ID
            - name: PRODUCT_RETURN_RECEIVER_SUBSCRIPTION_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PRODUCT_RETURN_RECEIVER_SUBSCRIPTION_ID
            - name: PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_QUEUE_URL
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_QUEUE_URL
            - name: PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_REGION
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_REGION
            - name: BIGQUERY_PROJECT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#BIGQUERY_PROJECT_ID
            - name: STS_PK
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STS_PK
            - name: STS_ISSUER
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STS_ISSUER
            - name: STS_CLIENT_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STS_CLIENT_ID
            - name: STS_KEY_ID
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STS_KEY_ID
            - name: STS_VENDOR_API_SCOPE_NAME
              value: vault:yemeksepeti/data/{{ .Values.vault.app }}/{{ .Values.vault.env }}#STS_VENDOR_API_SCOPE_NAME
          ports:
            - name: http
              containerPort: {{ .Values.service.containerPort }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/ping
              port: http
          readinessProbe:
            httpGet:
              path: /api/ping
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
