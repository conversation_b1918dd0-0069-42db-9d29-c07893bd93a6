{{- if .Values.istio.enabled }}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "fullname" . }}-vs
  namespace: {{ .Values.namespace | quote }}
  labels:
    name: {{ include "fullname" . }}-vs
    {{- include "labels.default" . | indent 4 }}
    {{- include "labels.infra" . | indent 4 }}
    {{- include "labels.tags" . | indent 4 }}
spec:
  hosts:
  - {{ .Values.istio.dns }}
  gateways:
  - istio-system/cluster-default-gw
  http:
  - match:
    - uri:
        prefix: /api/ping
    route:
    - destination:
        host: {{ include "fullname" . }}-service.{{ .Values.namespace }}.svc.cluster.local
        port:
          number: {{ .Values.service.port }}
{{- end }}
