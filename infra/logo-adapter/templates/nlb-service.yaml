{{- if not .Values.istio.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "fullname" . }}-service
  namespace: {{ .Values.namespace | quote }}
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-private-ipv4-addresses: '{{- join  ", "  .Values.static_loadbalancer_ips }}'
    external-dns.alpha.kubernetes.io/hostname: {{ .Values.service.internal_dns }}
    service.beta.kubernetes.io/aws-load-balancer-internal: 'true'
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: {{ .Values.certificate_arn }}
    service.beta.kubernetes.io/aws-load-balancer-scheme: internal
    service.beta.kubernetes.io/aws-load-balancer-type: nlb-ip
  labels:
    name: {{ include "fullname" . }}
    {{- include "labels.default" . | indent 4 }}
    {{- include "labels.infra" . | indent 4 }}
    {{- include "labels.tags" . | indent 4 }}
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    protocol: TCP
    targetPort: http
  - name: https
    port: 443
    targetPort: http
  selector:
    name: {{ template "fullname" . }}

  {{- end }}