package sts

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"regexp"
	"strings"

	sts "github.com/deliveryhero/pd-sts-go-sdk"
)

func GetNewTokenService(stsPrivateKey, issuer, clientID, keyID, scopeName string) (sts.TokenService, error) {
	privateKey := formatKey(stsPrivateKey)
	block, _ := pem.Decode([]byte(privateKey))

	if block != nil {
		key, parseErr := x509.ParsePKCS1PrivateKey(block.Bytes)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to parse private key: %v", parseErr)
		}
		tokenService := sts.NewTokenService(issuer, clientID, keyID, key)
		return tokenService, nil
	}
	return nil, nil
}

func GetToken(tokenService sts.TokenService, scopeName string) (string, error) {
	if tokenService == nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to get sts token because tokenService is nil")
	}
	accessToken, err := tokenService.GetToken([]string{scopeName})
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("Bearer %s", accessToken), nil
}

func formatKey(key string) string {
	data := fixPEM([]byte(key))

	return strings.TrimSpace(string(data))
}

var rePEMOneliner = regexp.MustCompile(`(-----BEGIN RSA PRIVATE KEY-----)( *?)([^ ][^\n]+[^ ])( *?)(-----END RSA PRIVATE KEY-----)`)

// fixPEM is for fixing pem-files.
// Infra could provide it as a one-liner.
func fixPEM(data []byte) []byte {
	matches := rePEMOneliner.FindSubmatch(data)
	if len(matches) > 0 {
		b := make([]byte, 0, len(data)+2)
		b = append(b, matches[1]...)
		b = append(b, '\n')
		b = append(b, matches[3]...)
		b = append(b, '\n')
		b = append(b, matches[5]...)
		return b
	}
	return data
}
