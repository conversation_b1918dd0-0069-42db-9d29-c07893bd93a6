// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/util/cacher/cacher.go

// Package cacher is a generated GoMock package.
package cacher

import (
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// Mock<PERSON>acher is a mock of ICacher interface.
type MockICacher struct {
	ctrl     *gomock.Controller
	recorder *MockICacherMockRecorder
}

// MockICacherMockRecorder is the mock recorder for MockICacher.
type MockICacherMockRecorder struct {
	mock *MockICacher
}

// New<PERSON>ock<PERSON>acher creates a new mock instance.
func NewMockICacher(ctrl *gomock.Controller) *<PERSON><PERSON><PERSON><PERSON> {
	mock := &MockICacher{ctrl: ctrl}
	mock.recorder = &MockICacherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICacher) EXPECT() *MockICacherMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *<PERSON><PERSON><PERSON><PERSON>) AcquireLock(lockKey string, expiration time.Duration) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", lockKey, expiration)
	ret0, _ := ret[0].(bool)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockICacherMockRecorder) AcquireLock(lockKey, expiration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockICacher)(nil).AcquireLock), lockKey, expiration)
}

// Delete mocks base method.
func (m *MockICacher) Delete(key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", key)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockICacherMockRecorder) Delete(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockICacher)(nil).Delete), key)
}

// Get mocks base method.
func (m *MockICacher) Get(key string) *string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", key)
	ret0, _ := ret[0].(*string)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockICacherMockRecorder) Get(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockICacher)(nil).Get), key)
}

// Ping mocks base method.
func (m *MockICacher) Ping() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping")
	ret0, _ := ret[0].(error)
	return ret0
}

// Ping indicates an expected call of Ping.
func (mr *MockICacherMockRecorder) Ping() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockICacher)(nil).Ping))
}

// ReleaseLock mocks base method.
func (m *MockICacher) ReleaseLock(lockKey string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseLock", lockKey)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockICacherMockRecorder) ReleaseLock(lockKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockICacher)(nil).ReleaseLock), lockKey)
}

// Set mocks base method.
func (m *MockICacher) Set(key string, value interface{}, duration time.Duration) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Set", key, value, duration)
}

// Set indicates an expected call of Set.
func (mr *MockICacherMockRecorder) Set(key, value, duration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockICacher)(nil).Set), key, value, duration)
}

// SetTime mocks base method.
func (m *MockICacher) SetTime(key string, value time.Time, duration time.Duration) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTime", key, value, duration)
}

// SetTime indicates an expected call of SetTime.
func (mr *MockICacherMockRecorder) SetTime(key, value, duration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTime", reflect.TypeOf((*MockICacher)(nil).SetTime), key, value, duration)
}
