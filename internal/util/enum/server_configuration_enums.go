package enum

const (
	IsSqsStoreStockReceiverOpen                     string = "IS_SQS_STORE_STOCK_RECEIVER_OPEN"
	IsSqsProductStreamReceiverOpen                         = "IS_SQS_PRODUCT_STREAM_RECEIVER_OPEN"
	IsSqsOrderRefundReceiverOpen                           = "IS_SQS_ORDER_REFUND_RECEIVER_OPEN"
	IsSqsSampleReceiverOpen                                = "IS_SQS_SAMPLE_RECEIVER_OPEN"
	IsSqsOrderFulfillmentOpen                              = "IS_SQS_ORDER_FULFILLMENT_RECEIVER_OPEN"
	IsSqsPandoraBillingReceiverOpen                        = "IS_SQS_PANDORA_BILLING_RECEIVER_OPEN"
	IsPubDcAdapterPoInboundReceiverOpen                    = "IS_PUB_DC_ADAPTER_PO_INBOUND_RECEIVER_OPEN"
	IsPubImWarehouseReceiverOpen                           = "IS_PUB_IM_WAREHOUSE_RECEIVER_OPEN"
	IsPubPurchaseOrderReceiverOpen                         = "IS_PUB_PURCHASE_ORDER_RECEIVER_OPEN"
	IsPubPurchaseOrderRefundReceiverOpen                   = "IS_PUB_PURCHASE_ORDER_REFUND_RECEIVER_OPEN"
	IsPubSampleProtoReceiverOpen                           = "IS_PUB_SAMPLE_PROTO_RECEIVER_OPEN"
	IsPubSampleReceiverOpen                                = "IS_PUB_SAMPLE_RECEIVER_OPEN"
	IsPubStoreManagementReceiverOpen                       = "IS_PUB_STORE_MANAGEMENT_RECEIVER_OPEN"
	ProductStreamCacheDuration                             = "PRODUCT_STREAM_CACHE_DURATION"
	PaymentMethodMaps                                      = "PAYMENT_METHOD_MAPS"
	IsSqsEttnReceiverOpen                                  = "IS_SQS_ETTN_RECEIVER_OPEN"
	IsSqsOrderOfflinePaymentReceiverOpen                   = "IS_SQS_ORDER_OFFLINE_PAYMENT_RECEIVER_OPEN"
	IsSqsOrderRefundOfflinePaymentReceiverOpen             = "IS_SQS_ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_OPEN"
	IsSqsSalesforceCreateAccountOpen                       = "IS_SQS_SALESFORCE_CREATE_ACCOUNT_RECEIVER_OPEN"
	IsSqsSalesforceUpdateAccountOpen                       = "IS_SQS_SALESFORCE_UPDATE_ACCOUNT_RECEIVER_OPEN"
	IsSqsSalesforceUpdateContactOpen                       = "IS_SQS_SALESFORCE_UPDATE_CONTACT_RECEIVER_OPEN"
	IsSqsSalesforceUpdateAddressOpen                       = "IS_SQS_SALESFORCE_UPDATE_ADDRESS_RECEIVER_OPEN"
	IsSqsOrderFulfillmentCancellationOpen                  = "IS_SQS_ORDER_FULFILLMENT_CANCELLATION_RECEIVER_OPEN"
	IsSqsOrderFulfillmentReconciliationReceiverOpen        = "IS_SQS_ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_OPEN"
	IsSqsOrderStreamReceiverOpen                           = "IS_SQS_ORDER_STREAM_RECEIVER_OPEN"
	IsSqsOrderStatusCancellationStreamReceiverOpen         = "IS_SQS_ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_OPEN"
	IsSendWarehouseStockCountEmailJobOpen                  = "IS_SEND_WAREHOUSE_STOCK_COUNT_EMAIL_JOB_OPEN"
	IsSendWarehouseStockCountToLogoJobOpen                 = "IS_SEND_WAREHOUSE_STOCK_COUNT_TO_LOGO_JOB_OPEN"
	IsSendPartialRefundOrdersToLogoJobOpen                 = "IS_SEND_PARTIAL_REFUND_ORDERS_TO_LOGO_JOB_OPEN"
	LogoStockCountProcessTime                              = "logo-stock-count-process-time-minute"
	CoffeeProductSKUs                                      = "coffee-product-skus"
	CoffeeMaterialSKUs                                     = "coffee-material-skus"
	IsPubProductReturnReceiverOpen                         = "IS_PUB_PRODUCT_RETURN_RECEIVER_OPEN"
	IsSqsPandoraBillingSoftposReceiverOpen                 = "IS_SQS_PANDORA_BILLING_SOFTPOS_RECEIVER_OPEN"
)

// Data Types
const (
	Integer  = 1
	Text     = 2
	Datetime = 3
	Bool     = 4
	Double   = 5
)
