package enum

const (
	PaymentTypeOnline  int = 1
	PaymentTypeOffline int = 2
	PaymentTypeOther   int = 3
)
const (
	PaymentGroupTypeFull           int = 1
	PaymentGroupTypeCreditCardOnly int = 2
)

const (
	PaymentOperationTypeOrder      int = 1
	PaymentOperationTypeRefund     int = 2
	PaymentOperationTypeAdditional int = 3
)

const (
	OrderTransactionTypeCancelled int = 1
)

func PaymentOperationTypeGetName(typeId int) string {
	switch typeId {
	case 1:
		return "Order"
	case 2:
		return "Refund"
	case 3:
		return "Additional"
	default:
		return ""
	}
}

const (
	PaymentMethodOnlineCreditCard int = 1
	PaymentMethodCreditCard       int = 2
	PaymentMethodCash             int = 3
	PaymentMethodOther            int = 4
)

const (
	PaymentStatusPending                  int = 1
	PaymentStatusWaitingForApproval       int = 2
	PaymentStatusCompleted                int = 3
	PaymentStatusWaitingForReconciliation int = 4
)
