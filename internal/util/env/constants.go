package env

// Application
const (
	AppEnvironment = "APP_ENVIRONMENT"
	AppName        = "APP_NAME"
	AppHost        = "APP_HOST"
)

// Jwt
const JwtSecret = "JWT_SECRET"

// Database
const PostgresqlConnectionString = "POSTGRESQL_CONNECTION_STRING"

// Redis
const (
	RedisAddress  = "REDIS_ADDRESS"
	RedisPassword = "REDIS_PASSWORD"
)

// PubSub
const (
	SamplePublisherSaJson                               = "SAMPLE_PUBLISHER_SA_JSON"
	SamplePublisherProjectId                            = "SAMPLE_PUBLISHER_PROJECT_ID"
	SamplePublisherTopicId                              = "SAMPLE_PUBLISHER_TOPIC_ID"
	SampleReceiverSaJson                                = "SAMPLE_RECEIVER_SA_JSON"
	SampleReceiverProjectId                             = "SAMPLE_RECEIVER_PROJECT_ID"
	SampleReceiverSubscriptionId                        = "SAMPLE_RECEIVER_SUBSCRIPTION_ID"
	SampleProtoPublisherSaJson                          = "SAMPLE_PROTO_PUBLISHER_SA_JSON"
	SampleProtoPublisherProjectId                       = "SAMPLE_PROTO_PUBLISHER_PROJECT_ID"
	SampleProtoPublisherTopicId                         = "SAMPLE_PROTO_PUBLISHER_TOPIC_ID"
	SampleProtoReceiverSaJson                           = "SAMPLE_PROTO_RECEIVER_SA_JSON"
	SampleProtoReceiverProjectId                        = "SAMPLE_PROTO_RECEIVER_PROJECT_ID"
	SampleProtoReceiverSubscriptionId                   = "SAMPLE_PROTO_RECEIVER_SUBSCRIPTION_ID"
	DefaultSaJson                                       = "DEFAULT_SA_JSON"
	PurchaseOrderProjectId                              = "PURCHASE_ORDER_PROJECT_ID"
	PurchaseOrderReceiverSubscriptionId                 = "PURCHASE_ORDER_RECEIVER_SUBSCRIPTION_ID"
	PurchaseOrderRefundProjectId                        = "PURCHASE_ORDER_REFUND_PROJECT_ID"
	PurchaseOrderRefundReceiverSubscriptionId           = "PURCHASE_ORDER_REFUND_SUBSCRIPTION_ID"
	StoreManagementReceiverProjectId                    = "STORE_MANAGEMENT_RECEIVER_PROJECT_ID"
	StoreManagementReceiverSubscriptionId               = "STORE_MANAGEMENT_RECEIVER_SUBSCRIPTION_ID"
	DcAdapterPurchaseOrderInboundReceiverProjectId      = "DC_ADAPTER_PO_INBOUND_RECEIVER_PROJECT_ID"
	DcAdapterPurchaseOrderInboundReceiverSubscriptionId = "DC_ADAPTER_PO_INBOUND_RECEIVER_SUBSCRIPTION_ID"
	ImWarehouseReceiverProjectId                        = "IM_WAREHOUSE_RECEIVER_PROJECT_ID"
	ImWarehouseReceiverSubscriptionId                   = "IM_WAREHOUSE_RECEIVER_SUBSCRIPTION_ID"
	ProductReturnProjectId                              = "PRODUCT_RETURN_PROJECT_ID"
	ProductReturnReceiverSubscriptionId                 = "PRODUCT_RETURN_RECEIVER_SUBSCRIPTION_ID"
	BigQueryProjectId                                   = "BIGQUERY_PROJECT_ID"
)

// Sqs
const (
	SamplePublisherSqsQueueUrl                        = "SAMPLE_PUBLISHER_SQS_QUEUE_URL"
	SamplePublisherSqsRegion                          = "SAMPLE_PUBLISHER_SQS_REGION"
	SampleReceiverSqsQueueUrl                         = "SAMPLE_RECEIVER_SQS_QUEUE_URL"
	SampleReceiverSqsRegion                           = "SAMPLE_RECEIVER_SQS_REGION"
	ProductStreamReceiverSqsQueueUrl                  = "PRODUCT_STREAM_RECEIVER_SQS_QUEUE_URL"
	ProductStreamReceiverSqsRegion                    = "PRODUCT_STREAM_RECEIVER_SQS_REGION"
	StoreStockReceiverSqsRegion                       = "STORE_STOCK_RECEIVER_SQS_REGION"
	StoreStockReceiverSqsQueueUrl                     = "STORE_STOCK_RECEIVER_SQS_QUEUE_URL"
	OrderFulfillmentReceiverSqsQueueUrl               = "ORDER_FULFILLMENT_RECEIVER_SQS_QUEUE_URL"
	OrderFulfillmentReceiverSqsRegion                 = "ORDER_FULFILLMENT_RECEIVER_SQS_REGION"
	PandoraBillingReceiverSqsQueueUrl                 = "PANDORA_BILLING_RECEIVER_SQS_QUEUE_URL"
	PandoraBillingReceiverSqsRegion                   = "PANDORA_BILLING_RECEIVER_SQS_REGION"
	EttnReceiverSqsQueueUrl                           = "ETTN_RECEIVER_SQS_QUEUE_URL"
	EttnReceiverSqsRegion                             = "ETTN_RECEIVER_SQS_REGION"
	OrderOfflinePaymentReceiverSqsQueueUrl            = "ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL"
	OrderOfflinePaymentReceiverSqsRegion              = "ORDER_OFFLINE_PAYMENT_RECEIVER_SQS_REGION"
	OrderRefundOfflinePaymentReceiverSqsQueueUrl      = "ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_QUEUE_URL"
	OrderRefundOfflinePaymentReceiverSqsRegion        = "ORDER_REFUND_OFFLINE_PAYMENT_RECEIVER_SQS_REGION"
	SalesforceCreateAccountReceiverSqsQueueUrl        = "SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL"
	SalesforceCreateAccountReceiverSqsRegion          = "SALESFORCE_CREATE_ACCOUNT_RECEIVER_SQS_REGION"
	SalesforceUpdateAccountReceiverSqsQueueUrl        = "SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_QUEUE_URL"
	SalesforceUpdateAccountReceiverSqsRegion          = "SALESFORCE_UPDATE_ACCOUNT_RECEIVER_SQS_REGION"
	SalesforceUpdateContactReceiverSqsQueueUrl        = "SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_QUEUE_URL"
	SalesforceUpdateContactReceiverSqsRegion          = "SALESFORCE_UPDATE_CONTACT_RECEIVER_SQS_REGION"
	SalesforceUpdateAddressReceiverSqsQueueUrl        = "SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_QUEUE_URL"
	SalesforceUpdateAddressReceiverSqsRegion          = "SALESFORCE_UPDATE_ADDRESS_RECEIVER_SQS_REGION"
	OrderFulfillmentCancellationReceiverSqsQueueUrl   = "ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_QUEUE_URL"
	OrderFulfillmentCancellationReceiverSqsRegion     = "ORDER_FULFILLMENT_CANCELLATION_RECEIVER_SQS_REGION"
	OrderFulfillmentReconciliationReceiverSqsQueueUrl = "ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_QUEUE_URL"
	OrderFulfillmentReconciliationReceiverSqsRegion   = "ORDER_FULFILLMENT_RECONCILIATION_RECEIVER_SQS_REGION"
	OrderStreamReceiverSqsQueueUrl                    = "ORDER_STREAM_RECEIVER_SQS_QUEUE_URL"
	OrderStreamReceiverSqsRegion                      = "ORDER_STREAM_RECEIVER_SQS_REGION"
	OrderStatusCancellationStreamReceiverSqsQueueUrl  = "ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_QUEUE_URL"
	OrderStatusCancellationStreamReceiverSqsRegion    = "ORDER_STATUS_CANCELLATION_STREAM_RECEIVER_SQS_REGION"
	PandoraBillingSoftposReceiverSqsQueueUrl          = "PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_QUEUE_URL"
	PandoraBillingSoftposReceiverSqsRegion            = "PANDORA_BILLING_SOFTPOS_RECEIVER_SQS_REGION"
)

// Sns

const (
	SamplePublisherSnsRegion   = "SAMPLE_PUBLISHER_SNS_REGION"
	SamplePublisherSnsTopicArn = "SAMPLE_PUBLISHER_SNS_TOPIC_ARN"
	EttnPublisherSnsRegion     = "ETTN_PUBLISHER_SNS_REGION"
	EttnPublisherSnsTopicArn   = "ETTN_PUBLISHER_SNS_TOPIC_ARN"
)

// Proxy
const (
	SampleProxyUrl                 = "SAMPLE_PROXY_URL"
	SampleProxyTimeout             = "SAMPLE_PROXY_TIMEOUT"
	LogoProxyUrl                   = "LOGO_PROXY_URL"
	LogoProxyTimeout               = "LOGO_PROXY_TIMEOUT"
	LogoProxySecret                = "LOGO_PROXY_SECRET"
	SupplierProxyGrpcUrl           = "SUPPLIER_PROXY_GRPC_URL"
	SupplierProxyGrpcTimeout       = "SUPPLIER_PROXY_GRPC_TIMEOUT"
	InvoiceApiUrl                  = "INVOICE_API_URL"
	DataFridgeVendorProxyUrl       = "DATA_FRIDGE_VENDOR_PROXY_URL"
	DataFridgeVendorProxyTimeout   = "DATA_FRIDGE_VENDOR_PROXY_TIMEOUT"
	DataFridgeVendorProxyAuthToken = "DATA_FRIDGE_VENDOR_PROXY_AUTH_TOKEN"
	LogisticPosProxyUrl            = "LOGISTIC_POS_PROXY_URL"
	LogisticPosProxyTimeout        = "LOGISTIC_POS_PROXY_TIMEOUT"
	LogisticPosProxyAuthToken      = "LOGISTIC_POS_PROXY_AUTH_TOKEN"
)

// Sts
const (
	StsPK                 = "STS_PK"
	StsIssuer             = "STS_ISSUER"
	StsClientId           = "STS_CLIENT_ID"
	StsKeyId              = "STS_KEY_ID"
	StsVendorApiScopeName = "STS_VENDOR_API_SCOPE_NAME"
)
