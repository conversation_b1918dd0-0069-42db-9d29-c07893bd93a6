// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/util/env/environment.go

// Package env is a generated GoMock package.
package env

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIEnvironment is a mock of IEnvironment interface.
type MockIEnvironment struct {
	ctrl     *gomock.Controller
	recorder *MockIEnvironmentMockRecorder
}

// MockIEnvironmentMockRecorder is the mock recorder for MockIEnvironment.
type MockIEnvironmentMockRecorder struct {
	mock *MockIEnvironment
}

// NewMockIEnvironment creates a new mock instance.
func NewMockIEnvironment(ctrl *gomock.Controller) *MockIEnvironment {
	mock := &MockIEnvironment{ctrl: ctrl}
	mock.recorder = &MockIEnvironmentMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIEnvironment) EXPECT() *MockIEnvironmentMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockIEnvironment) Get(key string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", key)
	ret0, _ := ret[0].(string)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockIEnvironmentMockRecorder) Get(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockIEnvironment)(nil).Get), key)
}

// GetHostname mocks base method.
func (m *MockIEnvironment) GetHostname() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHostname")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHostname indicates an expected call of GetHostname.
func (mr *MockIEnvironmentMockRecorder) GetHostname() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHostname", reflect.TypeOf((*MockIEnvironment)(nil).GetHostname))
}

// Init mocks base method.
func (m *MockIEnvironment) Init() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Init")
}

// Init indicates an expected call of Init.
func (mr *MockIEnvironmentMockRecorder) Init() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockIEnvironment)(nil).Init))
}

// Set mocks base method.
func (m *MockIEnvironment) Set(key, value string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", key, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockIEnvironmentMockRecorder) Set(key, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockIEnvironment)(nil).Set), key, value)
}
