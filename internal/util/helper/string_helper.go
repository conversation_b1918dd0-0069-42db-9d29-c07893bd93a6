package helper

import (
	"strings"
	"time"
)

func ToTimeFormat(s string) (time.Time, error) {
	//split if date string include timezone
	splitTimezone := strings.SplitN(s, "Z", 2)
	timeString := strings.SplitN(splitTimezone[0], "z", 2)

	timeLayout := "2006-01-02T15:04:05"

	loc, err := time.LoadLocation("Europe/Istanbul")
	if err != nil {
		return time.Time{}, err
	}

	formattedTime, err := time.ParseInLocation(timeLayout, timeString[0], loc)
	if err != nil {
		return time.Time{}, err
	}

	return formattedTime, nil
}

// Contains checks if a string is present in a slice
func Contains(s []string, str string) bool {
	for _, v := range s {
		if v == str {
			return true
		}
	}
	return false
}
