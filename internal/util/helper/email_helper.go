package helper

import (
	"errors"
	"net/mail"
	"os"
	"strconv"

	"github.com/matcornic/hermes/v2"
	"gopkg.in/gomail.v2"
)

type smtpAuthentication struct {
	Server         string
	Port           int
	SenderEmail    string
	SenderIdentity string
	SMTPUser       string
	SMTPPassword   string
}

func SendMail(email hermes.Email, to string, subject string, attachFile string) error {

	h := hermes.Hermes{
		Product: hermes.Product{
			Name:      "Local Support",
			Logo:      "https://images.deliveryhero.io/image/darkstores/logo/tr/yemek-banabi-logo-red.png",
			Copyright: "Copyright © 2022 Local Support. All rights reserved.",
		},
	}
	h.Theme = &hermes.Default{}

	// Generate an HTML email with the provided contents (for modern clients)
	emailBody, err := h.GenerateHTML(email)
	if err != nil {
		return err
	}

	// Generate the plaintext version of the e-mail (for clients that do not support xHTML)
	emailText, err := h.GeneratePlainText(email)
	if err != nil {
		return err
	}

	port, _ := strconv.Atoi(os.Getenv("EMAIL_SMTP_PORT"))

	smtpConfig := smtpAuthentication{
		Server:         os.Getenv("EMAIL_SMTP_SERVER"),
		Port:           port,
		SenderEmail:    os.Getenv("EMAIL_SENDER_EMAIL"),
		SenderIdentity: os.Getenv("EMAIL_SENDER_IDENTITY"),
		SMTPPassword:   os.Getenv("EMAIL_SMTP_PASSWORD"),
		SMTPUser:       os.Getenv("EMAIL_SMTP_USER"),
	}

	err = send(smtpConfig, to, subject, emailBody, emailText, attachFile)
	if err != nil {
		return err
	}

	return nil
}

func send(smtpConfig smtpAuthentication, to string, subject string, htmlBody string, txtBody string, attachFile string) error {

	if smtpConfig.Server == "" {
		return errors.New("SMTP server config is empty")
	}
	if smtpConfig.Port == 0 {
		return errors.New("SMTP port config is empty")
	}

	if smtpConfig.SMTPUser == "" {
		return errors.New("SMTP user is empty")
	}

	if smtpConfig.SenderIdentity == "" {
		return errors.New("SMTP sender identity is empty")
	}

	if smtpConfig.SenderEmail == "" {
		return errors.New("SMTP sender email is empty")
	}

	if to == "" {
		return errors.New("no receiver emails configured")
	}

	from := mail.Address{
		Name:    smtpConfig.SenderIdentity,
		Address: smtpConfig.SenderEmail,
	}

	m := gomail.NewMessage()
	m.SetHeader("From", from.String())
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)

	m.SetBody("text/plain", txtBody)
	m.AddAlternative("text/html", htmlBody)

	if attachFile != "" {
		m.Attach(attachFile)
	}

	d := gomail.NewDialer(smtpConfig.Server, smtpConfig.Port, smtpConfig.SMTPUser, smtpConfig.SMTPPassword)

	return d.DialAndSend(m)
}
