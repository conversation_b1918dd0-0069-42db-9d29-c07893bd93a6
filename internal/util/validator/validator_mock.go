// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/util/validator/validator.go

// Package validator is a generated GoMock package.
package validator

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIValidator is a mock of IValidator interface.
type MockIValidator struct {
	ctrl     *gomock.Controller
	recorder *MockIValidatorMockRecorder
}

// MockIValidatorMockRecorder is the mock recorder for MockIValidator.
type MockIValidatorMockRecorder struct {
	mock *MockIValidator
}

// NewMockIValidator creates a new mock instance.
func NewMockIValidator(ctrl *gomock.Controller) *MockIValidator {
	mock := &MockIValidator{ctrl: ctrl}
	mock.recorder = &MockIValidatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIValidator) EXPECT() *MockIValidatorMockRecorder {
	return m.recorder
}

// RegisterStructLevelValidation mocks base method.
func (m_2 *MockIValidator) RegisterStructLevelValidation(f func(StructLevelType), m interface{}) {
	m_2.ctrl.T.Helper()
	m_2.ctrl.Call(m_2, "RegisterStructLevelValidation", f, m)
}

// RegisterStructLevelValidation indicates an expected call of RegisterStructLevelValidation.
func (mr *MockIValidatorMockRecorder) RegisterStructLevelValidation(f, m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterStructLevelValidation", reflect.TypeOf((*MockIValidator)(nil).RegisterStructLevelValidation), f, m)
}

// ValidateStruct mocks base method.
func (m *MockIValidator) ValidateStruct(s interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateStruct", s)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateStruct indicates an expected call of ValidateStruct.
func (mr *MockIValidatorMockRecorder) ValidateStruct(s interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateStruct", reflect.TypeOf((*MockIValidator)(nil).ValidateStruct), s)
}
