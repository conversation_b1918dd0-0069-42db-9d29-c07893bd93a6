// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v2/controller/sample/sample_controller.go

// Package sample is a generated GoMock package.
package sample

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockISampleController is a mock of ISampleController interface.
type MockISampleController struct {
	ctrl     *gomock.Controller
	recorder *MockISampleControllerMockRecorder
}

// MockISampleControllerMockRecorder is the mock recorder for MockISampleController.
type MockISampleControllerMockRecorder struct {
	mock *MockISampleController
}

// NewMockISampleController creates a new mock instance.
func NewMockISampleController(ctrl *gomock.Controller) *MockISampleController {
	mock := &MockISampleController{ctrl: ctrl}
	mock.recorder = &MockISampleControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISampleController) EXPECT() *MockISampleControllerMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockISampleController) Get(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Get", context)
}

// Get indicates an expected call of Get.
func (mr *MockISampleControllerMockRecorder) Get(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockISampleController)(nil).Get), context)
}

// RegisterRoutes mocks base method.
func (m *MockISampleController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockISampleControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockISampleController)(nil).RegisterRoutes), routerGroup)
}
