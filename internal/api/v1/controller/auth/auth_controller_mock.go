// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v1/controller/auth/auth_controller.go

// Package auth is a generated GoMock package.
package auth

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockIAuthController is a mock of IAuthController interface.
type MockIAuthController struct {
	ctrl     *gomock.Controller
	recorder *MockIAuthControllerMockRecorder
}

// MockIAuthControllerMockRecorder is the mock recorder for MockIAuthController.
type MockIAuthControllerMockRecorder struct {
	mock *MockIAuthController
}

// NewMockIAuthController creates a new mock instance.
func NewMockIAuthController(ctrl *gomock.Controller) *MockIAuthController {
	mock := &MockIAuthController{ctrl: ctrl}
	mock.recorder = &MockIAuthControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAuthController) EXPECT() *MockIAuthControllerMockRecorder {
	return m.recorder
}

// GetAccessToken mocks base method.
func (m *MockIAuthController) GetAccessToken(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetAccessToken", context)
}

// GetAccessToken indicates an expected call of GetAccessToken.
func (mr *MockIAuthControllerMockRecorder) GetAccessToken(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccessToken", reflect.TypeOf((*MockIAuthController)(nil).GetAccessToken), context)
}

// GetProgrammaticAccessToken mocks base method.
func (m *MockIAuthController) GetProgrammaticAccessToken(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProgrammaticAccessToken", context)
}

// GetProgrammaticAccessToken indicates an expected call of GetProgrammaticAccessToken.
func (mr *MockIAuthControllerMockRecorder) GetProgrammaticAccessToken(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProgrammaticAccessToken", reflect.TypeOf((*MockIAuthController)(nil).GetProgrammaticAccessToken), context)
}

// Login mocks base method.
func (m *MockIAuthController) Login(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Login", context)
}

// Login indicates an expected call of Login.
func (mr *MockIAuthControllerMockRecorder) Login(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockIAuthController)(nil).Login), context)
}

// RegisterRoutes mocks base method.
func (m *MockIAuthController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockIAuthControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockIAuthController)(nil).RegisterRoutes), routerGroup)
}
