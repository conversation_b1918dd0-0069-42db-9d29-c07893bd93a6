package product

import (
	"logo-adapter/internal/api"
	"logo-adapter/internal/service/product"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"net/http"

	"github.com/gin-gonic/gin"
)

type IProductController interface {
	RegisterRoutes(routerGroup *gin.RouterGroup)
	GetAllProducts(context *gin.Context)
}

type ProductController struct {
	path           string
	environment    env.IEnvironment
	loggr          logger.ILogger
	validatr       validator.IValidator
	cachr          cacher.ICacher
	productService product.IProductService
}

// NewProductController
// Returns a new ProductController.
func NewProductController(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	productService product.IProductService,
) IProductController {
	controller := ProductController{
		path:        "product",
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if productService != nil {
		controller.productService = productService
	} else {
		controller.productService = product.New(environment, loggr, validatr, cachr, nil, nil, nil)
	}
	return &controller
}

// RegisterRoutes
// Registers routes to gin.
func (c *ProductController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	routes := routerGroup.Group(c.path)
	routes.Use(api.AuthenticationMiddleware(c.environment))
	routes.GET("all-products", c.GetAllProducts)
}

// GetAllProducts
// @basePath     /api
// @router       /v1/product/all-products [get]
// @tags         Products
// @summary      Get all products for Pricing Tool.
// @description  Get all products for Pricing Tool.
// @security     Bearer
// @accept       json
// @produce      json
// @Param        X-Culture   header    string  true  "Request culture"   default(tr-TR)
// @Param        X-Timezone  header    string  true  "Request timezone"  default(Europe/Istanbul)
// @success      200         {object}  api.ApiResponse
// @failure      400         {object}  api.ApiResponse
// @failure      401         {object}  api.ApiResponse
// @failure      500         {object}  api.ApiResponse
func (c *ProductController) GetAllProducts(context *gin.Context) {

	ch := make(chan product.GetAllProductsForPricingToolServiceResponse)
	defer close(ch)

	go c.productService.GetAllProductsForPricingTool(ch)

	serviceResponse := <-ch

	if serviceResponse.Error != nil {
		context.AbortWithError(http.StatusBadRequest, serviceResponse.Error)
		return
	}

	context.JSON(http.StatusOK, api.Ok(serviceResponse.AllProducts))
}
