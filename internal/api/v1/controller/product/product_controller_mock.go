// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v1/controller/product/product_controller.go

// Package product is a generated GoMock package.
package product

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockIProductController is a mock of IProductController interface.
type MockIProductController struct {
	ctrl     *gomock.Controller
	recorder *MockIProductControllerMockRecorder
}

// MockIProductControllerMockRecorder is the mock recorder for MockIProductController.
type MockIProductControllerMockRecorder struct {
	mock *MockIProductController
}

// NewMockIProductController creates a new mock instance.
func NewMockIProductController(ctrl *gomock.Controller) *MockIProductController {
	mock := &MockIProductController{ctrl: ctrl}
	mock.recorder = &MockIProductControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIProductController) EXPECT() *MockIProductControllerMockRecorder {
	return m.recorder
}

// GetAllProducts mocks base method.
func (m *MockIProductController) GetAllProducts(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetAllProducts", context)
}

// GetAllProducts indicates an expected call of GetAllProducts.
func (mr *MockIProductControllerMockRecorder) GetAllProducts(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllProducts", reflect.TypeOf((*MockIProductController)(nil).GetAllProducts), context)
}

// RegisterRoutes mocks base method.
func (m *MockIProductController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockIProductControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockIProductController)(nil).RegisterRoutes), routerGroup)
}
