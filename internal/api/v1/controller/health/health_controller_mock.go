// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v1/controller/health/health_controller.go

// Package health is a generated GoMock package.
package health

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockIHealthController is a mock of IHealthController interface.
type MockIHealthController struct {
	ctrl     *gomock.Controller
	recorder *MockIHealthControllerMockRecorder
}

// MockIHealthControllerMockRecorder is the mock recorder for MockIHealthController.
type MockIHealthControllerMockRecorder struct {
	mock *MockIHealthController
}

// NewMockIHealthController creates a new mock instance.
func NewMockIHealthController(ctrl *gomock.Controller) *MockIHealthController {
	mock := &MockIHealthController{ctrl: ctrl}
	mock.recorder = &MockIHealthControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIHealthController) EXPECT() *MockIHealthControllerMockRecorder {
	return m.recorder
}

// Ping mocks base method.
func (m *MockIHealthController) Ping(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Ping", context)
}

// Ping indicates an expected call of Ping.
func (mr *MockIHealthControllerMockRecorder) Ping(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockIHealthController)(nil).Ping), context)
}

// RegisterRoutes mocks base method.
func (m *MockIHealthController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockIHealthControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockIHealthController)(nil).RegisterRoutes), routerGroup)
}

// Service mocks base method.
func (m *MockIHealthController) Service(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Service", context)
}

// Service indicates an expected call of Service.
func (mr *MockIHealthControllerMockRecorder) Service(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Service", reflect.TypeOf((*MockIHealthController)(nil).Service), context)
}
