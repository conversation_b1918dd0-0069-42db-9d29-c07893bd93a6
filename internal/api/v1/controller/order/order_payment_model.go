package order

type OfflinePaymentListModel struct {
	SlipNo               string  `validate:"required"`
	RelatedTransactionId int64   `validate:"gt=0"`
	Price                float64 `validate:"gte=0"`
	TransactionDate      string  `validate:"required"`
	PaymentMethod        string  `validate:"required"`
	CollectedBy          string  `validate:"required"`
	CollectedUserName    string  `validate:"required"`
	BankCode             string  `validate:"required"`
	BankAuthCode         string  `validate:"required"`
	BankStan             string  `validate:"required"`
	SlipTerminalNumber   string  `validate:"required"`
}

type OrderOfflinePaymentModel struct {
	PaymentList    []OfflinePaymentListModel `validate:"required"`
	TrackingNumber string                    `validate:"required"`
	TransactionId  int64                     `validate:"gt=0"`
	TargetAmount   float64                   `validate:"gt=0"`
}

type SendOrderOfflinePaymentModel struct {
	OrderOfflinePaymentModel
}

type SendOrderRefundOfflinePaymentModel struct {
	OrderOfflinePaymentModel
}
