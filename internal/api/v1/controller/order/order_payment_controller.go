package order

import (
	"logo-adapter/internal/api"
	"logo-adapter/internal/service/order"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"net/http"

	"github.com/gin-gonic/gin"
)

type IOrderPaymentController interface {
	RegisterRoutes(routerGroup *gin.RouterGroup)
	SendOrderOfflinePayment(context *gin.Context)
	SendRefundOrderOfflinePayment(context *gin.Context)
}

type OrderPaymentController struct {
	path                string
	environment         env.IEnvironment
	loggr               logger.ILogger
	validatr            validator.IValidator
	cachr               cacher.ICacher
	OrderPaymentService order.IOrderPaymentService
}

// NewOrderPaymentController
// Returns a new OrderPaymentController.
func NewOrderPaymentController(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	OrderPaymentService order.IOrderPaymentService,
) IOrderPaymentController {
	controller := OrderPaymentController{
		path:        "order-payment",
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if OrderPaymentService != nil {
		controller.OrderPaymentService = OrderPaymentService
	} else {
		controller.OrderPaymentService = order.NewOrderPaymentService(environment, loggr, validatr, cachr, nil, nil)
	}
	return &controller
}

// RegisterRoutes
// Registers routes to gin.
func (c *OrderPaymentController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	routes := routerGroup.Group(c.path)
	routes.Use(api.AuthenticationMiddleware(c.environment))
	routes.POST("order-offline-payment", c.SendOrderOfflinePayment)
	routes.POST("refund-order-offline-payment", c.SendRefundOrderOfflinePayment)

}

// SendRefundOrderOfflinePayment
// @basePath     /api
// @router       /v1/order-payment/refund-order-offline-payment [post]
// @tags         Order Payment
// @summary      Send refund order offline  payment.
// @description  Send refund order offline  payment.
// @security     Bearer
// @accept       json
// @produce      json
// @Param        X-Culture   header    string  true  "Request culture"   default(tr-TR)
// @Param        X-Timezone  header    string  true  "Request timezone"  default(Europe/Istanbul)
// @success      200         {object}  api.ApiResponse
// @failure      400         {object}  api.ApiResponse
// @failure      401         {object}  api.ApiResponse
// @failure      500         {object}  api.ApiResponse
// @Param        Model       body      SendOrderRefundOfflinePaymentModel  true  "Request model"
func (c *OrderPaymentController) SendRefundOrderOfflinePayment(context *gin.Context) {
	var model SendOrderRefundOfflinePaymentModel
	err := context.ShouldBindJSON(&model)
	if err != nil {
		context.AbortWithError(http.StatusBadRequest, err)
		return
	}

	err = c.validatr.ValidateStruct(model)
	if err != nil {
		context.AbortWithError(http.StatusBadRequest, err)
		return
	}

	offlinePaymentServiceModel, err := c.PrepareOfflinePaymentServiceModel(&model.OrderOfflinePaymentModel)

	if err != nil {
		context.AbortWithError(http.StatusBadRequest, err)
		return
	}

	refundOrderOfflinePaymentCh := make(chan *order.SendRefundOrderOfflinePaymentServiceResponse)
	defer close(refundOrderOfflinePaymentCh)
	go c.OrderPaymentService.SendOrderRefundOfflinePayment(
		refundOrderOfflinePaymentCh, &order.SendRefundOrderOfflinePaymentServiceModel{
			OrderOfflinePaymentServiceModel: *offlinePaymentServiceModel,
		},
	)

	refundOrderOfflinePaymentResponse := <-refundOrderOfflinePaymentCh
	if refundOrderOfflinePaymentResponse.Error != nil {
		context.AbortWithError(http.StatusBadRequest, refundOrderOfflinePaymentResponse.Error)
		return
	}

	context.JSON(http.StatusOK, api.Ok(true))
}

// SendOrderOfflinePayment
// @basePath     /api
// @router       /v1/order-payment/order-offline-payment [post]
// @tags         Order Payment
// @summary      Send order offline payment.
// @description  Send order offline payment.
// @security     Bearer
// @accept       json
// @produce      json
// @Param        X-Culture   header    string  true  "Request culture"   default(tr-TR)
// @Param        X-Timezone  header    string  true  "Request timezone"  default(Europe/Istanbul)
// @success      200         {object}  api.ApiResponse
// @failure      400         {object}  api.ApiResponse
// @failure      401         {object}  api.ApiResponse
// @failure      500         {object}  api.ApiResponse
// @Param        Model       body      SendOrderOfflinePaymentModel  true  "Request model"
func (c *OrderPaymentController) SendOrderOfflinePayment(context *gin.Context) {
	var model SendOrderOfflinePaymentModel
	err := context.ShouldBindJSON(&model)
	if err != nil {
		context.AbortWithError(http.StatusBadRequest, err)
		return
	}

	err = c.validatr.ValidateStruct(model)
	if err != nil {
		context.AbortWithError(http.StatusBadRequest, err)
		return
	}

	offlinePaymentServiceModel, err := c.PrepareOfflinePaymentServiceModel(&model.OrderOfflinePaymentModel)

	if err != nil {
		context.AbortWithError(http.StatusBadRequest, err)
		return
	}

	orderOfflinePaymentCh := make(chan *order.SendOrderOfflinePaymentServiceResponse)
	defer close(orderOfflinePaymentCh)
	go c.OrderPaymentService.SendOrderOfflinePayment(
		orderOfflinePaymentCh, &order.SendOrderOfflinePaymentServiceModel{
			OrderOfflinePaymentServiceModel: *offlinePaymentServiceModel,
		},
	)

	orderOfflinePaymentResponse := <-orderOfflinePaymentCh
	if orderOfflinePaymentResponse.Error != nil {
		context.Error(orderOfflinePaymentResponse.Error)
		return
	}

	context.JSON(http.StatusOK, api.Ok(true))
}

func (c *OrderPaymentController) PrepareOfflinePaymentServiceModel(model *OrderOfflinePaymentModel) (*order.OrderOfflinePaymentServiceModel, error) {
	paymentList := make([]order.OfflinePaymentListServiceModel, len(model.PaymentList))
	for i, x := range model.PaymentList {

		transactionDate, err := helper.ToTimeFormat(x.TransactionDate)
		if err != nil {
			return nil, err
		}

		paymentList[i] = order.OfflinePaymentListServiceModel{
			SlipNo:               x.SlipNo,
			RelatedTransactionId: x.RelatedTransactionId,
			Price:                x.Price,
			TransactionDate:      transactionDate,
			PaymentMethod:        x.PaymentMethod,
			CollectedBy:          x.CollectedBy,
			CollectedUserName:    x.CollectedUserName,
			BankCode:             x.BankCode,
			BankAuthCode:         x.BankAuthCode,
			BankStan:             x.BankStan,
			SlipTerminalNumber:   x.SlipTerminalNumber,
		}
	}
	offlinePayment := order.OrderOfflinePaymentServiceModel{
		PaymentList:    paymentList,
		TrackingNumber: model.TrackingNumber,
		TransactionId:  model.TransactionId,
		TargetAmount:   model.TargetAmount,
	}

	return &offlinePayment, nil
}
