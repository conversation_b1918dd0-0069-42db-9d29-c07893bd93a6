package order

import "time"

type AddOrderHistoryModel struct {
	OrderHistory []AddOrderHistoryDetailsModel `validate:"required"`
}

type AddOrderHistoryDetailsModel struct {
	OrderId      string `validate:"required"`
	OrderDetails string `validate:"required"`
	Timestamp    string `validate:"required"`
}

type SendEttnToPandoraModel struct {
	OrderId       string    `json:"OrderId"`
	InvoiceNumber string    `json:"InvoiceNumber"`
	Ettn          string    `json:"Ettn"`
	InvoiceDate   time.Time `json:"InvoiceDate"`
}
