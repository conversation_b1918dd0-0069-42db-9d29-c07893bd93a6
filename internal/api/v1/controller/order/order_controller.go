package order

import (
	"logo-adapter/internal/api"
	"logo-adapter/internal/service/order"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/gin-gonic/gin"
)

type IOrderController interface {
	RegisterRoutes(routerGroup *gin.RouterGroup)
}

type OrderController struct {
	path         string
	environment  env.IEnvironment
	loggr        logger.ILogger
	validatr     validator.IValidator
	cachr        cacher.ICacher
	OrderService order.IOrderService
}

// NewOrderController
// Returns a new OrderController.
func NewOrderController(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	OrderService order.IOrderService,
) IOrderController {
	controller := OrderController{
		path:        "order",
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if OrderService != nil {
		controller.OrderService = OrderService
	} else {
		controller.OrderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}
	return &controller
}

// RegisterRoutes
// Registers routes to gin.
func (c *OrderController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	routes := routerGroup.Group(c.path)
	routes.Use(api.AuthenticationMiddleware(c.environment))
}
