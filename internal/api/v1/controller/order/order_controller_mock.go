// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v1/controller/order/order_controller.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockIOrderController is a mock of IOrderController interface.
type MockIOrderController struct {
	ctrl     *gomock.Controller
	recorder *MockIOrderControllerMockRecorder
}

// MockIOrderControllerMockRecorder is the mock recorder for MockIOrderController.
type MockIOrderControllerMockRecorder struct {
	mock *MockIOrderController
}

// NewMockIOrderController creates a new mock instance.
func NewMockIOrderController(ctrl *gomock.Controller) *MockIOrderController {
	mock := &MockIOrderController{ctrl: ctrl}
	mock.recorder = &MockIOrderControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrderController) EXPECT() *MockIOrderControllerMockRecorder {
	return m.recorder
}

// RegisterRoutes mocks base method.
func (m *MockIOrderController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockIOrderControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockIOrderController)(nil).RegisterRoutes), routerGroup)
}
