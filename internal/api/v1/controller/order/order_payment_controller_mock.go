// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v1/controller/order/order_payment_controller.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockIOrderPaymentController is a mock of IOrderPaymentController interface.
type MockIOrderPaymentController struct {
	ctrl     *gomock.Controller
	recorder *MockIOrderPaymentControllerMockRecorder
}

// MockIOrderPaymentControllerMockRecorder is the mock recorder for MockIOrderPaymentController.
type MockIOrderPaymentControllerMockRecorder struct {
	mock *MockIOrderPaymentController
}

// NewMockIOrderPaymentController creates a new mock instance.
func NewMockIOrderPaymentController(ctrl *gomock.Controller) *MockIOrderPaymentController {
	mock := &MockIOrderPaymentController{ctrl: ctrl}
	mock.recorder = &MockIOrderPaymentControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrderPaymentController) EXPECT() *MockIOrderPaymentControllerMockRecorder {
	return m.recorder
}

// RegisterRoutes mocks base method.
func (m *MockIOrderPaymentController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockIOrderPaymentControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockIOrderPaymentController)(nil).RegisterRoutes), routerGroup)
}

// SendOrderOfflinePayment mocks base method.
func (m *MockIOrderPaymentController) SendOrderOfflinePayment(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderOfflinePayment", context)
}

// SendOrderOfflinePayment indicates an expected call of SendOrderOfflinePayment.
func (mr *MockIOrderPaymentControllerMockRecorder) SendOrderOfflinePayment(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderOfflinePayment", reflect.TypeOf((*MockIOrderPaymentController)(nil).SendOrderOfflinePayment), context)
}

// SendRefundOrderOfflinePayment mocks base method.
func (m *MockIOrderPaymentController) SendRefundOrderOfflinePayment(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendRefundOrderOfflinePayment", context)
}

// SendRefundOrderOfflinePayment indicates an expected call of SendRefundOrderOfflinePayment.
func (mr *MockIOrderPaymentControllerMockRecorder) SendRefundOrderOfflinePayment(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRefundOrderOfflinePayment", reflect.TypeOf((*MockIOrderPaymentController)(nil).SendRefundOrderOfflinePayment), context)
}
