package sample

import (
	pubsubPublisherSample "logo-adapter/internal/data/pubsub/publisher/sample"
	snsPublisher "logo-adapter/internal/data/sns/publisher/sample"
	sqsPublisher "logo-adapter/internal/data/sqs/publisher/sample"
)

type AddSampleModel struct {
	SampleName *string `json:"SampleName"`
	SampleType *string `json:"SampleType"`
	SampleCode *int    `json:"SampleCode"`
}

type UpdateSampleModel struct {
	SampleStatus int    `json:"SampleStatus" validate:"required,gte=0"`
	ModifiedBy   string `json:"ModifiedBy" validate:"required"`
}

type PublishPubSubMessageModel struct {
	Count   int                                        `json:"Count" validate:"required"`
	Message pubsubPublisherSample.SamplePublisherModel `json:"Message" validate:"required"`
}

type PublishPubSubProtoMessageModel struct {
	Count   int                                             `json:"Count" validate:"required"`
	Message pubsubPublisherSample.SampleProtoPublisherModel `json:"Message" validate:"required"`
}

type PublishSqsMessageModel struct {
	Count   int                               `json:"Count" validate:"required"`
	Message sqsPublisher.SamplePublisherModel `json:"Message" validate:"required"`
}

type PublishSnsMessageModel struct {
	Count   int                                  `json:"Count" validate:"required"`
	Message snsPublisher.SampleSnsPublisherModel `json:"Message" validate:"required"`
}

type PostSampleXmlModel struct {
	SampleName string `json:"SampleName"`
	SampleType string `json:"SampleType"`
	SampleCode *int   `json:"SampleCode"`
}
