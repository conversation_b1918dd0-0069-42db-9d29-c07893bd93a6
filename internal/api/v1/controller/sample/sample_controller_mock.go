// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v1/controller/sample/sample_controller.go

// Package sample is a generated GoMock package.
package sample

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockISampleController is a mock of ISampleController interface.
type MockISampleController struct {
	ctrl     *gomock.Controller
	recorder *MockISampleControllerMockRecorder
}

// MockISampleControllerMockRecorder is the mock recorder for MockISampleController.
type MockISampleControllerMockRecorder struct {
	mock *MockISampleController
}

// NewMockISampleController creates a new mock instance.
func NewMockISampleController(ctrl *gomock.Controller) *MockISampleController {
	mock := &MockISampleController{ctrl: ctrl}
	mock.recorder = &MockISampleControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISampleController) EXPECT() *MockISampleControllerMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockISampleController) Add(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Add", context)
}

// Add indicates an expected call of Add.
func (mr *MockISampleControllerMockRecorder) Add(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockISampleController)(nil).Add), context)
}

// Get mocks base method.
func (m *MockISampleController) Get(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Get", context)
}

// Get indicates an expected call of Get.
func (mr *MockISampleControllerMockRecorder) Get(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockISampleController)(nil).Get), context)
}

// GetCache mocks base method.
func (m *MockISampleController) GetCache(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetCache", context)
}

// GetCache indicates an expected call of GetCache.
func (mr *MockISampleControllerMockRecorder) GetCache(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCache", reflect.TypeOf((*MockISampleController)(nil).GetCache), context)
}

// GetDatabase mocks base method.
func (m *MockISampleController) GetDatabase(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetDatabase", context)
}

// GetDatabase indicates an expected call of GetDatabase.
func (mr *MockISampleControllerMockRecorder) GetDatabase(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDatabase", reflect.TypeOf((*MockISampleController)(nil).GetDatabase), context)
}

// GetProxy mocks base method.
func (m *MockISampleController) GetProxy(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProxy", context)
}

// GetProxy indicates an expected call of GetProxy.
func (mr *MockISampleControllerMockRecorder) GetProxy(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProxy", reflect.TypeOf((*MockISampleController)(nil).GetProxy), context)
}

// PublishPubSubMessage mocks base method.
func (m *MockISampleController) PublishPubSubMessage(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishPubSubMessage", context)
}

// PublishPubSubMessage indicates an expected call of PublishPubSubMessage.
func (mr *MockISampleControllerMockRecorder) PublishPubSubMessage(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishPubSubMessage", reflect.TypeOf((*MockISampleController)(nil).PublishPubSubMessage), context)
}

// PublishPubSubProtoMessage mocks base method.
func (m *MockISampleController) PublishPubSubProtoMessage(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishPubSubProtoMessage", context)
}

// PublishPubSubProtoMessage indicates an expected call of PublishPubSubProtoMessage.
func (mr *MockISampleControllerMockRecorder) PublishPubSubProtoMessage(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishPubSubProtoMessage", reflect.TypeOf((*MockISampleController)(nil).PublishPubSubProtoMessage), context)
}

// PublishSnsMessage mocks base method.
func (m *MockISampleController) PublishSnsMessage(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishSnsMessage", context)
}

// PublishSnsMessage indicates an expected call of PublishSnsMessage.
func (mr *MockISampleControllerMockRecorder) PublishSnsMessage(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishSnsMessage", reflect.TypeOf((*MockISampleController)(nil).PublishSnsMessage), context)
}

// PublishSqsMessage mocks base method.
func (m *MockISampleController) PublishSqsMessage(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishSqsMessage", context)
}

// PublishSqsMessage indicates an expected call of PublishSqsMessage.
func (mr *MockISampleControllerMockRecorder) PublishSqsMessage(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishSqsMessage", reflect.TypeOf((*MockISampleController)(nil).PublishSqsMessage), context)
}

// RegisterRoutes mocks base method.
func (m *MockISampleController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockISampleControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockISampleController)(nil).RegisterRoutes), routerGroup)
}

// Update mocks base method.
func (m *MockISampleController) Update(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Update", context)
}

// Update indicates an expected call of Update.
func (mr *MockISampleControllerMockRecorder) Update(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockISampleController)(nil).Update), context)
}
