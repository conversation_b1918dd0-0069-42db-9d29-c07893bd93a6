// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/api/v1/controller/dispatch/dispatch_controller.go

// Package dispatch is a generated GoMock package.
package dispatch

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockIDispatchController is a mock of IDispatchController interface.
type MockIDispatchController struct {
	ctrl     *gomock.Controller
	recorder *MockIDispatchControllerMockRecorder
}

// MockIDispatchControllerMockRecorder is the mock recorder for MockIDispatchController.
type MockIDispatchControllerMockRecorder struct {
	mock *MockIDispatchController
}

// NewMockIDispatchController creates a new mock instance.
func NewMockIDispatchController(ctrl *gomock.Controller) *MockIDispatchController {
	mock := &MockIDispatchController{ctrl: ctrl}
	mock.recorder = &MockIDispatchControllerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDispatchController) EXPECT() *MockIDispatchControllerMockRecorder {
	return m.recorder
}

// GetDispatchDocument mocks base method.
func (m *MockIDispatchController) GetDispatchDocument(context *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetDispatchDocument", context)
}

// GetDispatchDocument indicates an expected call of GetDispatchDocument.
func (mr *MockIDispatchControllerMockRecorder) GetDispatchDocument(context interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDispatchDocument", reflect.TypeOf((*MockIDispatchController)(nil).GetDispatchDocument), context)
}

// RegisterRoutes mocks base method.
func (m *MockIDispatchController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterRoutes", routerGroup)
}

// RegisterRoutes indicates an expected call of RegisterRoutes.
func (mr *MockIDispatchControllerMockRecorder) RegisterRoutes(routerGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterRoutes", reflect.TypeOf((*MockIDispatchController)(nil).RegisterRoutes), routerGroup)
}
