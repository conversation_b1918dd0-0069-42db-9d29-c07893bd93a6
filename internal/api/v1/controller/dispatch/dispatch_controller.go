package dispatch

import (
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/service/dispatch"
	"net/http"

	"logo-adapter/internal/api"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/gin-gonic/gin"
)

type IDispatchController interface {
	GetDispatchDocument(context *gin.Context)
	RegisterRoutes(routerGroup *gin.RouterGroup)
}

type DispatchController struct {
	path            string
	environment     env.IEnvironment
	loggr           logger.ILogger
	validatr        validator.IValidator
	cachr           cacher.ICacher
	dispatchService dispatch.IDispatchService
}

// NewDispatchController
// Returns a new DispatchController.
func NewDispatchController(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, dispatchService dispatch.IDispatchService) IDispatchController {
	controller := DispatchController{
		path:        "dispatches",
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if dispatchService != nil {
		controller.dispatchService = dispatchService
	} else {
		controller.dispatchService = dispatch.NewDispatchService(environment, loggr, validatr, cachr, nil)
	}

	return &controller
}

func (c *DispatchController) RegisterRoutes(routerGroup *gin.RouterGroup) {
	routes := routerGroup.Group(c.path)
	routes.Use(api.AuthenticationMiddleware(c.environment))
	routes.GET("", c.GetDispatchDocument)
}

// GetDispatchDocument
// @basePath     /api
// @router       /v1/dispatches [get]
// @tags         Dispatches
// @summary      Gets dispatch document. Returns text/html response.
// @description  Gets dispatch document. Returns text/html response.
// @security     Bearer
// @accept       json
// @produce      text/html
// @success      200             {object}  string
// @failure      400             {object}  api.ApiResponse
// @failure      401             {object}  api.ApiResponse
// @failure      500             {object}  api.ApiResponse
// @Param        dispatchNumber  query     string  true  "Dispatch Number"
func (c *DispatchController) GetDispatchDocument(context *gin.Context) {
	dispatchNumber := context.Query("dispatchNumber")

	ch := make(chan *logo.LogoProxyBaseResponse)
	defer close(ch)

	go c.dispatchService.GetDispatchDocument(ch, &dispatch.GetDispatchDocumentModel{
		DispatchNumber: dispatchNumber,
	})

	serviceResponse := <-ch

	if serviceResponse.Error != nil {
		context.AbortWithError(http.StatusBadRequest, serviceResponse.Error)
		return
	}

	context.Data(http.StatusOK, "text/html", []byte(serviceResponse.Result))
}
