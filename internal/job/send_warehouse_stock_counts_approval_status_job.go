package job

import (
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/service/warehouse"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"time"

	"go.uber.org/zap"
)

type ISendWarehouseStockCountApprovalStatusJob interface {
	SendWarehouseStockCountsApprovalStatus()
}

type SendWarehouseStockCountApprovalStatusJob struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	warehouseStockService warehouse.IWarehouseStockService
	serverConfigService   configuration.IServerConfigurationService
}

func NewSendWarehouseStockCountApprovalStatusJob(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	warehouseStockService warehouse.IWarehouseStockService,
	serverConfigService configuration.IServerConfigurationService,
) ISendWarehouseStockCountApprovalStatusJob {
	sendWarehouseStockCountApprovalStatusJob := &SendWarehouseStockCountApprovalStatusJob{
		environment: environment,
		loggr: loggr.With(
			zap.String("JobName", "SendWarehouseStockCountsApprovalStatusJob"),
		),
		validatr: validatr,
		cachr:    cachr,
	}

	if warehouseStockService != nil {
		sendWarehouseStockCountApprovalStatusJob.warehouseStockService = warehouseStockService
	} else {
		sendWarehouseStockCountApprovalStatusJob.warehouseStockService = warehouse.NewWarehouseStockService(environment, loggr, validatr, cachr, nil, nil, nil, nil)
	}
	if serverConfigService != nil {
		sendWarehouseStockCountApprovalStatusJob.serverConfigService = serverConfigService
	} else {
		sendWarehouseStockCountApprovalStatusJob.serverConfigService = configuration.NewServerConfigurationService(environment, loggr, validatr, cachr, nil)
	}

	return sendWarehouseStockCountApprovalStatusJob
}

func (s *SendWarehouseStockCountApprovalStatusJob) SendWarehouseStockCountsApprovalStatus() {
	defer func() {
		if rec := recover(); rec != nil {
			s.loggr.Error("Recovered the panic.", zap.Any("panic", rec))
		}
	}()

	// Caching for job to run on single machine
	lockKey := "warehouse-approval-status-job-lock"
	lockAcquired := s.cachr.AcquireLock(lockKey, 60*time.Second)

	if lockAcquired {
		isOpen := s.serverConfigService.IsReceiverOpen("SendWarehouseStockCountsApprovalStatusJob", enum.IsSendWarehouseStockCountEmailJobOpen)
		if !isOpen {
			s.loggr.Warn("SendWarehouseStockCountsApprovalStatusJob is closed by config!")
			return
		}
		s.loggr.Info("SendWarehouseStockCountsApprovalStatusJob started.")

		ch := make(chan *warehouse.SendWarehouseStockCountsApprovalStatusAsMailServiceResponse)
		defer close(ch)
		go s.warehouseStockService.SendWarehouseStockCountsApprovalStatusAsMail(ch)

		response := <-ch

		if response.Error != nil {
			s.loggr.Error("SendWarehouseStockCountsApprovalStatusJob returned error.", zap.String("Error", response.Error.Error()))
			return
		}

		deletelockErr := s.cachr.ReleaseLock(lockKey)
		if deletelockErr != nil {
			s.loggr.Warn("SendWarehouseStockCountsApprovalStatusJob returned error while deleting redis lock!", zap.Error(deletelockErr))
		}
		s.loggr.Info("SendWarehouseStockCountsApprovalStatusJob finished.")
		return
	}
}
