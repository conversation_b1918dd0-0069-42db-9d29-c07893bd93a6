// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/job/send_warehouse_stock_counts_to_logo_job.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISendWarehouseStockCountsToLogoJob is a mock of ISendWarehouseStockCountsToLogoJob interface.
type MockISendWarehouseStockCountsToLogoJob struct {
	ctrl     *gomock.Controller
	recorder *MockISendWarehouseStockCountsToLogoJobMockRecorder
}

// MockISendWarehouseStockCountsToLogoJobMockRecorder is the mock recorder for MockISendWarehouseStockCountsToLogoJob.
type MockISendWarehouseStockCountsToLogoJobMockRecorder struct {
	mock *MockISendWarehouseStockCountsToLogoJob
}

// NewMockISendWarehouseStockCountsToLogoJob creates a new mock instance.
func NewMockISendWarehouseStockCountsToLogoJob(ctrl *gomock.Controller) *MockISendWarehouseStockCountsToLogoJob {
	mock := &MockISendWarehouseStockCountsToLogoJob{ctrl: ctrl}
	mock.recorder = &MockISendWarehouseStockCountsToLogoJobMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISendWarehouseStockCountsToLogoJob) EXPECT() *MockISendWarehouseStockCountsToLogoJobMockRecorder {
	return m.recorder
}

// SendWarehouseStockCounts mocks base method.
func (m *MockISendWarehouseStockCountsToLogoJob) SendWarehouseStockCounts() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendWarehouseStockCounts")
}

// SendWarehouseStockCounts indicates an expected call of SendWarehouseStockCounts.
func (mr *MockISendWarehouseStockCountsToLogoJobMockRecorder) SendWarehouseStockCounts() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWarehouseStockCounts", reflect.TypeOf((*MockISendWarehouseStockCountsToLogoJob)(nil).SendWarehouseStockCounts))
}
