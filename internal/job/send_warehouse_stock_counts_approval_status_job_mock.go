// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/job/send_warehouse_stock_counts_approval_status_job.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISendWarehouseStockCountApprovalStatusJob is a mock of ISendWarehouseStockCountApprovalStatusJob interface.
type MockISendWarehouseStockCountApprovalStatusJob struct {
	ctrl     *gomock.Controller
	recorder *MockISendWarehouseStockCountApprovalStatusJobMockRecorder
}

// MockISendWarehouseStockCountApprovalStatusJobMockRecorder is the mock recorder for MockISendWarehouseStockCountApprovalStatusJob.
type MockISendWarehouseStockCountApprovalStatusJobMockRecorder struct {
	mock *MockISendWarehouseStockCountApprovalStatusJob
}

// NewMockISendWarehouseStockCountApprovalStatusJob creates a new mock instance.
func NewMockISendWarehouseStockCountApprovalStatusJob(ctrl *gomock.Controller) *MockISendWarehouseStockCountApprovalStatusJob {
	mock := &MockISendWarehouseStockCountApprovalStatusJob{ctrl: ctrl}
	mock.recorder = &MockISendWarehouseStockCountApprovalStatusJobMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISendWarehouseStockCountApprovalStatusJob) EXPECT() *MockISendWarehouseStockCountApprovalStatusJobMockRecorder {
	return m.recorder
}

// SendWarehouseStockCountsApprovalStatus mocks base method.
func (m *MockISendWarehouseStockCountApprovalStatusJob) SendWarehouseStockCountsApprovalStatus() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendWarehouseStockCountsApprovalStatus")
}

// SendWarehouseStockCountsApprovalStatus indicates an expected call of SendWarehouseStockCountsApprovalStatus.
func (mr *MockISendWarehouseStockCountApprovalStatusJobMockRecorder) SendWarehouseStockCountsApprovalStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWarehouseStockCountsApprovalStatus", reflect.TypeOf((*MockISendWarehouseStockCountApprovalStatusJob)(nil).SendWarehouseStockCountsApprovalStatus))
}
