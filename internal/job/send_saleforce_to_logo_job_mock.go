// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/job/send_saleforce_to_logo_job.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISendSalesforceToLogoJob is a mock of ISendSalesforceToLogoJob interface.
type MockISendSalesforceToLogoJob struct {
	ctrl     *gomock.Controller
	recorder *MockISendSalesforceToLogoJobMockRecorder
}

// MockISendSalesforceToLogoJobMockRecorder is the mock recorder for MockISendSalesforceToLogoJob.
type MockISendSalesforceToLogoJobMockRecorder struct {
	mock *MockISendSalesforceToLogoJob
}

// NewMockISendSalesforceToLogoJob creates a new mock instance.
func NewMockISendSalesforceToLogoJob(ctrl *gomock.Controller) *MockISendSalesforceToLogoJob {
	mock := &MockISendSalesforceToLogoJob{ctrl: ctrl}
	mock.recorder = &MockISendSalesforceToLogoJobMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISendSalesforceToLogoJob) EXPECT() *MockISendSalesforceToLogoJobMockRecorder {
	return m.recorder
}

// SendSalesforceAccounts mocks base method.
func (m *MockISendSalesforceToLogoJob) SendSalesforceAccounts() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSalesforceAccounts")
}

// SendSalesforceAccounts indicates an expected call of SendSalesforceAccounts.
func (mr *MockISendSalesforceToLogoJobMockRecorder) SendSalesforceAccounts() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSalesforceAccounts", reflect.TypeOf((*MockISendSalesforceToLogoJob)(nil).SendSalesforceAccounts))
}
