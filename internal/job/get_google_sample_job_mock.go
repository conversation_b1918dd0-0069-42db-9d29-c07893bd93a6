// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/job/get_google_sample_job.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIGetGoogleSampleJob is a mock of IGetGoogleSampleJob interface.
type MockIGetGoogleSampleJob struct {
	ctrl     *gomock.Controller
	recorder *MockIGetGoogleSampleJobMockRecorder
}

// MockIGetGoogleSampleJobMockRecorder is the mock recorder for MockIGetGoogleSampleJob.
type MockIGetGoogleSampleJobMockRecorder struct {
	mock *MockIGetGoogleSampleJob
}

// NewMockIGetGoogleSampleJob creates a new mock instance.
func NewMockIGetGoogleSampleJob(ctrl *gomock.Controller) *MockIGetGoogleSampleJob {
	mock := &MockIGetGoogleSampleJob{ctrl: ctrl}
	mock.recorder = &MockIGetGoogleSampleJobMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGetGoogleSampleJob) EXPECT() *MockIGetGoogleSampleJobMockRecorder {
	return m.recorder
}

// GetGoogle mocks base method.
func (m *MockIGetGoogleSampleJob) GetGoogle() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetGoogle")
}

// GetGoogle indicates an expected call of GetGoogle.
func (mr *MockIGetGoogleSampleJobMockRecorder) GetGoogle() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoogle", reflect.TypeOf((*MockIGetGoogleSampleJob)(nil).GetGoogle))
}
