// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/job/send_partial_refund_orders_to_logo_job.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISendPartialRefundOrdersToLogoJob is a mock of ISendPartialRefundOrdersToLogoJob interface.
type MockISendPartialRefundOrdersToLogoJob struct {
	ctrl     *gomock.Controller
	recorder *MockISendPartialRefundOrdersToLogoJobMockRecorder
}

// MockISendPartialRefundOrdersToLogoJobMockRecorder is the mock recorder for MockISendPartialRefundOrdersToLogoJob.
type MockISendPartialRefundOrdersToLogoJobMockRecorder struct {
	mock *MockISendPartialRefundOrdersToLogoJob
}

// NewMockISendPartialRefundOrdersTo<PERSON><PERSON><PERSON>ob creates a new mock instance.
func NewMockISendPartialRefundOrdersToLogoJob(ctrl *gomock.Controller) *MockISendPartialRefundOrdersToLogoJob {
	mock := &MockISendPartialRefundOrdersToLogoJob{ctrl: ctrl}
	mock.recorder = &MockISendPartialRefundOrdersToLogoJobMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISendPartialRefundOrdersToLogoJob) EXPECT() *MockISendPartialRefundOrdersToLogoJobMockRecorder {
	return m.recorder
}

// SendPartialRefundOrders mocks base method.
func (m *MockISendPartialRefundOrdersToLogoJob) SendPartialRefundOrders() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendPartialRefundOrders")
}

// SendPartialRefundOrders indicates an expected call of SendPartialRefundOrders.
func (mr *MockISendPartialRefundOrdersToLogoJobMockRecorder) SendPartialRefundOrders() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPartialRefundOrders", reflect.TypeOf((*MockISendPartialRefundOrdersToLogoJob)(nil).SendPartialRefundOrders))
}
