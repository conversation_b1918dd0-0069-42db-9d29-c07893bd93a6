package job

import (
	"logo-adapter/internal/service/sample"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
)

type IGetGoogleSampleJob interface {
	GetGoogle()
}

type GetGoogleSampleJob struct {
	environment   env.IEnvironment
	loggr         logger.ILogger
	validatr      validator.IValidator
	cachr         cacher.ICacher
	sampleService sample.ISampleService
}

func NewGetGoogleSampleJob(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	sampleService sample.ISampleService,
) IGetGoogleSampleJob {
	sampleJob := &GetGoogleSampleJob{
		environment: environment,
		loggr: loggr.With(
			zap.String("JobName", "Get-Google-Job"),
		),
		validatr: validatr,
		cachr:    cachr,
	}

	if sampleService != nil {
		sampleJob.sampleService = sampleService
	} else {
		sampleJob.sampleService = sample.NewSampleService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil)
	}

	return sampleJob
}

func (s *GetGoogleSampleJob) GetGoogle() {
	defer func() {
		if rec := recover(); rec != nil {
			s.loggr.Error("Recovered the panic.", zap.Any("panic", rec))
		}
	}()

	ch := make(chan *sample.GetSampleServiceResponse)
	defer close(ch)

	serviceModel := sample.GetSampleServiceModel{
		SampleName: "sample-job",
		Id:         1,
	}
	go s.sampleService.GetGoogle(ch, &serviceModel)

	response := <-ch
	if response.Error != nil {
		s.loggr.Error("Get Google returned error !", zap.String("Error", response.Error.Error()))
	} else {
		s.loggr.Info("Successfully Get Google !",
			zap.Any("Response", response),
		)
	}
}
