package job

import (
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/service/warehouse"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"time"

	"go.uber.org/zap"
)

type ISendWarehouseStockCountsToLogoJob interface {
	SendWarehouseStockCounts()
}

type SendWarehouseStockCountsToLogoJob struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	warehouseStockService warehouse.IWarehouseStockService
	serverConfigService   configuration.IServerConfigurationService
}

func NewSendWarehouseStockCountsToLogoJob(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	warehouseStockService warehouse.IWarehouseStockService,
	serverConfigService configuration.IServerConfigurationService,
) ISendWarehouseStockCountsToLogoJob {
	sendWarehouseStockCountsToLogoJob := &SendWarehouseStockCountsToLogoJob{
		environment: environment,
		loggr: loggr.With(
			zap.String("JobName", "SendWarehouseStockCountsToLogoJob"),
		),
		validatr: validatr,
		cachr:    cachr,
	}

	if warehouseStockService != nil {
		sendWarehouseStockCountsToLogoJob.warehouseStockService = warehouseStockService
	} else {
		sendWarehouseStockCountsToLogoJob.warehouseStockService = warehouse.NewWarehouseStockService(environment, loggr, validatr, cachr, nil, nil, nil, nil)
	}
	if serverConfigService != nil {
		sendWarehouseStockCountsToLogoJob.serverConfigService = serverConfigService
	} else {
		sendWarehouseStockCountsToLogoJob.serverConfigService = configuration.NewServerConfigurationService(environment, loggr, validatr, cachr, nil)
	}

	return sendWarehouseStockCountsToLogoJob
}

func (s *SendWarehouseStockCountsToLogoJob) SendWarehouseStockCounts() {
	defer func() {
		if rec := recover(); rec != nil {
			s.loggr.Error("Recovered the panic.", zap.Any("panic", rec))
		}
	}()

	// Caching for job to run on single machine
	lockKey := "warehouse-stock-count-job-lock"
	lockAcquired := s.cachr.AcquireLock(lockKey, 60*time.Second)

	if lockAcquired {
		isOpen := s.serverConfigService.IsReceiverOpen("SendWarehouseStockCountsToLogoJob", enum.IsSendWarehouseStockCountToLogoJobOpen)
		if !isOpen {
			s.loggr.Warn("SendWarehouseStockCountsToLogoJob is closed by config!")
			return
		}
		s.loggr.Info("SendWarehouseStockCountsToLogoJob started.")

		ch := make(chan *warehouse.SendWarehouseStockCountsToLogoServiceResponse)
		defer close(ch)
		go s.warehouseStockService.SendWarehouseStockCountsToLogo(ch)

		response := <-ch

		if response.Error != nil {
			s.loggr.Error("SendWarehouseStockCountsToLogoJob returned error.", zap.String("Error", response.Error.Error()))
			return
		}

		if response.SentCount == 0 {
			s.loggr.Info("SendWarehouseStockCountsToLogoJob nothing to sent.")
			return
		}

		deletelockErr := s.cachr.ReleaseLock(lockKey)
		if deletelockErr != nil {
			s.loggr.Warn("SendWarehouseStockCountsToLogoJob returned error while deleting redis lock!", zap.Error(deletelockErr))
		}
		s.loggr.Info("SendWarehouseStockCountsToLogoJob finished.")
	}
}
