package job

import (
	"go.uber.org/zap"
	"logo-adapter/internal/service/salesforce"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"
)

type ISendSalesforceToLogoJob interface {
	SendSalesforceAccounts()
}

type SendSalesforceToLogoJob struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	salesforceService salesforce.ISalesforceService
}

func NewSendSalesforceAccountsToLogoJob(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	salesforceService salesforce.ISalesforceService,
) ISendSalesforceToLogoJob {
	SendSalesforceToLogoJob := &SendSalesforceToLogoJob{
		environment: environment,
		loggr: loggr.With(
			zap.String("<PERSON>N<PERSON>", "SendSalesforceToLogoJob"),
		),
		validatr: validatr,
		cachr:    cachr,
	}

	if salesforceService != nil {
		SendSalesforceToLogoJob.salesforceService = salesforceService
	} else {
		SendSalesforceToLogoJob.salesforceService = salesforce.NewSalesforceService(environment, loggr, validatr, cachr, nil, nil, nil)
	}

	return SendSalesforceToLogoJob
}

func (s *SendSalesforceToLogoJob) SendSalesforceAccounts() {
	defer func() {
		if rec := recover(); rec != nil {
			s.loggr.Error("Recovered the panic.", zap.Any("panic", rec))
		}
	}()

	ch := make(chan *salesforce.SendSalesforceAccountsToLogoServiceResponse)
	defer close(ch)
	go s.salesforceService.SendSalesforceAccountsToLogo(ch)

	response := <-ch

	if response.Error != nil {
		s.loggr.Error("SendSalesforceAccountsToLogo returned error.", zap.String("Error", response.Error.Error()))
		return
	}

	if response.DeliveredCount == 0 {
		s.loggr.Info("No Salesforce records to send to Logo.")
		return
	}

	s.loggr.Info(strconv.Itoa(response.DeliveredCount) + " salesforce records sent to Logo successfully.")
}
