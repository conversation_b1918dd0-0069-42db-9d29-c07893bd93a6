package job

import (
	"fmt"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/service/supplier"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strings"
	"time"

	"go.uber.org/zap"
)

type ISendSupplierToLogoJob interface {
	SendSuppliers()
}

type SendSupplierToLogoJob struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	supplierService      supplier.ISupplierService
	serverConfigService  configuration.IServerConfigurationService
	recurringTimeInHours int
	lastUpdatedAtKey     string
}

func NewSendSupplierToLogoJob(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	supplierService supplier.ISupplierService,
	serverConfigService configuration.IServerConfigurationService,
) ISendSupplierToLogoJob {
	sendSupplierToLogoJob := &SendSupplierToLogoJob{
		environment: environment,
		loggr: loggr.With(
			zap.String("JobName", "SendSupplierToLogoJob"),
		),
		validatr:             validatr,
		cachr:                cachr,
		recurringTimeInHours: 48,
		lastUpdatedAtKey:     "supplier-last-updated-at",
	}

	if supplierService != nil {
		sendSupplierToLogoJob.supplierService = supplierService
	} else {
		sendSupplierToLogoJob.supplierService = supplier.NewSupplierService(environment, loggr, validatr, cachr, nil, nil)
	}

	if serverConfigService != nil {
		sendSupplierToLogoJob.serverConfigService = serverConfigService
	} else {
		sendSupplierToLogoJob.serverConfigService = configuration.NewServerConfigurationService(environment, loggr, validatr, cachr, nil)
	}
	return sendSupplierToLogoJob
}

func (s *SendSupplierToLogoJob) SendSuppliers() {
	defer func() {
		if rec := recover(); rec != nil {
			s.loggr.Error("Recovered the panic.", zap.Any("panic", rec))
		}
	}()

	var updatedAt time.Time
	startTime := time.Now().UTC()

	configCh := make(chan configuration.GetConfigServiceResponse)
	defer close(configCh)

	go s.serverConfigService.GetConfig(configCh, s.lastUpdatedAtKey)
	configResponse := <-configCh

	if configResponse.Error != nil {
		s.loggr.Error(configResponse.Error.Error())
		return
	}

	if configResponse.DataValue == "" {
		formattedStartTime := startTime.Add(time.Hour * time.Duration(-s.recurringTimeInHours)).Format(time.RFC3339)
		parsedStartTime, parseErr := time.Parse(time.RFC3339, formattedStartTime)
		if parseErr != nil {
			s.loggr.Error(parseErr.Error())
			return
		}

		updatedAt = parsedStartTime
	} else {
		parsedTime, parseErr := time.Parse(time.RFC3339, configResponse.DataValue)
		if parseErr != nil {
			s.loggr.Error(parseErr.Error())
			return
		}

		updatedAt = parsedTime
	}

	ch := make(chan *supplier.SendSuppliersToLogoResponse)
	defer close(ch)

	go s.supplierService.SendSuppliersToLogo(ch, supplier.SendSuppliersToLogoServiceModel{
		UpdatedAt: updatedAt,
	})

	response := <-ch

	if response.Error != nil {
		s.loggr.Error("SendSuppliersToLogo Returned Error !", zap.String("Error", response.Error.Error()))
	} else {
		sentSupplierCount := len(response.SupplierNames)
		if sentSupplierCount > 0 {
			message := fmt.Sprintf("%v Suppliers Sent To Logo Successfully !", sentSupplierCount)
			s.loggr.Info(message, zap.String("SupplierNames", strings.Join(response.SupplierNames[:], ",\n")))
		}

		go s.serverConfigService.UpsertConfig(configCh, configuration.UpsertConfigServiceModel{
			DataKey:    s.lastUpdatedAtKey,
			DataValue:  startTime.Format(time.RFC3339),
			DataTypeId: enum.Text,
		})
		configResponse = <-configCh
		if configResponse.Error != nil {
			s.loggr.Error("Couldn't set last execution time.", zap.Error(configResponse.Error))
			return
		}
	}
}
