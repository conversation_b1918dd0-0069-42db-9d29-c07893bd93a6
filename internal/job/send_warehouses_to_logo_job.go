package job

import (
	"logo-adapter/internal/service/warehouse"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"

	"go.uber.org/zap"
)

type ISendWarehouseToLogoJob interface {
	SendWarehouses()
}

type SendWarehouseToLogoJob struct {
	environment      env.IEnvironment
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	warehouseService warehouse.IWarehouseService
}

func NewSendWarehouseToLogoJob(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	warehouseService warehouse.IWarehouseService,
) ISendWarehouseToLogoJob {
	SendWarehouseToLogoJob := &SendWarehouseToLogoJob{
		environment: environment,
		loggr: loggr.With(
			zap.String("JobName", "SendWarehouseToLogoJob"),
		),
		validatr: validatr,
		cachr:    cachr,
	}

	if warehouseService != nil {
		SendWarehouseToLogoJob.warehouseService = warehouseService
	} else {
		SendWarehouseToLogoJob.warehouseService = warehouse.NewWarehouseService(environment, loggr, validatr, cachr, nil, nil, nil, nil)
	}

	return SendWarehouseToLogoJob
}

func (s *SendWarehouseToLogoJob) SendWarehouses() {
	defer func() {
		if rec := recover(); rec != nil {
			s.loggr.Error("Recovered the panic.", zap.Any("panic", rec))
		}
	}()

	ch := make(chan *warehouse.SendUndeliveredWarehousesToLogoServiceResponse)
	defer close(ch)
	go s.warehouseService.SendUndeliveredWarehousesToLogo(ch)

	response := <-ch

	if response.Error != nil {
		s.loggr.Error("SendWarehousesToLogo returned error.", zap.String("Error", response.Error.Error()))
		return
	}

	if response.DeliveredCount == 0 {
		s.loggr.Info("Nothing to deliver.")
		return
	}

	s.loggr.Info(strconv.Itoa(response.DeliveredCount) + " " + "warehouses sent to Logo successfully.")
}
