// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/job/send_warehouses_to_logo_job.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISendWarehouseToLogoJob is a mock of ISendWarehouseToLogoJob interface.
type MockISendWarehouseToLogoJob struct {
	ctrl     *gomock.Controller
	recorder *MockISendWarehouseToLogoJobMockRecorder
}

// MockISendWarehouseToLogoJobMockRecorder is the mock recorder for MockISendWarehouseToLogoJob.
type MockISendWarehouseToLogoJobMockRecorder struct {
	mock *MockISendWarehouseToLogoJob
}

// NewMockISendWarehouseToLogoJob creates a new mock instance.
func NewMockISendWarehouseToLogoJob(ctrl *gomock.Controller) *MockISendWarehouseToLogoJob {
	mock := &MockISendWarehouseToLogoJob{ctrl: ctrl}
	mock.recorder = &MockISendWarehouseToLogoJobMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISendWarehouseToLogoJob) EXPECT() *MockISendWarehouseToLogoJobMockRecorder {
	return m.recorder
}

// SendWarehouses mocks base method.
func (m *MockISendWarehouseToLogoJob) SendWarehouses() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendWarehouses")
}

// SendWarehouses indicates an expected call of SendWarehouses.
func (mr *MockISendWarehouseToLogoJobMockRecorder) SendWarehouses() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWarehouses", reflect.TypeOf((*MockISendWarehouseToLogoJob)(nil).SendWarehouses))
}
