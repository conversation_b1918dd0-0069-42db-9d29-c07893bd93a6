// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/job/send_supplier_to_logo_job.go

// Package job is a generated GoMock package.
package job

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISendSupplierToLogoJob is a mock of ISendSupplierToLogoJob interface.
type MockISendSupplierToLogoJob struct {
	ctrl     *gomock.Controller
	recorder *MockISendSupplierToLogoJobMockRecorder
}

// MockISendSupplierToLogoJobMockRecorder is the mock recorder for MockISendSupplierToLogoJob.
type MockISendSupplierToLogoJobMockRecorder struct {
	mock *MockISendSupplierToLogoJob
}

// NewMockISendSupplierToLogoJob creates a new mock instance.
func NewMockISendSupplierToLogoJob(ctrl *gomock.Controller) *MockISendSupplierToLogoJob {
	mock := &MockISendSupplierToLogoJob{ctrl: ctrl}
	mock.recorder = &MockISendSupplierToLogoJobMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISendSupplierToLogoJob) EXPECT() *MockISendSupplierToLogoJobMockRecorder {
	return m.recorder
}

// SendSuppliers mocks base method.
func (m *MockISendSupplierToLogoJob) SendSuppliers() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSuppliers")
}

// SendSuppliers indicates an expected call of SendSuppliers.
func (mr *MockISendSupplierToLogoJobMockRecorder) SendSuppliers() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSuppliers", reflect.TypeOf((*MockISendSupplierToLogoJob)(nil).SendSuppliers))
}
