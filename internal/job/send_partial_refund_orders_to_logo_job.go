package job

import (
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/service/order"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"time"

	"go.uber.org/zap"
)

type ISendPartialRefundOrdersToLogoJob interface {
	SendPartialRefundOrders()
}

type SendPartialRefundOrdersToLogoJob struct {
	environment         env.IEnvironment
	loggr               logger.ILogger
	validatr            validator.IValidator
	cachr               cacher.ICacher
	orderService        order.IOrderService
	serverConfigService configuration.IServerConfigurationService
}

func NewSendPartialRefundOrdersToLogoJob(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	orderService order.IOrderService,
	serverConfigService configuration.IServerConfigurationService,
) ISendPartialRefundOrdersToLogoJob {
	sendPartialRefundOrdersToLogoJob := &SendPartialRefundOrdersToLogoJob{
		environment: environment,
		loggr: loggr.With(
			zap.String("JobName", "SendPartialRefundOrdersToLogoJob"),
		),
		validatr: validatr,
		cachr:    cachr,
	}

	if orderService != nil {
		sendPartialRefundOrdersToLogoJob.orderService = orderService
	} else {
		sendPartialRefundOrdersToLogoJob.orderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}
	if serverConfigService != nil {
		sendPartialRefundOrdersToLogoJob.serverConfigService = serverConfigService
	} else {
		sendPartialRefundOrdersToLogoJob.serverConfigService = configuration.NewServerConfigurationService(environment, loggr, validatr, cachr, nil)
	}

	return sendPartialRefundOrdersToLogoJob
}

func (s *SendPartialRefundOrdersToLogoJob) SendPartialRefundOrders() {
	defer func() {
		if rec := recover(); rec != nil {
			s.loggr.Error("Recovered the panic.", zap.Any("panic", rec))
		}
	}()

	// Caching for job to run on single machine
	lockKey := "partial-refund-orders-job-lock"
	lockAcquired := s.cachr.AcquireLock(lockKey, 60*time.Second)

	if lockAcquired {
		isOpen := s.serverConfigService.IsReceiverOpen("SendPartialRefundOrdersToLogoJob", enum.IsSendPartialRefundOrdersToLogoJobOpen)
		if !isOpen {
			s.loggr.Warn("SendPartialRefundOrdersToLogoJob is closed by config!")
			return
		}
		s.loggr.Info("SendPartialRefundOrdersToLogoJob started.")

		ch := make(chan *order.SendPartialRefundOrdersServiceResponse)
		defer close(ch)
		go s.orderService.SendPartialRefundOrders(ch)

		response := <-ch

		if response.Error != nil {
			s.loggr.Error("SendPartialRefundOrdersToLogoJob returned error.", zap.String("Error", response.Error.Error()))
			return
		}

		deletelockErr := s.cachr.ReleaseLock(lockKey)
		if deletelockErr != nil {
			s.loggr.Warn("SendPartialRefundOrdersToLogoJob returned error while deleting redis lock!", zap.Error(deletelockErr))
		}
		s.loggr.Info("SendPartialRefundOrdersToLogoJob finished.")
	}
}
