// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/sns/publisher/sample/sample_publisher.go

// Package sample is a generated GoMock package.
package sample

import (
	publisher "logo-adapter/internal/data/sns/publisher"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISampleSnsPublisher is a mock of ISampleSnsPublisher interface.
type MockISampleSnsPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockISampleSnsPublisherMockRecorder
}

// MockISampleSnsPublisherMockRecorder is the mock recorder for MockISampleSnsPublisher.
type MockISampleSnsPublisherMockRecorder struct {
	mock *MockISampleSnsPublisher
}

// NewMockISampleSnsPublisher creates a new mock instance.
func NewMockISampleSnsPublisher(ctrl *gomock.Controller) *MockISampleSnsPublisher {
	mock := &MockISampleSnsPublisher{ctrl: ctrl}
	mock.recorder = &MockISampleSnsPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISampleSnsPublisher) EXPECT() *MockISampleSnsPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockISampleSnsPublisher) Publish(ch chan publisher.PublisherResponse, message *SampleSnsPublisherModel, attributeOverrides map[string]string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Publish", ch, message, attributeOverrides)
}

// Publish indicates an expected call of Publish.
func (mr *MockISampleSnsPublisherMockRecorder) Publish(ch, message, attributeOverrides interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockISampleSnsPublisher)(nil).Publish), ch, message, attributeOverrides)
}
