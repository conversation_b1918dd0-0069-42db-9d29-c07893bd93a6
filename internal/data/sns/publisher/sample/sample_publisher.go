package sample

import (
	"context"
	"encoding/json"
	"logo-adapter/internal/data/sns/publisher"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sns"
	"github.com/aws/aws-sdk-go-v2/service/sns/types"
	"go.uber.org/zap"
)

type ISampleSnsPublisher interface {
	Publish(ch chan publisher.PublisherResponse, message *SampleSnsPublisherModel, attributeOverrides map[string]string)
}

type SampleSnsPublisher struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	timeout           time.Duration
	defaultAttributes map[string]string
	topicArn          string
	client            *sns.Client
}

// NewSampleSnsPublisher
// Returns a new SampleSnsPublisher.
func NewSampleSnsPublisher(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) ISampleSnsPublisher {
	topicArn := environment.Get(env.SamplePublisherSnsTopicArn)
	region := environment.Get(env.SamplePublisherSnsRegion)

	awsConfig, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(region))
	if err != nil {
		panic("Panicked while loading AWS config: " + err.Error())
	}

	snsClient := sns.NewFromConfig(awsConfig)

	publisher := SampleSnsPublisher{
		environment:       environment,
		loggr:             loggr.With(zap.String("topicArn", topicArn)),
		validatr:          validatr,
		cachr:             cachr,
		timeout:           time.Second * 5,
		defaultAttributes: map[string]string{},
		topicArn:          topicArn,
		client:            snsClient,
	}

	return &publisher
}

func (p *SampleSnsPublisher) Publish(ch chan publisher.PublisherResponse, model *SampleSnsPublisherModel, attributeOverrides map[string]string) {
	err := p.validatr.ValidateStruct(model)
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), p.timeout)
	defer cancel()

	bytes, err := json.Marshal(model)
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	output, err := p.client.Publish(ctx, &sns.PublishInput{
		TargetArn:         aws.String(p.topicArn),
		Message:           aws.String(string(bytes)),
		MessageAttributes: p.mapAttributes(p.overrideAttributes(attributeOverrides)),
	})

	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	p.loggr.Info(*output.MessageId+" ID message is published successfully.", zap.String("messageId", *output.MessageId), zap.Any("data", model))
	ch <- publisher.PublisherResponse{MessageId: output.MessageId}
}

func (p *SampleSnsPublisher) overrideAttributes(overrides map[string]string) map[string]string {
	attr := make(map[string]string)

	for key, value := range p.defaultAttributes {
		attr[key] = value
	}

	for key, value := range overrides {
		attr[key] = value
	}

	return attr
}

func (p *SampleSnsPublisher) mapAttributes(attributes map[string]string) map[string]types.MessageAttributeValue {
	attr := make(map[string]types.MessageAttributeValue)

	for key, value := range attributes {
		attr[key] = types.MessageAttributeValue{
			DataType:    aws.String("String"),
			StringValue: aws.String(value),
		}
	}

	return attr
}
