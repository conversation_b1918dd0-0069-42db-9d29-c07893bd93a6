// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/sns/publisher/order/ettn_publisher.go

// Package order is a generated GoMock package.
package order

import (
	publisher "logo-adapter/internal/data/sns/publisher"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIEttnPublisher is a mock of IEttnPublisher interface.
type MockIEttnPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockIEttnPublisherMockRecorder
}

// MockIEttnPublisherMockRecorder is the mock recorder for MockIEttnPublisher.
type MockIEttnPublisherMockRecorder struct {
	mock *MockIEttnPublisher
}

// NewMockIEttnPublisher creates a new mock instance.
func NewMockIEttnPublisher(ctrl *gomock.Controller) *MockIEttnPublisher {
	mock := &MockIEttnPublisher{ctrl: ctrl}
	mock.recorder = &MockIEttnPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIEttnPublisher) EXPECT() *MockIEttnPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockIEttnPublisher) Publish(ch chan publisher.PublisherResponse, message *EttnPublisherModel, attributeOverrides map[string]string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Publish", ch, message, attributeOverrides)
}

// Publish indicates an expected call of Publish.
func (mr *MockIEttnPublisherMockRecorder) Publish(ch, message, attributeOverrides interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockIEttnPublisher)(nil).Publish), ch, message, attributeOverrides)
}
