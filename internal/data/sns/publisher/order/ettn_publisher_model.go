package order

import "time"

type EttnPublisherModel struct {
	EventId        string    `json:"event_id"`
	EventTime      time.Time `json:"event_time"`
	Source         string    `json:"source"`
	Type           string    `json:"type"`
	Brand          string    `json:"brand"`
	CountryCode    string    `json:"country_code"`
	GlobalEntityId string    `json:"global_entity_id"`
	RequestId      string    `json:"request_id"`
	OrderCode      string    `json:"order_code" validate:"required"`
	LanguageCode   string    `json:"language_code"`
	Data           EttnData  `json:"data"`
}

type EttnData struct {
	OrderCode    string   `json:"order_code"`
	LanguageCode string   `json:"language_code"`
	Logo         LogoData `json:"logo"`
}

type LogoData struct {
	OrderCode     string    `json:"orderCode" validate:"required"`
	Ettn          string    `json:"ettn" validate:"required"`
	InvoiceNumber string    `json:"invoiceNumber" validate:"required"`
	CourierName   string    `json:"courierName"`
	InvoiceDate   time.Time `json:"invoiceDate" validate:"required"`
	Order         EttnOrder `json:"order"`
}

type EttnOrder struct {
	OrderID              string        `json:"OrderID"`
	PaymentMethodName    string        `json:"PaymentMethodName"`
	Version              int           `json:"Version"`
	Status               string        `json:"Status"`
	PaymentList          []Payment     `json:"PaymentList"`
	TotalLineItemsAmount float64       `json:"TotalLineItemsAmount"`
	DeliveryFee          float64       `json:"DeliveryFee"`
	TotalAmount          float64       `json:"TotalAmount"`
	TipAmount            float64       `json:"TipAmount"`
	CouponDiscountAmount float64       `json:"CouponDiscountAmount"`
	BasketDiscountAmount float64       `json:"BasketDiscountAmount"`
	OrderDetail          []OrderDetail `json:"OrderDetail"`
	OrderAddress         OrderAddress  `json:"OrderAddress"`
	UserData             UserData      `json:"UserData"`
	OrderCreationDate    time.Time     `json:"OrderCreationDate"`
	PaidAt               time.Time     `json:"PaidAt"`
	Vats                 []Vat         `json:"Vats"`
	VatBase              float64       `json:"VatBase"`
}

type Payment struct {
	BankCode string  `json:"BankCode"`
	Amount   float64 `json:"Amount"`
}

type OrderDetail struct {
	LineItemID     string  `json:"LineItemId"`
	Name           string  `json:"Name"`
	ProductID      string  `json:"ProductId"`
	Quantity       int     `json:"Quantity"`
	TotalListPrice float64 `json:"TotalListPrice"`
	TotalPrice     float64 `json:"TotalPrice"`
	UnitListPrice  float64 `json:"UnitListPrice"`
	Vat            float64 `json:"Vat"`
	Discount       float64 `json:"Discount"`
}

type OrderAddress struct {
	FirstName       string `json:"FirstName"`
	LastName        string `json:"LastName"`
	CityName        string `json:"CityName"`
	RegionName      string `json:"RegionName"`
	AddressLine     string `json:"AddressLine"`
	TelephoneNumber string `json:"TelephoneNumber"`
	Email           string `json:"Email"`
}
type UserData struct {
	UserID        string `json:"UserId"`
	UserFirstName string `json:"UserFirstName"`
	UserLastName  string `json:"UserLastName"`
}

type Vat struct {
	Rate   float64 `json:"Rate"`
	Amount float64 `json:"Amount"`
	Base   float64 `json:"Base"`
}
