package receiver

type IReceiver interface {
	InitReceivers(count int)
}

type IReceiverHandler interface {
	Handle(ch chan error, model *ReceiverHandlerModel)
}

type IOrderStreamReceiverHandler interface {
	Handle(ch chan OrderStreamReceiverResponseModel, model *ReceiverHandlerModel)
}

type ReceiverHandlerModel struct {
	MessageId  string            `validate:"required"`
	Data       []byte            `validate:"required"`
	Attributes map[string]string `validate:"required"`
}

type OrderStreamReceiverResponseModel struct {
	Error               error
	IsUnsupportDataType bool
}
