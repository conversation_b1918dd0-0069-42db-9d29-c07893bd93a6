package salesforce

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/salesforce"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type ISalesforceCreateAccountReceiverHandler interface {
	Handle(ch chan error, model *receiver.ReceiverHandlerModel)
}

type SalesforceCreateAccountReceiverHandler struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	SalesforceService salesforce.ISalesforceService
}

// NewSalesforceCreateAccountReceiverHandler
// Returns a new SalesforceCreateAccountReceiverHandler
func NewSalesforceCreateAccountReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	salesforceService salesforce.ISalesforceService,
) ISalesforceCreateAccountReceiverHandler {
	handler := SalesforceCreateAccountReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if salesforceService != nil {
		handler.SalesforceService = salesforceService
	} else {
		handler.SalesforceService = salesforce.NewSalesforceService(environment, loggr, validatr, cachr, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *SalesforceCreateAccountReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel SalesforceCreateAccountReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	updateDbCh := make(chan *salesforce.CreateSalesforceAccountServiceResponse)
	defer close(updateDbCh)

	serviceModel := handlerModel.ToServiceModel()
	go h.SalesforceService.CreateSalesforceAccount(updateDbCh, &serviceModel)

	updateDbChResponse := <-updateDbCh
	if updateDbChResponse.Error != nil {
		ch <- updateDbChResponse.Error
		return
	}

	ch <- nil
	return
}
