package salesforce

import (
	"logo-adapter/internal/service/salesforce"
	"time"
)

type SalesforceUpdateContactReceiverHandlerModel struct {
	Version  int `json:"version"`
	Metadata struct {
		IngestionTimestamp string `json:"ingestion_timestamp"`
		Type               string `json:"type"`
		Version            int    `json:"version"`
		Source             string `json:"source"`
		Tracking           []struct {
			Name string `json:"name"`
			Id   string `json:"id"`
		} `json:"tracking"`
		ImportId          string   `json:"import_id"`
		PiiRemovalDate    string   `json:"pii_removal_date"`
		ChangedFieldNames []string `json:"changed_field_names"`
	} `json:"metadata"`
	Content struct {
		ReplayId           int       `json:"replay_id"`
		Timestamp          time.Time `json:"timestamp"`
		AccountGrid        string    `json:"account_grid"`
		RecordType         string    `json:"record_type"`
		AccountCountryCode string    `json:"account_country_code"`
		EventType          string    `json:"event_type"`
		AccountId          string    `json:"account_id"`
		Contact            struct {
			Id          string `json:"id"`
			Salutation  string `json:"salutation"`
			FirstName   string `json:"first_name"`
			LastName    string `json:"last_name"`
			MiddleName  string `json:"middle_name"`
			Suffix      string `json:"suffix"`
			MobilePhone string `json:"mobile_phone"`
			Phone       string `json:"phone"`
			Role        string `json:"role"`
			Email       string `json:"email"`
			Hotline     string `json:"hotline"`
		} `json:"contact"`
		Contacts []struct {
			Id          string `json:"id"`
			Salutation  string `json:"salutation"`
			FirstName   string `json:"first_name"`
			LastName    string `json:"last_name"`
			MiddleName  string `json:"middle_name"`
			Suffix      string `json:"suffix"`
			MobilePhone string `json:"mobile_phone"`
			Phone       string `json:"phone"`
			Role        string `json:"role"`
			Email       string `json:"email"`
			Hotline     string `json:"hotline"`
		} `json:"contacts"`
	} `json:"content"`
	Timestamp string `json:"timestamp"`
}

func (handlerModel SalesforceUpdateContactReceiverHandlerModel) ToServiceModel(messageId string) salesforce.SalesforceUpdateContactServiceModel {
	content := handlerModel.Content

	serviceModel := salesforce.SalesforceUpdateContactServiceModel{
		MessageId:        messageId,
		ReplayId:         content.ReplayId,
		PayloadTimestamp: content.Timestamp,
		AccountGrid:      content.AccountGrid,
	}

	if content.Contacts != nil && len(content.Contacts) > 0 {
		serviceModel.EmailAddress = content.Contacts[0].Email
		serviceModel.MobilePhoneNumber = content.Contacts[0].MobilePhone
		serviceModel.PhoneNumber = content.Contacts[0].Phone
	} else {
		serviceModel.EmailAddress = content.Contact.Email
		serviceModel.MobilePhoneNumber = content.Contact.MobilePhone
		serviceModel.PhoneNumber = content.Contact.Phone
	}

	return serviceModel
}
