package salesforce

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/salesforce"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type ISalesforceUpdateAccountReceiverHandler interface {
	Handle(ch chan error, model *receiver.ReceiverHandlerModel)
}

type SalesforceUpdateAccountReceiverHandler struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	SalesforceService salesforce.ISalesforceService
}

// NewSalesforceUpdateAccountReceiverHandler
// Returns a new SalesforceUpdateAccountReceiverHandler
func NewSalesforceUpdateAccountReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	salesforceService salesforce.ISalesforceService,
) ISalesforceUpdateAccountReceiverHandler {
	handler := SalesforceUpdateAccountReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if salesforceService != nil {
		handler.SalesforceService = salesforceService
	} else {
		handler.SalesforceService = salesforce.NewSalesforceService(environment, loggr, validatr, cachr, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *SalesforceUpdateAccountReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel SalesforceUpdateAccountReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	serviceResponseCh := make(chan *salesforce.UpdateSalesforceAccountServiceResponse)
	defer close(serviceResponseCh)

	serviceModel := handlerModel.ToServiceModel()
	go h.SalesforceService.UpdateSalesforceAccount(serviceResponseCh, &serviceModel)

	serviceResponse := <-serviceResponseCh

	if serviceResponse.Error != nil {
		ch <- serviceResponse.Error
		return
	}

	ch <- nil
	return
}
