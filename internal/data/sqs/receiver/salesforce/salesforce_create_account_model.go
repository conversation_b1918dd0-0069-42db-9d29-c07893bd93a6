package salesforce

import (
	"logo-adapter/internal/service/salesforce"
	"time"
)

type SalesforceCreateAccountReceiverHandlerModel struct {
	Version  int `json:"version"`
	Metadata struct {
		IngestionTimestamp string `json:"ingestion_timestamp"`
		Type               string `json:"type"`
		Version            int    `json:"version"`
		Source             string `json:"source"`
		Tracking           []struct {
			Name string `json:"name"`
			Id   string `json:"id"`
		} `json:"tracking"`
		ImportId          string   `json:"import_id"`
		PiiRemovalDate    string   `json:"pii_removal_date"`
		ChangedFieldNames []string `json:"changed_field_names"`
	} `json:"metadata"`
	Content struct {
		ReplayId           int       `json:"replay_id"`
		Timestamp          time.Time `json:"timestamp"`
		AccountGrid        string    `json:"account_grid"`
		RecordType         string    `json:"record_type"`
		AccountCountryCode string    `json:"account_country_code"`
		EventType          string    `json:"event_type"`
		New                bool      `json:"new"`
		Industry           string    `json:"industry"`
		Platforms          []struct {
			Id      string `json:"id"`
			Name    string `json:"name"`
			Account string `json:"account"`
			Status  string `json:"status"`
		} `json:"platforms"`
		Account struct {
			Id            string `json:"id"`
			Name          string `json:"name"`
			ParentAccount struct {
				Id   string `json:"id"`
				Type string `json:"type"`
				Grid string `json:"grid"`
			} `json:"parent_account"`
			FranchiseAccount struct {
				Id   string `json:"id"`
				Type string `json:"type"`
				Grid string `json:"grid"`
				Name string `json:"name"`
			} `json:"franchise_account"`
			FacilityAccount struct {
				Grid string `json:"grid"`
			} `json:"facility_account"`
			AccountFacilityCapex                int      `json:"account_facility_capex"`
			AccountFacilityOpexNonRent          int      `json:"account_facility_opex_non_rent"`
			AccountFacilityOpexRent             int      `json:"account_facility_opex_rent"`
			AccountFacilityRentalExpirationDate string   `json:"account_facility_rental_expiration_date"`
			AccountFacilityStallsOccupied       int      `json:"account_facility_stalls_occupied"`
			AccountFacilitySurfaceArea          int      `json:"account_facility_surface_area"`
			AccountFacilityTotalStalls          int      `json:"account_facility_total_stalls"`
			AccountFinanceSpecifications        []string `json:"account_finance_specifications"`
			VendorName                          string   `json:"vendor_name"`
			Phone                               string   `json:"phone"`
			DeliveryServicePickList             string   `json:"delivery_service_pick_list"`
			PaymentMethod                       string   `json:"payment_method"`
			TaxNumber                           string   `json:"tax_number"`
			LegalForm                           string   `json:"legal_form"`
			BusinessOffice                      string   `json:"business_office"`
			RegistrationOffice                  string   `json:"registration_office"`
			TradeRegisterNumber                 string   `json:"trade_register_number"`
			MenuTaxInformation                  string   `json:"menu_tax_information"`
			OwnerEmail                          string   `json:"owner_email"`
			MarkForTestingTraining              bool     `json:"mark_for_testing_training"`
			KeyVipAccount                       bool     `json:"key_vip_account"`
			Email                               string   `json:"email"`
			BankInfo                            struct {
				Name               string `json:"name"`
				AccountNumber      string `json:"account_number"`
				Iban               string `json:"iban"`
				AccountOwner       string `json:"account_owner"`
				SwiftCodeBic       string `json:"swift_code_bic"`
				BankAccountType    string `json:"bank_account_type"`
				BankActivationDate string `json:"bank_activation_date"`
			} `json:"bank_info"`
			LegalName                        string   `json:"legal_name"`
			Type                             string   `json:"type"`
			SharedMenuGrid                   string   `json:"shared_menu_grid"`
			AccountVertical                  string   `json:"account_vertical"`
			AccountVerticalSegment           string   `json:"account_vertical_segment"`
			CourierService                   string   `json:"courier_service"`
			CourierServices                  []string `json:"courier_services"`
			CommissionBase                   string   `json:"commission_base"`
			AccountStatus                    string   `json:"account_status"`
			Source                           string   `json:"source"`
			BillingToken                     string   `json:"billing_token"`
			FoodLicenseNumber                string   `json:"food_license_number"`
			Nrc                              string   `json:"nrc"`
			SpecialCharacteristics           []string `json:"special_characteristics"`
			AccountDescription               string   `json:"account_description"`
			AccountMinimumOrderValue         int      `json:"account_minimum_order_value"`
			AccountVendorGrade               string   `json:"account_vendor_grade"`
			AccountTargetPartner             string   `json:"account_target_partner"`
			AccountKeyAccountSubCategory     string   `json:"account_key_account_sub_category"`
			AccountNumberOfOutletsPerChain   int      `json:"account_number_of_outlets_per_chain"`
			AccountMarkUp                    string   `json:"account_mark_up"`
			AccountMarkUpValue               int      `json:"account_mark_up_value"`
			AccountThirdPartyDeliveryService string   `json:"account_third_party_delivery_service"`
			SelfSetup                        string   `json:"self_setup"`
			RevenueModel                     []string `json:"revenue_model"`
			AccountTranslatedName            string   `json:"account_translated_name"`
			AccountActivityCode              string   `json:"account_activity_code"`
		} `json:"account"`
		Contact struct {
			Id          string `json:"id"`
			Salutation  string `json:"salutation"`
			FirstName   string `json:"first_name"`
			LastName    string `json:"last_name"`
			MiddleName  string `json:"middle_name"`
			Suffix      string `json:"suffix"`
			MobilePhone string `json:"mobile_phone"`
			Phone       string `json:"phone"`
			Role        string `json:"role"`
			Email       string `json:"email"`
			Hotline     string `json:"hotline"`
		} `json:"contact"`
		Address struct {
			AddressText  string  `json:"address_text"`
			Latitude     float64 `json:"latitude"`
			Longitude    float64 `json:"longitude"`
			Country      string  `json:"country"`
			City         string  `json:"city"`
			Postcode     string  `json:"postcode"`
			StreetName   string  `json:"street_name"`
			StreetNumber string  `json:"street_number"`
			Area         string  `json:"area"`
		} `json:"address"`
		InvoiceType string                                      `json:"invoice_type"`
		Giro        string                                      `json:"giro"`
		Addresses   []salesforce.SalesforceReceiverAddressModel `json:"addresses"`
		BankDetails []struct {
			AccountNumber           string `json:"account_number"`
			AccountOwner            string `json:"account_owner"`
			AccountOwnerId          string `json:"account_owner_id"`
			ActivationDate          string `json:"activation_date"`
			AccountName             string `json:"account_name"`
			AccountType             string `json:"account_type"`
			IsDirectDebit           bool   `json:"is_direct_debit"`
			BankName                string `json:"bank_name"`
			SortCode                string `json:"sort_code"`
			Iban                    string `json:"iban"`
			PaymentMethod           string `json:"payment_method"`
			SwiftCodeBic            string `json:"swift_code_bic"`
			BankOfficeNumber        string `json:"bank_office_number"`
			Status                  string `json:"status"`
			CustomerReferenceNumber string `json:"customer_reference_number"`
		} `json:"bank_details"`
		AccountCategories             []string `json:"account_categories"`
		AccountPriceRange             string   `json:"account_price_range"`
		AccountSpecialCharacteristics []string `json:"account_special_characteristics"`
		DietaryInformation            string   `json:"dietary_information"`
		InvoiceFrequency              string   `json:"invoice_frequency"`
		Contacts                      []struct {
			Id          string `json:"id"`
			Salutation  string `json:"salutation"`
			FirstName   string `json:"first_name"`
			LastName    string `json:"last_name"`
			MiddleName  string `json:"middle_name"`
			Suffix      string `json:"suffix"`
			MobilePhone string `json:"mobile_phone"`
			Phone       string `json:"phone"`
			Role        string `json:"role"`
			Email       string `json:"email"`
			Hotline     string `json:"hotline"`
		} `json:"contacts"`
	} `json:"content"`
	Timestamp string `json:"timestamp"`
}

func (handlerModel SalesforceCreateAccountReceiverHandlerModel) ToServiceModel() salesforce.SalesforceCreateAccountServiceModel {
	content := handlerModel.Content

	serviceModel := salesforce.SalesforceCreateAccountServiceModel{
		SalesforceGridId:               content.AccountGrid,
		CommercialName:                 content.Account.LegalName,
		TaxAdministration:              content.Account.RegistrationOffice,
		TaxNumber:                      content.Account.TaxNumber,
		IBAN:                           content.Account.BankInfo.Iban,
		StoreSiteName:                  content.Account.VendorName,
		ResponsibleSalesRepresentative: content.Account.OwnerEmail,
		PayloadTimestamp:               content.Timestamp,
		ReplayId:                       content.ReplayId,
	}

	// StoreType
	if content.Account.FranchiseAccount.Name == "" {
		serviceModel.StoreType = 0
	} else if content.Account.FranchiseAccount.Name == "Banabi Franchise" {
		serviceModel.StoreType = 1
	} else if content.Account.FranchiseAccount.Name == "Banabi Franchise Bakkal" {
		serviceModel.StoreType = 2
	} else {
		serviceModel.StoreType = -1
	}

	// CommercialType
	if content.Account.LegalForm == "Physical Person" {
		serviceModel.CommercialType = 0
	} else if content.Account.LegalForm == "Legal Entity" {
		serviceModel.CommercialType = 1
	} else {
		serviceModel.CommercialType = -1
	}

	helper := salesforce.SalesforceServiceModelHelper{}
	serviceModel.BillingAddress = helper.ExtractAddress(content.Addresses, "Billing Address")
	serviceModel.RestaurantAddress = helper.ExtractAddress(content.Addresses, "Restaurant Address")

	if content.Contacts != nil && len(content.Contacts) > 0 {
		serviceModel.EmailAddress = content.Contacts[0].Email
		serviceModel.MobilePhoneNumber = content.Contacts[0].MobilePhone
		serviceModel.PhoneNumber = content.Contacts[0].Phone
	}

	return serviceModel
}
