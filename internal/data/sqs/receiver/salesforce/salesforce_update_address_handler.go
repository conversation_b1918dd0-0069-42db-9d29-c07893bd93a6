package salesforce

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/salesforce"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type ISalesforceUpdateAddressReceiverHandler interface {
	Handle(ch chan error, model *receiver.ReceiverHandlerModel)
}

type SalesforceUpdateAddressReceiverHandler struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	salesforceService salesforce.ISalesforceService
}

// NewSalesforceUpdateAddressReceiverHandler
// Returns a new SalesforceUpdateAddressReceiverHandler
func NewSalesforceUpdateAddressReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	salesforceService salesforce.ISalesforceService,
) ISalesforceUpdateAddressReceiverHandler {
	handler := SalesforceUpdateAddressReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if salesforceService != nil {
		handler.salesforceService = salesforceService
	} else {
		handler.salesforceService = salesforce.NewSalesforceService(environment, loggr, validatr, cachr, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *SalesforceUpdateAddressReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel SalesforceUpdateAddressReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	serviceResponseCh := make(chan *salesforce.UpdateSalesforceAddressServiceResponse)
	defer close(serviceResponseCh)

	serviceModel := handlerModel.ToServiceModel(model.MessageId)
	go h.salesforceService.UpdateSalesforceAddress(serviceResponseCh, &serviceModel)

	serviceResponse := <-serviceResponseCh

	if serviceResponse.Error != nil {
		ch <- serviceResponse.Error
		return
	}

	ch <- nil
	return
}
