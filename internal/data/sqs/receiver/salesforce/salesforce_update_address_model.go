package salesforce

import (
	"logo-adapter/internal/service/salesforce"
	"time"
)

type SalesforceUpdateAddressReceiverHandlerModel struct {
	Version  int `json:"version"`
	Metadata struct {
		IngestionTimestamp string `json:"ingestion_timestamp"`
		Type               string `json:"type"`
		Version            int    `json:"version"`
		Source             string `json:"source"`
		Tracking           []struct {
			Name string `json:"name"`
			Id   string `json:"id"`
		} `json:"tracking"`
		ImportId          string   `json:"import_id"`
		PiiRemovalDate    string   `json:"pii_removal_date"`
		ChangedFieldNames []string `json:"changed_field_names"`
	} `json:"metadata"`
	Content struct {
		ReplayId           int       `json:"replay_id"`
		Timestamp          time.Time `json:"timestamp"`
		AccountGrid        string    `json:"account_grid"`
		RecordType         string    `json:"record_type"`
		AccountCountryCode string    `json:"account_country_code"`
		EventType          string    `json:"event_type"`
		AccountId          string    `json:"account_id"`
		Address            struct {
			AddressText  string  `json:"address_text"`
			Latitude     float64 `json:"latitude"`
			Longitude    float64 `json:"longitude"`
			Country      string  `json:"country"`
			City         string  `json:"city"`
			Postcode     string  `json:"postcode"`
			StreetName   string  `json:"street_name"`
			StreetNumber string  `json:"street_number"`
			Area         string  `json:"area"`
		} `json:"address"`
		Addresses []salesforce.SalesforceReceiverAddressModel `json:"addresses"`
	} `json:"content"`
	Timestamp string `json:"timestamp"`
}

func (handlerModel SalesforceUpdateAddressReceiverHandlerModel) ToServiceModel(messageId string) salesforce.SalesforceUpdateAddressServiceModel {
	content := handlerModel.Content

	serviceModel := salesforce.SalesforceUpdateAddressServiceModel{
		MessageId:        messageId,
		ReplayId:         content.ReplayId,
		PayloadTimestamp: content.Timestamp,
		AccountGrid:      content.AccountGrid,
	}

	helper := salesforce.SalesforceServiceModelHelper{}
	serviceModel.BillingAddress = helper.ExtractAddress(content.Addresses, "Billing Address")
	serviceModel.RestaurantAddress = helper.ExtractAddress(content.Addresses, "Restaurant Address")

	return serviceModel
}
