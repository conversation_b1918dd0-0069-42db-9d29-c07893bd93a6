package product

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/product"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type ProductStreamReceiverHandler struct {
	environment    env.IEnvironment
	loggr          logger.ILogger
	validatr       validator.IValidator
	cachr          cacher.ICacher
	productService product.IProductService
}

// NewProductStreamReceiverHandler
// Returns a new ProductStreamReceiverHandler
func NewProductStreamReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	productService product.IProductService,

) receiver.IReceiverHandler {
	handler := ProductStreamReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if productService != nil {
		handler.productService = productService
	} else {
		handler.productService = product.New(environment, loggr, validatr, cachr, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *ProductStreamReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var serviceModel product.SendProductsToLogoServiceModel
	err = json.Unmarshal(model.Data, &serviceModel)
	if err != nil {
		ch <- err
		return
	}

	serviceCh := make(chan product.SendProductsToLogoServiceResponse)
	defer close(serviceCh)
	go h.productService.SendProductsToLogo(serviceCh, serviceModel)

	serviceResponse := <-serviceCh
	if serviceResponse.Error != nil {
		ch <- serviceResponse.Error
		return
	}

	ch <- nil
}
