package warehouse

import (
	"bytes"
	"compress/zlib"
	"encoding/base64"
	"io/ioutil"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/warehouse"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	pb "github.com/deliveryhero/dh-nv-proto-golang/packages/inventory_management/movements/event/v1"
	"google.golang.org/protobuf/proto"
)

type WarehouseStockReceiverHandler struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	storeStockService warehouse.IWarehouseStockService
}

func NewWarehouseStockReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	storeStockService warehouse.IWarehouseStockService,
) receiver.IReceiverHandler {

	handler := WarehouseStockReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if storeStockService != nil {
		handler.storeStockService = storeStockService
	} else {
		handler.storeStockService = warehouse.NewWarehouseStockService(environment, loggr, validatr, cachr, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *WarehouseStockReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	movementEvent, convertErr := h.convertMessageToProto(model.Data)
	if convertErr != nil {
		ch <- convertErr
		return
	}
	err = h.validatr.ValidateStruct(movementEvent)
	if err != nil {
		ch <- err
		return
	}

	stockMovementCh := make(chan *warehouse.StockMovementServiceResponse)
	defer close(stockMovementCh)

	go h.storeStockService.StockMovement(stockMovementCh, warehouse.StockMovementServiceModel{
		Event:     *movementEvent,
		MessageId: model.MessageId,
	})
	stockAdjustmentResponse := <-stockMovementCh

	if stockAdjustmentResponse.Error != nil {
		ch <- stockAdjustmentResponse.Error
		return
	}

	ch <- nil
}

func (h *WarehouseStockReceiverHandler) convertMessageToProto(arr []byte) (*pb.MovementsEvent, error) {

	decodedBase64Model, decodeErr := base64.StdEncoding.DecodeString(string(arr))
	if decodeErr != nil {
		return nil, decodeErr
	}

	reader, readerErr := zlib.NewReader(bytes.NewReader(decodedBase64Model))
	if readerErr != nil {
		return nil, readerErr
	}
	defer reader.Close()

	body, err := ioutil.ReadAll(reader)
	if err != nil {
		return nil, err
	}

	var handlerModel pb.MovementsEvent

	errProto := proto.Unmarshal(body, &handlerModel)
	if errProto != nil {
		return nil, errProto
	}

	return &handlerModel, nil
}
