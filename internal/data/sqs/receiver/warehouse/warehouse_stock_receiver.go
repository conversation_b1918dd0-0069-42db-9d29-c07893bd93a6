package warehouse

import (
	"context"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/configuration"
	"strconv"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"go.uber.org/zap"
)

type WarehouseStockReceiver struct {
	environment                env.IEnvironment
	loggr                      logger.ILogger
	validatr                   validator.IValidator
	cachr                      cacher.ICacher
	timeout                    time.Duration
	defaultAttributes          map[string]string
	messageCacheKeyPrefix      string
	queueUrl                   string
	client                     *sqs.Client
	handler                    receiver.IReceiverHandler
	receiverName               string
	serverConfigurationService configuration.IServerConfigurationService
}

func NewWarehouseStockReceiver(environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	handler receiver.IReceiverHandler,
	serverConfigurationService configuration.IServerConfigurationService) receiver.IReceiver {

	queueUrl := environment.Get(env.StoreStockReceiverSqsQueueUrl)
	region := environment.Get(env.StoreStockReceiverSqsRegion)
	receiverName := "WarehouseStockReceiver"

	awsConfig, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(region))
	if err != nil {
		panic("Panicked while loading AWS config: " + err.Error())
	}

	receiver := WarehouseStockReceiver{
		environment:           environment,
		loggr:                 loggr.With(zap.String("receiverName", receiverName), zap.String("queueUrl", queueUrl)),
		validatr:              validatr,
		cachr:                 cachr,
		timeout:               time.Second * 5,
		defaultAttributes:     map[string]string{receiver.DummyAttribute: "dummy_attribute_value"},
		messageCacheKeyPrefix: "warehouse-warehouse-receiver-message-id",
		queueUrl:              queueUrl,
		client:                sqs.NewFromConfig(awsConfig),
		receiverName:          receiverName,
	}

	if handler != nil {
		receiver.handler = handler
	} else {
		receiver.handler = NewWarehouseStockReceiverHandler(environment, loggr, validatr, cachr, nil)
	}

	if serverConfigurationService != nil {
		receiver.serverConfigurationService = serverConfigurationService
	} else {
		receiver.serverConfigurationService = configuration.NewServerConfigurationService(environment, loggr, validatr, cachr, nil)
	}

	return &receiver
}

func (r *WarehouseStockReceiver) InitReceivers(count int) {
	r.loggr.Info(r.receiverName + "Initializing receivers.")
	for i := 0; i < count; i++ {
		go r.receive()
	}
	r.loggr.Info(r.receiverName + "Initialized " + strconv.Itoa(count) + " receivers.")
}

func (r *WarehouseStockReceiver) receive() {
	// Register another receiver if one of them fails.

	defer func() {
		if rec := recover(); rec != nil {
			r.loggr.Error(r.receiverName+"Recovered the panic. Trying to receive again.", zap.Any("panic", rec))
			go r.receive()
		}
	}()

	isOpen := r.serverConfigurationService.IsReceiverOpen(r.receiverName, enum.IsSqsStoreStockReceiverOpen)

	if !isOpen {
		r.loggr.Info(r.receiverName + " is closed by config!")
		time.Sleep(time.Second * 10)
		go r.receive()
		return
	}

	ctx := context.Background()
	output, err := r.client.ReceiveMessage(ctx, &sqs.ReceiveMessageInput{
		QueueUrl:              aws.String(r.queueUrl),
		WaitTimeSeconds:       20,
		VisibilityTimeout:     60,
		MaxNumberOfMessages:   1,
		AttributeNames:        []types.QueueAttributeName{"All"},
		MessageAttributeNames: []string{"All"},
	})

	if err != nil {
		r.loggr.Error(r.receiverName+"Failed to receive SQS message.", zap.Error(err))
		go r.receive()
		return
	}

	if output == nil || output.Messages == nil || len(output.Messages) < 1 {
		r.loggr.Info(r.receiverName + "There are no new messages. Starting to receive again.")
		go r.receive()
		return
	}

	if output.Messages != nil {
		r.eventHandler(ctx, &output.Messages[0])
	}

	go r.receive()
}

func (r *WarehouseStockReceiver) eventHandler(ctx context.Context, msg *types.Message) {
	defer func() {
		if rec := recover(); rec != nil {
			r.loggr.Error(r.receiverName+" "+*msg.MessageId+" ID message is panicked during execution in event handler.",
				zap.String("messageId", *msg.MessageId),
				zap.String("data", *msg.Body),
				zap.Any("attributes", msg.MessageAttributes),
				zap.String("receiptHandle", *msg.ReceiptHandle),
				zap.Any("panic", rec),
			)
		}
	}()

	// Cache message id for two days to prevent duplication.
	existingMessageId := r.cachr.Get(r.getMessageCacheKey(*msg.MessageId))
	if existingMessageId != nil {
		r.loggr.Error(r.receiverName+" "+*msg.MessageId+" ID message is duplicate.",
			zap.String("messageId", *msg.MessageId),
			zap.String("data", *msg.Body),
			zap.Any("attributes", msg.MessageAttributes),
			zap.String("receiptHandle", *msg.ReceiptHandle),
		)

		_, err := r.client.DeleteMessage(ctx, &sqs.DeleteMessageInput{
			QueueUrl:      aws.String(r.queueUrl),
			ReceiptHandle: msg.ReceiptHandle,
		})
		if err != nil {
			r.loggr.Error(r.receiverName+" "+*msg.MessageId+" ID message is failed to delete from queue.",
				zap.String("messageId", *msg.MessageId),
				zap.String("data", *msg.Body),
				zap.Any("attributes", msg.MessageAttributes),
				zap.String("receiptHandle", *msg.ReceiptHandle),
				zap.Error(err),
			)
		}
		return
	}

	ch := make(chan error)
	defer close(ch)
	go r.handler.Handle(ch, &receiver.ReceiverHandlerModel{
		MessageId:  *msg.MessageId,
		Data:       []byte(*msg.Body),
		Attributes: r.mapAttributes(msg.MessageAttributes),
	})

	err := <-ch
	if err != nil {
		r.loggr.Error(r.receiverName+" "+*msg.MessageId+" ID message is failed to process.",
			zap.String("messageId", *msg.MessageId),
			zap.String("data", *msg.Body),
			zap.Any("attributes", msg.MessageAttributes),
			zap.String("receiptHandle", *msg.ReceiptHandle),
			zap.Error(err),
		)
		return
	}

	// Success
	_, err = r.client.DeleteMessage(ctx, &sqs.DeleteMessageInput{
		QueueUrl:      aws.String(r.queueUrl),
		ReceiptHandle: msg.ReceiptHandle,
	})
	if err != nil {
		r.loggr.Error(r.receiverName+" "+*msg.MessageId+" ID message is failed to delete from queue.",
			zap.String("messageId", *msg.MessageId),
			zap.String("data", *msg.Body),
			zap.Any("attributes", msg.MessageAttributes),
			zap.String("receiptHandle", *msg.ReceiptHandle),
			zap.Error(err),
		)
		// do NOT return, make sure it gets cached.
	}
	r.cachr.Set(r.getMessageCacheKey(*msg.MessageId), "", time.Hour*24*1)
	r.loggr.Info(r.receiverName+" "+*msg.MessageId+" ID message is processed successfully.",
		zap.String("messageId", *msg.MessageId),
		zap.String("data", *msg.Body),
		zap.Any("attributes", msg.MessageAttributes),
		zap.String("receiptHandle", *msg.ReceiptHandle),
	)
}

// Returns a cache key for a specific message id.
func (r *WarehouseStockReceiver) getMessageCacheKey(messageId string) string {
	return r.messageCacheKeyPrefix + ":" + messageId
}

func (r *WarehouseStockReceiver) mapAttributes(attributes map[string]types.MessageAttributeValue) map[string]string {
	attr := make(map[string]string)

	for key, value := range attributes {
		attr[key] = *value.StringValue
	}

	return attr
}
