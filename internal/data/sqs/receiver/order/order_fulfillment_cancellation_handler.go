package order

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type IOrderFulfillmentCancellationReceiverHandler interface {
	Handle(ch chan error, model *receiver.ReceiverHandlerModel)
}

type OrderFulfillmentCancellationReceiverHandler struct {
	environment  env.IEnvironment
	loggr        logger.ILogger
	validatr     validator.IValidator
	cachr        cacher.ICacher
	orderService order.IOrderService
}

// NewOrderFulfillmentCancellationReceiverHandler
// Returns a new OrderFulfillmentCancellationReceiverHandler
func NewOrderFulfillmentCancellationReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	orderService order.IOrderService,
) IOrderFulfillmentCancellationReceiverHandler {
	handler := OrderFulfillmentCancellationReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if orderService != nil {
		handler.orderService = orderService
	} else {
		handler.orderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *OrderFulfillmentCancellationReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel OrderFulfillmentCancellationReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	var products []order.SendOrderFulfillmentCancellationServiceProductModel

	for _, product := range handlerModel.Data.Order.Products {

		productModel := order.SendOrderFulfillmentCancellationServiceProductModel{
			Id:          product.Id,
			ExternalId:  product.ExternalId,
			OrderItemId: product.OrderItemId,
			Sku:         product.Sku,
			Status:      product.Status,
		}
		products = append(products, productModel)
	}

	sendPandoraBeforeOrderDeliveryCancellationCh := make(chan *order.SendOrderFulfillmentCancellationServiceResponse)
	defer close(sendPandoraBeforeOrderDeliveryCancellationCh)

	go h.orderService.SendOrderFulfillmentCancellation(sendPandoraBeforeOrderDeliveryCancellationCh, &order.SendOrderFulfillmentCancellationServiceModel{
		OrderId:          handlerModel.Data.Order.ExternalId,
		TotalGrossAmount: handlerModel.Data.Order.Payment.TotalGross,
		PaymentMethod:    handlerModel.Data.Order.Payment.PaymentMethod,
		CancellationDate: handlerModel.Data.Order.CreatedAt,
		Customer: order.SendOrderFulfillmentCancellationServiceCustomerModel{
			Id:        handlerModel.Data.Order.Customer.Id,
			FirstName: handlerModel.Data.Order.Customer.FirstName,
			LastName:  handlerModel.Data.Order.Customer.LastName,
		},
		Products:    products,
		WarehouseId: handlerModel.WarehouseId,
	})

	sendPandoraBeforeOrderDeliveryCancellationResponse := <-sendPandoraBeforeOrderDeliveryCancellationCh
	if sendPandoraBeforeOrderDeliveryCancellationResponse.Error != nil {
		ch <- sendPandoraBeforeOrderDeliveryCancellationResponse.Error
		return
	}

	ch <- nil
	return
}
