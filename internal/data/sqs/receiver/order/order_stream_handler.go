package order

import (
	"encoding/json"
	"logo-adapter/internal/data/proxy/datafridge"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type OrderStreamReceiverHandler struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	OrderService          order.IOrderService
	dataFridgeVendorProxy datafridge.IVendorProxy
}

// NewOrderStreamReceiverHandler
// Returns a new OrderStreamReceiverHandler
func NewOrderStreamReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	OrderService order.IOrderService,
	dataFridgeVendorProxy datafridge.IVendorProxy,
) receiver.IOrderStreamReceiverHandler {
	handler := OrderStreamReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if OrderService != nil {
		handler.OrderService = OrderService
	} else {
		handler.OrderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}

	if dataFridgeVendorProxy != nil {
		handler.dataFridgeVendorProxy = dataFridgeVendorProxy
	} else {
		handler.dataFridgeVendorProxy = datafridge.NewVendorProxy(environment, loggr, validatr, cachr)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *OrderStreamReceiverHandler) Handle(ch chan receiver.OrderStreamReceiverResponseModel, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	var handlerModel OrderStreamReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	if handlerModel.Content.Customer.Payment.TotalOrderValue == 0 {
		h.loggr.Warn("OrderStreamReceiver " + handlerModel.Content.OrderId +
			" order total value is 0.")
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error:               nil,
			IsUnsupportDataType: true,
		}
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	//check if payment method is offline
	paymentMethodName, err := h.OrderService.GetPaymentMethodMap(handlerModel.Content.Customer.Payment.PaymentMethod)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	_, paymentTypeId, err := h.OrderService.GetPaymentMethodIds(paymentMethodName)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	if paymentTypeId != enum.PaymentTypeOffline {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error:               nil,
			IsUnsupportDataType: true,
		}
		return
	}

	//get vendor data
	dfProxyCh := make(chan *datafridge.GetVendorProxyResponse)
	defer close(dfProxyCh)
	go h.dataFridgeVendorProxy.GetVendor(
		dfProxyCh, &datafridge.GetVendorProxyModel{
			GlobalEntityId: "YS_TR",
			VendorId:       handlerModel.Content.Vendor.VendorId,
		},
	)

	dfProxyResponse := <-dfProxyCh
	if dfProxyResponse.Error != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: dfProxyResponse.Error,
		}
		return
	}

	//remove market order by vertical type
	if dfProxyResponse.Data.Vendor.VerticalType == "darkstores" {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error:               nil,
			IsUnsupportDataType: true,
		}
		return
	}

	vendorType := enum.MahalleVendor
	if dfProxyResponse.Data.Vendor.VerticalType == "restaurants" {
		if handlerModel.Content.Delivery.Provider != "platform_delivery" {
			ch <- receiver.OrderStreamReceiverResponseModel{
				Error:               nil,
				IsUnsupportDataType: true,
			}
			return
		}
		vendorType = enum.ValeVendor
	}

	//Cache mahalle-vale orders for one day
	h.cachr.Set("MahalleVale_"+handlerModel.Content.OrderId, "", 24*time.Hour)

	sendYsOrderCh := make(chan *order.SendOfflineYsOrderToLogisticServiceResponse)
	defer close(sendYsOrderCh)

	go h.OrderService.SendOfflineYsOrderToLogistic(sendYsOrderCh, &order.SendOfflineYsOrderToLogisticServiceModel{
		OrderType:        handlerModel.Metadata.OrderType,
		OrderId:          handlerModel.Content.OrderId,
		OrderCreatedDate: handlerModel.Timestamp,
		VendorId:         handlerModel.Content.Vendor.VendorId,
		VendorType:       vendorType,
		StoreName:        handlerModel.Content.Vendor.VendorName,
		CustomerId:       handlerModel.Content.Customer.CustomerId,
		FirstName:        handlerModel.Content.Customer.Profile.FirstName,
		LastName:         handlerModel.Content.Customer.Profile.LastName,
		PaymentMethod:    paymentMethodName,
		Amount:           handlerModel.Content.Customer.Payment.TotalOrderValue,
	})

	sendOrderResponse := <-sendYsOrderCh

	if sendOrderResponse.Error != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: sendOrderResponse.Error,
		}
		return
	}

	ch <- receiver.OrderStreamReceiverResponseModel{}
	return
}
