package order

import (
	"encoding/json"
	"errors"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
)

type PandoraBillingSoftposReceiverHandler struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	pandoraBillingService order.IPandoraBillingService
	orderPaymentService   order.IOrderPaymentService
}

var pandoraBillingSoftposMsgAlreadyReceivedErr = errors.New("the_order_of_softpos_has_already_been_received")

// NewPandoraBillingSoftposReceiverHandler
// Returns a new PandoraBillingSoftposReceiverHandler
func NewPandoraBillingSoftposReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.I<PERSON>acher,
	pandoraBillingService order.IPandoraBillingService,
	orderPaymentService order.IOrderPaymentService,
) receiver.IReceiverHandler {
	handler := PandoraBillingSoftposReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if pandoraBillingService != nil {
		handler.pandoraBillingService = pandoraBillingService
	} else {
		handler.pandoraBillingService = order.NewPandoraBillingService(environment, loggr, validatr, cachr, nil, nil)
	}

	if orderPaymentService != nil {
		handler.orderPaymentService = orderPaymentService
	} else {
		handler.orderPaymentService = order.NewOrderPaymentService(environment, loggr, validatr, cachr, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *PandoraBillingSoftposReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var pdBillingSoftposMessage order.PandoraBillingSoftposMessage
	err = json.Unmarshal(model.Data, &pdBillingSoftposMessage)
	if err != nil {
		ch <- err
		return
	}

	modelErr := h.validatr.ValidateStruct(pdBillingSoftposMessage)
	if modelErr != nil {
		ch <- modelErr
		return
	}

	//Caching to handle the message on one machine so that data are not duplicated
	cacheValue := h.cachr.Get(pdBillingSoftposMessage.TrackingNumber + "_softpos")
	if cacheValue != nil {
		ch <- pandoraBillingSoftposMsgAlreadyReceivedErr
		return
	}
	h.cachr.Set(pdBillingSoftposMessage.TrackingNumber+"_softpos", "", 60*time.Second)

	pdBillingMessageExistsCh := make(chan *order.GetPandoraBillingMessageExistsServiceResponse)
	defer close(pdBillingMessageExistsCh)
	go h.pandoraBillingService.GetPandoraBillingSoftposMessageExists(pdBillingMessageExistsCh, &order.GetPandoraBillingSoftposMessageExistsServiceModel{
		OrderId: pdBillingSoftposMessage.TrackingNumber,
	})

	pdBillingMessageExistsResponse := <-pdBillingMessageExistsCh
	if pdBillingMessageExistsResponse.Error != nil {
		ch <- pdBillingMessageExistsResponse.Error
		return
	}

	if pdBillingMessageExistsResponse.Exists {
		h.loggr.Info("Pandora billing softpos message has already processed !", zap.Any("Message", pdBillingSoftposMessage))
		ch <- nil
		return
	}

	paymentList := make([]order.OfflinePaymentListServiceModel, len(pdBillingSoftposMessage.PaymentList))
	for i, x := range pdBillingSoftposMessage.PaymentList {

		paymentList[i] = order.OfflinePaymentListServiceModel{
			SlipNo:               x.SlipNo,
			RelatedTransactionId: 1, //not used for softpos
			Price:                x.Price,
			TransactionDate:      x.TransactionDate,
			PaymentMethod:        x.PaymentMethod,
			CollectedBy:          x.CollectedBy,
			CollectedUserName:    x.CollectedUserName,
			BankCode:             x.BankCode,
			BankAuthCode:         x.BankAuthCode,
			BankStan:             x.BankStan,
			SlipTerminalNumber:   x.SlipTerminalNumber,
		}
	}

	orderOfflinePaymentCh := make(chan *order.SendSoftposOrderOfflinePaymentServiceResponse)
	defer close(orderOfflinePaymentCh)
	go h.orderPaymentService.SendSoftposOrderOfflinePayment(
		orderOfflinePaymentCh, &order.SendOrderOfflinePaymentServiceModel{
			OrderOfflinePaymentServiceModel: order.OrderOfflinePaymentServiceModel{
				PaymentList:    paymentList,
				TrackingNumber: pdBillingSoftposMessage.TrackingNumber,
				TransactionId:  1, //not used for softpos
				TargetAmount:   pdBillingSoftposMessage.TargetAmount,
			},
		},
	)

	orderOfflinePaymentResponse := <-orderOfflinePaymentCh
	if orderOfflinePaymentResponse.Error != nil {
		ch <- orderOfflinePaymentResponse.Error
		return
	}

	saveMessageCh := make(chan *order.SavePandoraBillingMessageServiceResponse)
	defer close(saveMessageCh)

	marshaledLogoModel, _ := json.Marshal(orderOfflinePaymentResponse.LogoMessageModel)
	go h.pandoraBillingService.SavePandoraBillingSoftposMessage(
		saveMessageCh, &order.SavePandoraBillingSoftposMessageServiceModel{
			Message:          pdBillingSoftposMessage,
			LogoMessageId:    model.MessageId,
			LogoOrderDetails: string(marshaledLogoModel),
		},
	)

	saveMessageResponse := <-saveMessageCh
	if saveMessageResponse.Error != nil {
		ch <- saveMessageResponse.Error
		return
	}

	ch <- nil
}
