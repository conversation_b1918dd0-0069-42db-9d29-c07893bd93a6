package order

import "time"

type OrderFulfillmentReceiverHandlerModel struct {
	Data        OrderFulfillmentDataModel `validate:"required" json:"data"`
	Event       string                    `validate:"required" json:"event"`
	Timestamp   time.Time                 `validate:"required" json:"timestamp"`
	WarehouseId string                    `validate:"required" json:"warehouse_id"`
}

type OrderFulfillmentDataModel struct {
	Discounts           []OrderFulfillmentDiscountModel   `validate:"required" json:"discounts"`
	Fees                []OrderFulfillmentFeeModel        `validate:"required" json:"fees"`
	Items               []OrderFulfillmentItemModel       `validate:"required" json:"items"`
	Customer            OrderFulfillmentCustomerModel     `validate:"required" json:"customer"`
	ExternalId          string                            `validate:"required" json:"external_id"`
	PickerId            int                               `validate:"gte=0" json:"picker_id"`
	PaymentMethod       string                            `validate:"required" json:"payment_method"`
	OriginalTotalNet    float64                           `validate:"gte=0" json:"original_total_net"`
	OriginalTotalGross  float64                           `validate:"gte=0" json:"original_total_gross"`
	ConfirmedTotalGross float64                           `validate:"gte=0" json:"confirmed_total_gross"`
	OrderCreatedAt      time.Time                         `validate:"required" json:"order_created_at"`
	OrderCheckedOutAt   time.Time                         `validate:"required" json:"order_checked_out_at"`
	RiderName           string                            `json:"rider_name"`
	YemekParams         *OrderFulfillmentYemekParamsModel `json:"yemek_params"`
	IsModified          bool                              `json:"is_modified"`
}

type OrderFulfillmentDiscountModel struct {
	Name  string  `validate:"required" json:"name"`
	Value float64 `validate:"gte=0" json:"value"`
}

type OrderFulfillmentFeeModel struct {
	Name  string  `validate:"required" json:"name"`
	Value float64 `validate:"gte=0" json:"value"`
}

type OrderFulfillmentItemModel struct {
	ExternalId       string                                `validate:"required" json:"external_id"`
	GlobalCatalogId  string                                `validate:"required" json:"global_catalog_id"`
	OrderItemId      string                                `validate:"required" json:"order_item_id"`
	SKU              string                                `validate:"required" json:"sku"`
	Name             string                                `validate:"required" json:"name"`
	Status           string                                `validate:"required" json:"status"`
	OriginalPricing  OrderFulfillmentPricingModel          `validate:"required" json:"original_pricing"`
	CollectedPricing OrderFulfillmentCollectedPricingModel `validate:"required" json:"pricing"`
}

type OrderFulfillmentCollectedPricingModel struct {
	Type       string  `validate:"required" json:"type"`
	UnitPrice  float64 `validate:"gt=0" json:"unit_price"`
	TotalPrice float64 `validate:"gt=0" json:"total_price"`
	Quantity   float32 `validate:"gt=0" json:"quantity"`
}

type OrderFulfillmentPricingModel struct {
	Type          string  `validate:"required" json:"type"`
	UnitPrice     float64 `validate:"gt=0" json:"unit_price"`
	TotalPrice    float64 `validate:"gt=0" json:"total_price"`
	Quantity      float64 `validate:"gt=0" json:"quantity"`
	UnitListPrice float64 `validate:"gt=0" json:"unit_list_price"`
}

type OrderFulfillmentCustomerModel struct {
	Id        string `validate:"required" json:"id"`
	Phone     string `validate:"required" json:"phone"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

type OrderFulfillmentYemekParamsModel struct {
	BankCode            string  `json:"bank_code"`
	Email               string  `json:"email"`
	TipAmount           float64 `validate:"gte=0" json:"tip_amount"`
	AddressLine         string  `json:"address_line"`
	RegionName          string  `json:"region_name"`
	CityName            string  `json:"city_name"`
	CollectFromCustomer float64 `validate:"gte=0" json:"collect_from_customer"`
}
