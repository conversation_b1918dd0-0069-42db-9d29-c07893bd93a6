package order

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type EttnReceiverHandler struct {
	environment  env.IEnvironment
	loggr        logger.ILogger
	validatr     validator.IValidator
	cachr        cacher.ICacher
	OrderService order.IOrderService
}

// NewEttnReceiverHandler
// Returns a new EttnReceiverHandler
func NewEttnReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	OrderService order.IOrderService,
) receiver.IReceiverHandler {
	handler := EttnReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if OrderService != nil {
		handler.OrderService = OrderService
	} else {
		handler.OrderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *EttnReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var ettnModel order.SendEttnToPandoraModel
	err = json.Unmarshal(model.Data, &ettnModel)
	if err != nil {
		ch <- err
		return
	}

	sendEttnToPandoraCh := make(chan *order.SendEttnToPandoraResponse)
	defer close(sendEttnToPandoraCh)
	go h.OrderService.SendEttnToPandora(sendEttnToPandoraCh, &ettnModel)

	serviceResponse := <-sendEttnToPandoraCh
	if serviceResponse.Error != nil {
		ch <- serviceResponse.Error
		return
	}

	ch <- nil
}
