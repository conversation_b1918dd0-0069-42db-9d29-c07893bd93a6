package order

import "time"

type OrderFulfillmentCancellationReceiverHandlerModel struct {
	Data        OrderFulfillmentCancellationDataModel `validate:"required" json:"data"`
	Event       string                                `validate:"required" json:"event"`
	Timestamp   time.Time                             `validate:"required" json:"timestamp"`
	WarehouseId string                                `validate:"required" json:"warehouse_id"`
}

type OrderFulfillmentCancellationDataModel struct {
	Owner        string                                 `json:"owner"`
	Reason       string                                 `json:"reason"`
	Source       string                                 `json:"source"`
	Stage        string                                 `json:"stage"`
	Comment      string                                 `json:"comment"`
	PostCheckout bool                                   `json:"postCheckout"`
	Order        OrderFulfillmentOrderCancellationModel `validate:"required" json:"order"`
}
type OrderFulfillmentOrderCancellationModel struct {
	Id                    string                                     `json:"id"`
	ExternalId            string                                     `validate:"required" json:"externalId"`
	ShortCode             string                                     `json:"shortCode"`
	GlobalEntityId        string                                     `json:"globalEntityId"`
	PlacedAt              string                                     `json:"placedAt"`
	PickupAt              string                                     `json:"pickupAt"`
	PromisedFor           string                                     `json:"promisedFor"`
	Customer              OrderFulfillmentCancellationCustomerModel  `json:"customer"`
	Payment               OrderFulfillmentCancellationPaymentModel   `validate:"required" json:"payment"`
	Products              []OrderFulfillmentCancellationProductModel `json:"products"`
	Comment               string                                     `json:"comment"`
	Status                string                                     `json:"status"`
	AdminStatus           string                                     `json:"adminStatus"`
	VendorId              string                                     `json:"vendorId"`
	VendorName            string                                     `json:"vendorName"`
	TransportType         string                                     `json:"transportType"`
	VendorPlatformId      string                                     `json:"vendorPlatformId"`
	PrescriptionPhotoUrls []string                                   `json:"prescriptionPhotoUrls"`
	CreatedAt             time.Time                                  `validate:"required" json:"createdAt"`
	IsTest                bool                                       `json:"isTest"`
	IsDryRun              bool                                       `json:"isDryRun"`
	IsPreorder            bool                                       `json:"isPreorder"`
}

type OrderFulfillmentCancellationCustomerModel struct {
	Id              string                                                   `json:"id"`
	NationalId      string                                                   `json:"nationalId"`
	FirstName       string                                                   `json:"firstName"`
	LastName        string                                                   `json:"lastName"`
	Phone           string                                                   `json:"phone"`
	DeliveryAddress OrderFulfillmentCancellationCustomerDeliveryAddressModel `json:"deliveryAddress"`
}

type OrderFulfillmentCancellationCustomerDeliveryAddressModel struct {
	Street    string  `json:"street"`
	Number    string  `json:"number"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}
type OrderFulfillmentCancellationPaymentModel struct {
	Currency        string                                      `json:"currency"`
	TotalNet        float64                                     `validate:"gte=0" json:"totalNet"`
	TotalGross      float64                                     `validate:"gte=0" json:"totalGross"`
	VatPercent      float64                                     `validate:"gte=0" json:"vatPercent"`
	VatAmount       float64                                     `validate:"gte=0" json:"vatAmount"`
	WalletAmount    float64                                     `json:"walletAmount"`
	Discounts       []OrderFulfillmentCancellationDiscountModel `json:"discounts"`
	DiscountsTotal  float64                                     `json:"discountsTotal"`
	Fees            []OrderFulfillmentCancellationFeeModel      `json:"fees"`
	FeesTotal       float64                                     `json:"feesTotal"`
	PaymentMethod   string                                      `validate:"required" json:"method"`
	PaymentType     string                                      `validate:"required" json:"type"`
	CollectAtPickup float64                                     `json:"collectAtPickup"`
}

type OrderFulfillmentCancellationDiscountModel struct {
	Name  string  `validate:"required" json:"name"`
	Value float64 `validate:"gte=0" json:"value"`
}

type OrderFulfillmentCancellationFeeModel struct {
	Name  string  `validate:"required" json:"name"`
	Value float64 `validate:"gte=0" json:"value"`
}

type OrderFulfillmentCancellationProductModel struct {
	Id              string                                   `validate:"required" json:"id"`
	ExternalId      string                                   `validate:"required" json:"externalId"`
	OrderItemId     string                                   `validate:"required" json:"orderItemId"`
	Sku             string                                   `validate:"required" json:"sku"`
	Barcodes        []string                                 `validate:"required" json:"barcodes"`
	Name            string                                   `validate:"required" json:"name"`
	CategoryId      string                                   `validate:"required" json:"categoryId"`
	CategoryName    string                                   `json:"categoryName"`
	ImageURL        string                                   `json:"imageUrl"`
	Pricing         OrderFulfillmentCancellationPricingModel `validate:"required" json:"pricing"`
	Description     string                                   `json:"description"`
	Comment         string                                   `json:"comment"`
	PickupPriority  int64                                    `json:"pickupPriority"`
	Status          string                                   `json:"status"`
	GlobalCatalogId string                                   `json:"global_catalog_id"`
	OriginalPricing OrderFulfillmentCancellationPricingModel `json:"original_pricing"`
}

type OrderFulfillmentCancellationPricingModel struct {
	PricingType string  `validate:"required" json:"pricingType"`
	UnitPrice   float64 `validate:"gte=0" json:"unitPrice"`
	VatPercent  float64 `validate:"gte=0" json:"vatPercent"`
	TotalPrice  float64 `validate:"gte=0" json:"totalPrice"`
	Quantity    float64 `validate:"gt=0" json:"quantity"`
	ListPrice   float64 `validate:"gte=0" json:"listPrice"`
}
