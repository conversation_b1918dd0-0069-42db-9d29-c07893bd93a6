package order

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type OrderFulfillmentReceiverHandler struct {
	environment  env.IEnvironment
	loggr        logger.ILogger
	validatr     validator.IValidator
	cachr        cacher.ICacher
	OrderService order.IOrderService
}

// NewOrderFulfillmentReceiverHandler
// Returns a new OrderFulfillmentReceiverHandler
func NewOrderFulfillmentReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	OrderService order.IOrderService,
) receiver.IReceiverHandler {
	handler := OrderFulfillmentReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if OrderService != nil {
		handler.OrderService = OrderService
	} else {
		handler.OrderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *OrderFulfillmentReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel OrderFulfillmentReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	discounts := make(map[string]float64, len(handlerModel.Data.Discounts))
	for _, x := range handlerModel.Data.Discounts {
		discounts[x.Name] = x.Value
	}

	fees := make(map[string]float64, len(handlerModel.Data.Fees))
	for _, x := range handlerModel.Data.Fees {
		fees[x.Name] = x.Value
	}

	items := make([]order.SendOrderServiceItemModel, len(handlerModel.Data.Items))
	for i, x := range handlerModel.Data.Items {
		items[i] = order.SendOrderServiceItemModel{
			ExternalId:      x.ExternalId,
			GlobalCatalogId: x.GlobalCatalogId,
			OrderItemId:     x.OrderItemId,
			SKU:             x.SKU,
			Name:            x.Name,
			Status:          x.Status,
			OriginalPricing: order.SendOrderServicePricingModel{
				Type:          x.OriginalPricing.Type,
				UnitPrice:     x.OriginalPricing.UnitPrice,
				TotalPrice:    x.OriginalPricing.TotalPrice,
				Quantity:      int(x.OriginalPricing.Quantity),
				UnitListPrice: x.OriginalPricing.UnitListPrice,
			},
			CollectedPricing: order.SendOrderServiceCollectedPricingModel{
				Type:       x.CollectedPricing.Type,
				UnitPrice:  x.CollectedPricing.UnitPrice,
				TotalPrice: x.CollectedPricing.TotalPrice,
				Quantity:   int(x.CollectedPricing.Quantity),
			},
		}
	}

	sendOrderCh := make(chan *order.SendOrderFulfillmentServiceResponse)
	defer close(sendOrderCh)

	var yemekParams *order.SendOrderServiceYemekParamsModel
	if handlerModel.Data.YemekParams != nil {
		yemekParams = &order.SendOrderServiceYemekParamsModel{
			BankCode:            handlerModel.Data.YemekParams.BankCode,
			Email:               handlerModel.Data.YemekParams.Email,
			TipAmount:           handlerModel.Data.YemekParams.TipAmount,
			AddressLine:         handlerModel.Data.YemekParams.AddressLine,
			RegionName:          handlerModel.Data.YemekParams.RegionName,
			CityName:            handlerModel.Data.YemekParams.CityName,
			CollectFromCustomer: handlerModel.Data.YemekParams.CollectFromCustomer,
		}
	}

	go h.OrderService.SendOrderFulfillment(sendOrderCh, &order.SendOrderFulfillmentServiceModel{
		MessageId:           model.MessageId,
		ExternalId:          handlerModel.Data.ExternalId,
		WarehouseId:         handlerModel.WarehouseId,
		PaymentMethod:       handlerModel.Data.PaymentMethod,
		OriginalTotalNet:    handlerModel.Data.OriginalTotalNet,
		OriginalTotalGross:  handlerModel.Data.OriginalTotalGross,
		ConfirmedTotalGross: handlerModel.Data.ConfirmedTotalGross,
		Discounts:           discounts,
		Fees:                fees,
		Items:               items,
		Customer: order.SendOrderServiceCustomerModel{
			Id:        handlerModel.Data.Customer.Id,
			Phone:     handlerModel.Data.Customer.Phone,
			FirstName: handlerModel.Data.Customer.FirstName,
			LastName:  handlerModel.Data.Customer.LastName,
		},
		OrderCreatedAt:    handlerModel.Data.OrderCreatedAt,
		OrderCheckedOutAt: handlerModel.Data.OrderCheckedOutAt,
		RiderName:         handlerModel.Data.RiderName,
		YemekParams:       yemekParams,
		Timestamp:         handlerModel.Timestamp,
		IsModified:        handlerModel.Data.IsModified,
	},
	)

	sendOrderResponse := <-sendOrderCh

	if sendOrderResponse.Error != nil {
		ch <- sendOrderResponse.Error
		return
	}

	ch <- nil
	return
}
