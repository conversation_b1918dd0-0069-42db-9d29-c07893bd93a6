package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"
	"strings"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
)

type PandoraBillingReceiverHandler struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	PandoraBillingService order.IPandoraBillingService
	OrderService          order.IOrderService
}

var pandoraBillingMsgAlreadyReceivedErr = errors.New("the_order_has_already_been_received")

// NewPandoraBillingReceiverHandler
// Returns a new PandoraBillingReceiverHandler
func NewPandoraBillingReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.<PERSON><PERSON><PERSON>,
	PandoraBillingService order.IPandoraBillingService,
	OrderService order.IOrderService,
) receiver.IReceiverHandler {
	handler := PandoraBillingReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if PandoraBillingService != nil {
		handler.PandoraBillingService = PandoraBillingService
	} else {
		handler.PandoraBillingService = order.NewPandoraBillingService(environment, loggr, validatr, cachr, nil, nil)
	}

	if OrderService != nil {
		handler.OrderService = OrderService
	} else {
		handler.OrderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *PandoraBillingReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var pdBillingMessage order.PandoraBillingMessage
	err = json.Unmarshal(model.Data, &pdBillingMessage)
	if err != nil {
		ch <- err
		return
	}

	h.validatr.RegisterStructLevelValidation(func(sl validator.StructLevelType) {
		msg := sl.Current().Interface().(order.PandoraBillingMessage)

		if msg.Status == "created" && msg.TotalAmount < 0 {
			sl.ReportError(msg.TotalAmount, "totalAmount", "totalAmount", "totalAmountCreatedStatus", "")
		}
		if msg.Status == "canceled" && msg.TotalAmount > 0 {
			sl.ReportError(msg.TotalAmount, "totalAmount", "totalAmount", "totalAmountCanceledStatus", "")
		}

	}, order.PandoraBillingMessage{})

	//Caching to handle the message on one machine so that data are not duplicated
	cacheValue := h.cachr.Get(pdBillingMessage.OrderId + "_" + pdBillingMessage.Status)
	if cacheValue != nil {
		ch <- pandoraBillingMsgAlreadyReceivedErr
		return
	}
	h.cachr.Set(pdBillingMessage.OrderId+"_"+pdBillingMessage.Status, "", 60*time.Second)

	pdBillingMessageExistsCh := make(chan *order.GetPandoraBillingMessageExistsServiceResponse)
	defer close(pdBillingMessageExistsCh)
	go h.PandoraBillingService.GetPandoraBillingMessageExists(pdBillingMessageExistsCh, &order.GetPandoraBillingMessageExistsServiceModel{
		OrderId: pdBillingMessage.OrderId,
		Status:  pdBillingMessage.Status,
	})

	pdBillingMessageExistsResponse := <-pdBillingMessageExistsCh
	if pdBillingMessageExistsResponse.Error != nil {
		ch <- pdBillingMessageExistsResponse.Error
		return
	}

	if pdBillingMessageExistsResponse.Exists {
		h.loggr.Info("Pandora billing message has already processed !", zap.Any("Message", pdBillingMessage))
		ch <- nil
		return
	}

	switch strings.ToLower(pdBillingMessage.Status) {
	case "created":
		sendPandoraOrderCh := make(chan *order.SendOrderServiceResponse)
		defer close(sendPandoraOrderCh)

		go h.OrderService.SendOrder(sendPandoraOrderCh, &order.SendOrderServiceModel{
			Message:   pdBillingMessage,
			MessageId: model.MessageId,
		})

		sendPandoraOrderResponse := <-sendPandoraOrderCh
		if sendPandoraOrderResponse.Error != nil {
			ch <- sendPandoraOrderResponse.Error
			return
		}
	case "canceled":
		sendPandoraOrderCancellationCh := make(chan *order.SendOrderCancellationServiceResponse)
		defer close(sendPandoraOrderCancellationCh)

		go h.OrderService.SendOrderCancellation(sendPandoraOrderCancellationCh, &order.SendOrderCancellationServiceModel{
			Message:   pdBillingMessage,
			MessageId: model.MessageId,
		})

		sendPandoraOrderCancellationResponse := <-sendPandoraOrderCancellationCh
		if sendPandoraOrderCancellationResponse.Error != nil {
			ch <- sendPandoraOrderCancellationResponse.Error
			return
		}
	default:
		ch <- fmt.Errorf("unexpected status : (%s), message : (%#v)", pdBillingMessage.Status, pdBillingMessage)
	}

	ch <- nil
}
