package order

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"
	"logo-adapter/internal/util/helper"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type OrderOfflinePaymentReceiverHandler struct {
	environment         env.IEnvironment
	loggr               logger.ILogger
	validatr            validator.IValidator
	cachr               cacher.ICacher
	orderPaymentService order.IOrderPaymentService
}

// NewOrderOfflinePaymentReceiverHandler
// Returns a new OrderOfflinePaymentReceiverHandler
func NewOrderOfflinePaymentReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	orderPaymentService order.IOrderPaymentService,
) receiver.IReceiverHandler {
	handler := OrderOfflinePaymentReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if orderPaymentService != nil {
		handler.orderPaymentService = orderPaymentService
	} else {
		handler.orderPaymentService = order.NewOrderPaymentService(environment, loggr, validatr, cachr, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *OrderOfflinePaymentReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel OrderOfflinePaymentReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	paymentList := make([]order.OfflinePaymentListServiceModel, len(handlerModel.PaymentList))
	for i, x := range handlerModel.PaymentList {

		transactionDate, err := helper.ToTimeFormat(x.TransactionDate)
		if err != nil {
			ch <- err
			return
		}

		paymentList[i] = order.OfflinePaymentListServiceModel{
			SlipNo:               x.SlipNo,
			RelatedTransactionId: x.RelatedTransactionId,
			Price:                x.Price,
			TransactionDate:      transactionDate,
			PaymentMethod:        x.PaymentMethod,
			CollectedBy:          x.CollectedBy,
			CollectedUserName:    x.CollectedUserName,
			BankCode:             x.BankCode,
			BankAuthCode:         x.BankAuthCode,
			BankStan:             x.BankStan,
			SlipTerminalNumber:   x.SlipTerminalNumber,
		}
	}

	orderOfflinePaymentCh := make(chan *order.SendOrderOfflinePaymentServiceResponse)
	defer close(orderOfflinePaymentCh)
	go h.orderPaymentService.SendOrderOfflinePayment(
		orderOfflinePaymentCh, &order.SendOrderOfflinePaymentServiceModel{
			OrderOfflinePaymentServiceModel: order.OrderOfflinePaymentServiceModel{
				PaymentList:    paymentList,
				TrackingNumber: handlerModel.TrackingNumber,
				TransactionId:  handlerModel.TransactionId,
				TargetAmount:   handlerModel.TargetAmount,
			},
		},
	)

	orderOfflinePaymentResponse := <-orderOfflinePaymentCh
	if orderOfflinePaymentResponse.Error != nil {
		ch <- orderOfflinePaymentResponse.Error
		return
	}

	ch <- nil
	return
}
