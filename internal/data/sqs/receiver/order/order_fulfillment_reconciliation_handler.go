package order

import (
	"encoding/json"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"
)

type OrderFulfillmentReconciliationReceiverHandler struct {
	environment  env.IEnvironment
	loggr        logger.ILogger
	validatr     validator.IValidator
	cachr        cacher.ICacher
	orderService order.IOrderService
}

// NewOrderFulfillmentReconciliationReceiverHandler
// Returns a new OrderFulfillmentReconciliationReceiverHandler
func NewOrderFulfillmentReconciliationReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	orderService order.IOrderService,
) receiver.IReceiverHandler {
	handler := OrderFulfillmentReconciliationReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if orderService != nil {
		handler.orderService = orderService
	} else {
		handler.orderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *OrderFulfillmentReconciliationReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel OrderFulfillmentReconciliationReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	paymentMethodName, err := h.orderService.GetPaymentMethodMap(handlerModel.Data.Vendor.Payment.PaymentType)
	if err != nil {
		ch <- err
		return
	}
	_, paymentTypeId, err := h.orderService.GetPaymentMethodIds(paymentMethodName)
	if err != nil {
		ch <- err
		return
	}

	orderId := model.Attributes["platform_order_id"]

	if paymentTypeId == enum.PaymentTypeOnline && handlerModel.Data.Vendor.ExtraParameters.YemekParams == "" {
		h.loggr.Warn("Yemek params is empty for online payment. TrackingNumber: " + orderId)
		ch <- nil
		return
	}

	var yemekParamsModel OrderFulfillmentReconciliationYemekParamsModel
	err = json.Unmarshal([]byte(handlerModel.Data.Vendor.ExtraParameters.YemekParams), &yemekParamsModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(yemekParamsModel)
	if err != nil {
		ch <- err
		return
	}

	var collectFromCustomer float64 = 0
	if yemekParamsModel.CollectFromCustomer != "" {
		collectFromCustomer, err = strconv.ParseFloat(yemekParamsModel.CollectFromCustomer, 64)
		if err != nil {
			ch <- err
			return
		}
	}

	orderFulfillmentReconciliationCh := make(chan *order.SendOrderFulfillmentReconciliationServiceResponse)
	defer close(orderFulfillmentReconciliationCh)
	go h.orderService.SendOrderFulfillmentReconciliation(
		orderFulfillmentReconciliationCh, &order.SendOrderFulfillmentReconciliationServiceModel{
			OrderId:             model.Attributes["platform_order_id"],
			PaymentMethod:       handlerModel.Data.Vendor.Payment.PaymentTypeDetails,
			CollectFromCustomer: collectFromCustomer,
			WarehouseId:         handlerModel.WarehouseId,
		},
	)

	orderFulfillmentReconciliationResponse := <-orderFulfillmentReconciliationCh
	if orderFulfillmentReconciliationResponse.Error != nil {
		ch <- orderFulfillmentReconciliationResponse.Error
		return
	}

	ch <- nil
	return
}
