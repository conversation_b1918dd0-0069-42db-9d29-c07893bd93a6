package order

import "time"

type OrderStreamReceiverHandlerModel struct {
	Metadata  OrderMetadata `json:"metadata" validate:"required"`
	Content   OrderContent  `json:"content" validate:"required"`
	Timestamp time.Time     `json:"timestamp" validate:"required"`
}

type OrderMetadata struct {
	OrderType string `json:"type" validate:"required"`
}

type OrderContent struct {
	OrderId  string   `json:"order_id" validate:"required"`
	Vendor   Vendor   `json:"vendor" validate:"required"`
	Customer Customer `json:"customer" validate:"required"`
	Delivery Delivery `json:"delivery" validate:"required"`
}

type Vendor struct {
	VendorId   string `json:"id" validate:"required"`
	VendorName string `json:"name" validate:"required"`
}

type Customer struct {
	CustomerId string  `json:"customer_id" validate:"required"`
	Payment    Payment `json:"payment" validate:"required"`
	Profile    Profile `json:"profile" validate:"required"`
}

type Payment struct {
	PaymentMethod   string  `json:"payment_method" validate:"required"`
	TotalOrderValue float64 `json:"total_order_value" validate:"required"`
}

type Profile struct {
	FirstName string `json:"first_name" validate:"required"`
	LastName  string `json:"last_name" validate:"required"`
}

type Delivery struct {
	Provider string `json:"provider" validate:"required"`
}

//OrderStatusCancellation
type OrderStatusCancellationStreamReceiverHandlerModel struct {
	OrderStatusMetadata OrderStatusMetadata `json:"metadata" validate:"required"`
	OrderStatusContent  OrderStatusContent  `json:"content" validate:"required"`
	Timestamp           time.Time           `json:"timestamp" validate:"required"`
}

type OrderStatusMetadata struct {
	OrderType string `json:"type" validate:"required"`
}

type OrderStatusContent struct {
	OrderId string            `json:"order_id" validate:"required"`
	Status  string            `json:"status" validate:"required"`
	Vendor  OrderStatusVendor `json:"vendor" validate:"required"`
}

type OrderStatusVendor struct {
	VendorId string `json:"id" validate:"required"`
}
