package order

import "time"

type OrderFulfillmentReconciliationReceiverHandlerModel struct {
	Data        OrderFulfillmentReconciliationDataModel `validate:"required" json:"data"`
	Event       string                                  `validate:"required" json:"event"`
	Timestamp   time.Time                               `validate:"required" json:"timestamp"`
	WarehouseId string                                  `validate:"required" json:"warehouse_id"`
}

type OrderFulfillmentReconciliationDataModel struct {
	Vendor OrderFulfillmentReconciliationVendorModel `json:"vendor"`
}

type OrderFulfillmentReconciliationVendorModel struct {
	VendorId        string                                             `json:"id"`
	Name            string                                             `json:"name"`
	Payment         OrderFulfillmentReconciliationPaymentModel         `json:"payment"`
	ExtraParameters OrderFulfillmentReconciliationExtraParametersModel `json:"extra_parameters"`
}

type OrderFulfillmentReconciliationExtraParametersModel struct {
	YemekParams string `json:"yemek_params"`
}

type OrderFulfillmentReconciliationPaymentModel struct {
	Currency            string  `json:"currency"`
	PaymentType         string  `json:"payment_type"`
	PaymentTypeDetails  string  `json:"payment_type_details"`
	TotalNet            float64 `json:"total_net"`
	TotalGross          float64 `json:"total_gross"`
	VatPercent          float64 `json:"vat_percent"`
	VatAmount           float64 `json:"vat_amount"`
	PayAtPickup         float64 `json:"pay_at_pickup"`
	CollectFromCustomer float64 `json:"collect_from_customer"`
	Wallet              float64 `json:"wallet"`
	IsVatIncluded       bool    `json:"is_vat_included"`
}

type OrderFulfillmentReconciliationYemekParamsModel struct {
	CollectFromCustomer string `json:"collect_from_customer"`
}
