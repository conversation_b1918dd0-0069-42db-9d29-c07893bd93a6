package order

import (
	"encoding/json"
	"errors"
	"logo-adapter/internal/data/proxy/datafridge"
	"logo-adapter/internal/data/sqs/receiver"
	"logo-adapter/internal/service/order"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type OrderStatusCancellationStreamReceiverHandler struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	OrderService          order.IOrderService
	dataFridgeVendorProxy datafridge.IVendorProxy
}

// NewOrderStatusCancellationStreamReceiverHandler
// Returns a new OrderStatusCancellationStreamReceiverHandler
func NewOrderStatusCancellationStreamReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.I<PERSON>acher,
	OrderService order.IOrderService,
	dataFridgeVendorProxy datafridge.IVendorProxy,
) receiver.IOrderStreamReceiverHandler {
	handler := OrderStatusCancellationStreamReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if OrderService != nil {
		handler.OrderService = OrderService
	} else {
		handler.OrderService = order.NewOrderService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil, nil, nil)
	}

	if dataFridgeVendorProxy != nil {
		handler.dataFridgeVendorProxy = dataFridgeVendorProxy
	} else {
		handler.dataFridgeVendorProxy = datafridge.NewVendorProxy(environment, loggr, validatr, cachr)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *OrderStatusCancellationStreamReceiverHandler) Handle(ch chan receiver.OrderStreamReceiverResponseModel, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	var handlerModel OrderStatusCancellationStreamReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error: err,
		}
		return
	}

	if handlerModel.OrderStatusContent.Status == "CANCELLED" {
		//get vendor data
		dfProxyCh := make(chan *datafridge.GetVendorProxyResponse)
		defer close(dfProxyCh)
		go h.dataFridgeVendorProxy.GetVendor(
			dfProxyCh, &datafridge.GetVendorProxyModel{
				GlobalEntityId: "YS_TR",
				VendorId:       handlerModel.OrderStatusContent.Vendor.VendorId,
			},
		)

		dfProxyResponse := <-dfProxyCh
		if dfProxyResponse.Error != nil {
			ch <- receiver.OrderStreamReceiverResponseModel{
				Error: dfProxyResponse.Error,
			}
			return
		}

		//remove market order by vertical type
		if dfProxyResponse.Data.Vendor.VerticalType == "darkstores" {
			ch <- receiver.OrderStreamReceiverResponseModel{
				Error:               nil,
				IsUnsupportDataType: true,
			}
			return
		}

		cacheValue := h.cachr.Get("MahalleVale_" + handlerModel.OrderStatusContent.OrderId)
		if cacheValue == nil {
			ch <- receiver.OrderStreamReceiverResponseModel{
				Error:               errors.New("order_cancellation_not_processed"),
				IsUnsupportDataType: true,
			}
			return
		}

		sendYsOrderCancellationCh := make(chan *order.SendOfflineYsOrderCancellationToLogisticServiceResponse)
		defer close(sendYsOrderCancellationCh)

		go h.OrderService.SendOfflineYsOrderCancellationToLogistic(sendYsOrderCancellationCh, &order.SendOfflineYsOrderCancellationToLogisticServiceModel{
			OrderId:               handlerModel.OrderStatusContent.OrderId,
			OrderCancellationDate: handlerModel.Timestamp,
		})

		sendOrderCancellationResponse := <-sendYsOrderCancellationCh

		if sendOrderCancellationResponse.Error != nil {
			ch <- receiver.OrderStreamReceiverResponseModel{
				Error: sendOrderCancellationResponse.Error,
			}
			return
		}
	} else {
		ch <- receiver.OrderStreamReceiverResponseModel{
			Error:               nil,
			IsUnsupportDataType: true,
		}
		return
	}

	ch <- receiver.OrderStreamReceiverResponseModel{}
	return
}
