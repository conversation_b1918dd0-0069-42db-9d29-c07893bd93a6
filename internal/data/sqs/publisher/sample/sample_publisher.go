package sample

import (
	"context"
	"encoding/json"
	"logo-adapter/internal/data/sqs/publisher"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"go.uber.org/zap"
)

type ISamplePublisher interface {
	Publish(ch chan publisher.PublisherResponse, message *SamplePublisherModel, attributeOverrides map[string]string)
}

type SamplePublisher struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	timeout           time.Duration
	defaultAttributes map[string]string
	queueUrl          string
	client            *sqs.Client
}

// NewSamplePublisher
// Returns a new SamplePublisher.
func NewSamplePublisher(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) ISamplePublisher {
	queueUrl := environment.Get(env.SamplePublisherSqsQueueUrl)
	region := environment.Get(env.SamplePublisherSqsRegion)

	awsConfig, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(region))
	if err != nil {
		panic("Panicked while loading AWS config: " + err.Error())
	}

	publisher := SamplePublisher{
		environment:       environment,
		loggr:             loggr.With(zap.String("queueUrl", queueUrl)),
		validatr:          validatr,
		cachr:             cachr,
		timeout:           time.Second * 5,
		defaultAttributes: map[string]string{publisher.DummyAttribute: "dummy_attribute_value"},
		queueUrl:          queueUrl,
		client:            sqs.NewFromConfig(awsConfig),
	}

	return &publisher
}

func (p *SamplePublisher) Publish(ch chan publisher.PublisherResponse, model *SamplePublisherModel, attributeOverrides map[string]string) {
	err := p.validatr.ValidateStruct(model)
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), p.timeout)
	defer cancel()

	bytes, err := json.Marshal(model)
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	output, err := p.client.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:          &p.queueUrl,
		MessageAttributes: p.mapAttributes(p.overrideAttributes(attributeOverrides)),
		MessageBody:       aws.String(string(bytes)),
	})

	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	p.loggr.Info(*output.MessageId+" ID message is published successfully.", zap.String("messageId", *output.MessageId))
	ch <- publisher.PublisherResponse{MessageId: output.MessageId}
	return
}

func (p *SamplePublisher) overrideAttributes(overrides map[string]string) map[string]string {
	attr := make(map[string]string)

	for key, value := range p.defaultAttributes {
		attr[key] = value
	}

	if overrides != nil {
		for key, value := range overrides {
			attr[key] = value
		}
	}

	return attr
}

func (p *SamplePublisher) mapAttributes(attributes map[string]string) map[string]types.MessageAttributeValue {
	attr := make(map[string]types.MessageAttributeValue)

	for key, value := range attributes {
		attr[key] = types.MessageAttributeValue{
			DataType:    aws.String("String"),
			StringValue: aws.String(value),
		}
	}

	return attr
}
