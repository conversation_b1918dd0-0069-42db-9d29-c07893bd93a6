package logistic

type LogisticPosProxyBaseResponse struct {
	Error  error `json:"-"`
	Data   bool
	Info   LogisticPosProxyBaseResponseInfo
	Errors []LogisticPosProxyBaseResponseError
}

type LogisticPosProxyBaseResponseInfo struct {
	Code    int
	Message string
}

type LogisticPosProxyBaseResponseError struct {
	Type    string
	Message string
}

type GetWarehouseCountConfigProxyResponse struct {
	Error  error `json:"-"`
	Data   WarehouseCountConfig
	Info   LogisticPosProxyBaseResponseInfo
	Errors []LogisticPosProxyBaseResponseError
}

type WarehouseCountConfig struct {
	IsCycleCountOpen bool
	IsDeltaOpen      bool
}
