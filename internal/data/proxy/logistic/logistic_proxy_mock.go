// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/proxy/logistic/logistic_proxy.go

// Package logistic is a generated GoMock package.
package logistic

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockILogisticProxy is a mock of ILogisticProxy interface.
type MockILogisticProxy struct {
	ctrl     *gomock.Controller
	recorder *MockILogisticProxyMockRecorder
}

// MockILogisticProxyMockRecorder is the mock recorder for MockILogisticProxy.
type MockILogisticProxyMockRecorder struct {
	mock *MockILogisticProxy
}

// NewMockILogisticProxy creates a new mock instance.
func NewMockILogisticProxy(ctrl *gomock.Controller) *MockILogisticProxy {
	mock := &MockILogisticProxy{ctrl: ctrl}
	mock.recorder = &MockILogisticProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILogisticProxy) EXPECT() *MockILogisticProxyMockRecorder {
	return m.recorder
}

// GetWarehouseCountConfig mocks base method.
func (m *MockILogisticProxy) GetWarehouseCountConfig(ch chan *GetWarehouseCountConfigProxyResponse, model *GetWarehouseCountConfigModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetWarehouseCountConfig", ch, model)
}

// GetWarehouseCountConfig indicates an expected call of GetWarehouseCountConfig.
func (mr *MockILogisticProxyMockRecorder) GetWarehouseCountConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarehouseCountConfig", reflect.TypeOf((*MockILogisticProxy)(nil).GetWarehouseCountConfig), ch, model)
}

// SendMahalleValeOrderCollection mocks base method.
func (m *MockILogisticProxy) SendMahalleValeOrderCollection(ch chan *LogisticPosProxyBaseResponse, model *SendMahalleValeOrderCollectionModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendMahalleValeOrderCollection", ch, model)
}

// SendMahalleValeOrderCollection indicates an expected call of SendMahalleValeOrderCollection.
func (mr *MockILogisticProxyMockRecorder) SendMahalleValeOrderCollection(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMahalleValeOrderCollection", reflect.TypeOf((*MockILogisticProxy)(nil).SendMahalleValeOrderCollection), ch, model)
}

// SendOrderCollection mocks base method.
func (m *MockILogisticProxy) SendOrderCollection(ch chan *LogisticPosProxyBaseResponse, model *SendOrderCollectionModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderCollection", ch, model)
}

// SendOrderCollection indicates an expected call of SendOrderCollection.
func (mr *MockILogisticProxyMockRecorder) SendOrderCollection(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderCollection", reflect.TypeOf((*MockILogisticProxy)(nil).SendOrderCollection), ch, model)
}

// SendWarehouse mocks base method.
func (m *MockILogisticProxy) SendWarehouse(ch chan *LogisticPosProxyBaseResponse, model *SendWarehouseModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendWarehouse", ch, model)
}

// SendWarehouse indicates an expected call of SendWarehouse.
func (mr *MockILogisticProxyMockRecorder) SendWarehouse(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWarehouse", reflect.TypeOf((*MockILogisticProxy)(nil).SendWarehouse), ch, model)
}
