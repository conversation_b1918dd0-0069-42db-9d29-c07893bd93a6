package logistic

type SendOrderCollectionModel struct {
	VendorName           string  `validate:"required"`
	VendorTransactionId  int64   `validate:"required"`
	VendorTrackingNumber string  `validate:"required"`
	Amount               float64 `validate:"required"`
	TransactionType      string  `validate:"required"`
	PaymentGroupType     int     `validate:"required"`
	PaymentMethodName    string
	StoreId              string
	RiderName            string
}

type SendMahalleValeOrderCollectionModel struct {
	VendorName           string  `validate:"required"`
	VendorTrackingNumber string  `validate:"required"`
	Amount               float64 `validate:"gte=0"`
	TransactionType      string  `validate:"required"`
	PaymentGroupType     int     `validate:"required"`
	PaymentMethodName    string
	StoreName            string
	RiderName            string
}

type SendWarehouseModel struct {
	WarehouseId   string `validate:"required"`
	WarehouseName string `validate:"required"`
	IsActive      bool
}

type GetWarehouseCountConfigModel struct {
	WarehouseId string `validate:"required"`
}
