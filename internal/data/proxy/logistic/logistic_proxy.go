package logistic

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"go.uber.org/zap"
)

type ILogisticProxy interface {
	SendOrderCollection(ch chan *LogisticPosProxyBaseResponse, model *SendOrderCollectionModel)
	SendMahalleValeOrderCollection(ch chan *LogisticPosProxyBaseResponse, model *SendMahalleValeOrderCollectionModel)
	SendWarehouse(ch chan *LogisticPosProxyBaseResponse, model *SendWarehouseModel)
	GetWarehouseCountConfig(ch chan *GetWarehouseCountConfigProxyResponse, model *GetWarehouseCountConfigModel)
}

type LogisticProxy struct {
	baseUrl     *url.URL
	timeout     time.Duration
	environment env.IEnvironment
	loggr       logger.ILogger
	validatr    validator.IValidator
	cachr       cacher.ICacher
	client      *http.Client
	authToken   string
}

// NewLogisticProxy
// Returns a new LogisticProxy.
func NewLogisticProxy(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
) ILogisticProxy {

	timeout, timeoutErr := strconv.Atoi(environment.Get(env.LogisticPosProxyTimeout))
	if timeoutErr != nil {
		loggr.Panic("Couldn't convert LOGISTIC_POS_PROXY_TIMEOUT environment variable to int !")
	}

	logisticPosUrl, urlErr := url.Parse(environment.Get(env.LogisticPosProxyUrl))
	if urlErr != nil {
		loggr.Panic("Couldn't convert LOGISTIC_POS_URL environment variable to url.URL !")
	}

	return &LogisticProxy{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
		baseUrl:     logisticPosUrl,
		timeout:     time.Second * time.Duration(timeout),
		client:      &http.Client{Timeout: time.Second * time.Duration(timeout)},
		authToken:   environment.Get(env.LogisticPosProxyAuthToken),
	}
}

func (l *LogisticProxy) SendOrderCollection(ch chan *LogisticPosProxyBaseResponse, model *SendOrderCollectionModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/orders/collection",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogisticPosProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: decodeErr}
		return
	}

	serializedErrors, serializationErrorsErr := json.Marshal(response.Errors)
	if serializationErrorsErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: serializationErrorsErr}
		return
	}

	if response.Info.Code == 1000 {
		l.loggr.Info("Order already delivered before.TrackingNumber: " + model.VendorTrackingNumber)
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't transfer order collection to logistic pos ! details : %v", response)
		l.loggr.Error(
			"couldn't transfer order collection to logistic pos !",
			zap.String("ResultMessage", response.Info.Message),
			zap.Bool("Result", response.Data),
			zap.String("LogisticPosProxyOrderCollectionError", string(serializedErrors)),
			zap.Error(response.Error),
		)
		ch <- &LogisticPosProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogisticProxy) SendMahalleValeOrderCollection(ch chan *LogisticPosProxyBaseResponse, model *SendMahalleValeOrderCollectionModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/mahallevaleorders/collection",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogisticPosProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: decodeErr}
		return
	}

	serializedErrors, serializationErrorsErr := json.Marshal(response.Errors)
	if serializationErrorsErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: serializationErrorsErr}
		return
	}

	if response.Info.Code == 1000 {
		l.loggr.Info("Order already sent before.TrackingNumber: " + model.VendorTrackingNumber)
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't transfer order collection to logistic pos ! details : %v", response)
		l.loggr.Error(
			"couldn't transfer order collection to logistic pos !",
			zap.String("ResultMessage", response.Info.Message),
			zap.Bool("Result", response.Data),
			zap.String("LogisticPosProxyOrderCollectionError", string(serializedErrors)),
			zap.Error(response.Error),
		)
		ch <- &LogisticPosProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogisticProxy) SendWarehouse(ch chan *LogisticPosProxyBaseResponse, model *SendWarehouseModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/warehouses/add-or-update-warehouse",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogisticPosProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: decodeErr}
		return
	}

	serializedErrors, serializationErrorsErr := json.Marshal(response.Errors)
	if serializationErrorsErr != nil {
		ch <- &LogisticPosProxyBaseResponse{Error: serializationErrorsErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't send warehouse to logistic pos ! details : %v", response)
		l.loggr.Error(
			"couldn't send warehouse to logistic pos !",
			zap.String("ResultMessage", response.Info.Message),
			zap.Bool("Result", response.Data),
			zap.String("LogisticPosProxyAddWarehouseError", string(serializedErrors)),
			zap.Error(response.Error),
		)
		ch <- &LogisticPosProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogisticProxy) GetWarehouseCountConfig(ch chan *GetWarehouseCountConfigProxyResponse, model *GetWarehouseCountConfigModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetWarehouseCountConfigProxyResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &GetWarehouseCountConfigProxyResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodGet,
			l.baseUrl.String()+"/warehouses/warehouse-count-config?warehouseId="+model.WarehouseId,
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &GetWarehouseCountConfigProxyResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &GetWarehouseCountConfigProxyResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response GetWarehouseCountConfigProxyResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &GetWarehouseCountConfigProxyResponse{Error: decodeErr}
		return
	}

	serializedErrors, serializationErrorsErr := json.Marshal(response.Errors)
	if serializationErrorsErr != nil {
		ch <- &GetWarehouseCountConfigProxyResponse{Error: serializationErrorsErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't get warehouse count config from logistic pos ! details : %v", response)
		l.loggr.Error(
			"couldn't get warehouse count config from logistic pos !",
			zap.String("ResultMessage", response.Info.Message),
			zap.Any("Result", response.Data),
			zap.String("LogisticPosProxyGetWarehouseConfigError", string(serializedErrors)),
			zap.Error(response.Error),
		)
		ch <- &GetWarehouseCountConfigProxyResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogisticProxy) addDefaultHeaders(request *http.Request) {
	request.Header.Set("Content-Type", "application/json")
	request.Header.Add("Authorization", l.authToken)
}
