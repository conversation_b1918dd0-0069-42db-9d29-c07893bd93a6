// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/proxy/datafridge/vendor_proxy.go

// Package datafridge is a generated GoMock package.
package datafridge

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIVendorProxy is a mock of IVendorProxy interface.
type MockIVendorProxy struct {
	ctrl     *gomock.Controller
	recorder *MockIVendorProxyMockRecorder
}

// MockIVendorProxyMockRecorder is the mock recorder for MockIVendorProxy.
type MockIVendorProxyMockRecorder struct {
	mock *MockIVendorProxy
}

// NewMockIVendorProxy creates a new mock instance.
func NewMockIVendorProxy(ctrl *gomock.Controller) *MockIVendorProxy {
	mock := &MockIVendorProxy{ctrl: ctrl}
	mock.recorder = &MockIVendorProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIVendorProxy) EXPECT() *MockIVendorProxyMockRecorder {
	return m.recorder
}

// GetVendor mocks base method.
func (m *MockIVendorProxy) GetVendor(ch chan *GetVendorProxyResponse, model *GetVendorProxyModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetVendor", ch, model)
}

// GetVendor indicates an expected call of GetVendor.
func (mr *MockIVendorProxyMockRecorder) GetVendor(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVendor", reflect.TypeOf((*MockIVendorProxy)(nil).GetVendor), ch, model)
}
