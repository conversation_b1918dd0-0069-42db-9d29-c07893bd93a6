package datafridge

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"go.uber.org/zap"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	usts "logo-adapter/internal/util/sts"
	"logo-adapter/internal/util/validator"

	sts "github.com/deliveryhero/pd-sts-go-sdk"
)

type IVendorProxy interface {
	GetVendor(ch chan *GetVendorProxyResponse, model *GetVendorProxyModel)
}

type VendorProxy struct {
	environment   env.IEnvironment
	loggr         logger.ILogger
	validatr      validator.IValidator
	cachr         cacher.ICacher
	client        *http.Client
	baseUrl       *url.URL
	timeout       time.Duration
	stsPrivateKey string
	issuer        string
	clientID      string
	keyID         string
	scopeName     string
	tokenService  sts.TokenService
}

// NewVendorProxy
// Returns a new VendorProxy.
func NewVendorProxy(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IVendorProxy {
	proxyUrl, urlErr := url.Parse(environment.Get(env.DataFridgeVendorProxyUrl))
	if urlErr != nil {
		loggr.Panic("Couldn't convert DATA_FRIDGE_VENDOR_PROXY_URL environment variable to url.URL !")
	}

	timeout, timeoutErr := strconv.Atoi(environment.Get(env.DataFridgeVendorProxyTimeout))
	if timeoutErr != nil {
		loggr.Panic("Couldn't convert DATA_FRIDGE_VENDOR_PROXY_TIMEOUT environment variable to int !")
	}

	stsPrivateKey := environment.Get(env.StsPK)
	issuer := environment.Get(env.StsIssuer)
	clientID := environment.Get(env.StsClientId)
	keyID := environment.Get(env.StsKeyId)
	scopeName := environment.Get(env.StsVendorApiScopeName)

	tokenService, errNewTokenService := usts.GetNewTokenService(stsPrivateKey, issuer, clientID, keyID, scopeName)
	if errNewTokenService != nil {
		loggr.Error("Failed to get new token Vendor Api STS Token", zap.Error(errNewTokenService))
	}

	service := VendorProxy{
		environment:   environment,
		loggr:         loggr,
		validatr:      validatr,
		cachr:         cachr,
		client:        &http.Client{Timeout: time.Second * time.Duration(timeout)},
		baseUrl:       proxyUrl,
		timeout:       time.Second * time.Duration(timeout),
		stsPrivateKey: stsPrivateKey,
		issuer:        issuer,
		clientID:      clientID,
		keyID:         keyID,
		scopeName:     scopeName,
		tokenService:  tokenService,
	}
	return &service
}

// GetVendor
// Gets vendor details from Data Fridge.
func (p *VendorProxy) GetVendor(ch chan *GetVendorProxyResponse, model *GetVendorProxyModel) {
	modelErr := p.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetVendorProxyResponse{Error: modelErr}
		return
	}

	authToken, tokenErr := usts.GetToken(p.tokenService, p.scopeName)
	if tokenErr != nil {
		ch <- &GetVendorProxyResponse{Error: tokenErr}
		return
	}

	// You can override the default timeout here.
	ctx, cancel := context.WithTimeout(context.Background(), p.timeout)
	defer cancel()
	request, ctxErr := http.NewRequestWithContext(ctx, http.MethodGet, p.baseUrl.String()+"/v2/global-entity-ids/"+model.GlobalEntityId+"/vendors/"+model.VendorId, nil)
	if ctxErr != nil {
		ch <- &GetVendorProxyResponse{Error: ctxErr}
		return
	}

	request.Header.Add("Authorization", authToken)

	response, err := p.client.Do(request)
	if err != nil {
		ch <- &GetVendorProxyResponse{Error: err}
		return
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		responseError := fmt.Errorf("dataFridgeVendorProxy returned status code %v", response.StatusCode)
		ch <- &GetVendorProxyResponse{Error: responseError}
		return
	}

	var resp GetVendorProxyResponse
	decodeError := json.NewDecoder(response.Body).Decode(&resp)
	if decodeError != nil {
		ch <- &GetVendorProxyResponse{Error: decodeError}
		return
	}

	ch <- &resp
}
