// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/proxy/sample/sample_proxy.go

// Package sample is a generated GoMock package.
package sample

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISampleProxy is a mock of ISampleProxy interface.
type MockISampleProxy struct {
	ctrl     *gomock.Controller
	recorder *MockISampleProxyMockRecorder
}

// MockISampleProxyMockRecorder is the mock recorder for MockISampleProxy.
type MockISampleProxyMockRecorder struct {
	mock *MockISampleProxy
}

// NewMockISampleProxy creates a new mock instance.
func NewMockISampleProxy(ctrl *gomock.Controller) *MockISampleProxy {
	mock := &MockISampleProxy{ctrl: ctrl}
	mock.recorder = &MockISampleProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISampleProxy) EXPECT() *MockISampleProxyMockRecorder {
	return m.recorder
}

// GetGoogle mocks base method.
func (m *MockISampleProxy) GetGoogle(ch chan *GetSampleProxyResponse, model *GetSampleProxyModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetGoogle", ch, model)
}

// GetGoogle indicates an expected call of GetGoogle.
func (mr *MockISampleProxyMockRecorder) GetGoogle(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoogle", reflect.TypeOf((*MockISampleProxy)(nil).GetGoogle), ch, model)
}
