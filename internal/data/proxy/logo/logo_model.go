package logo

import (
	"time"
)

type UpdateSupplierOrderCollectionModel struct {
	OrderCollectionId int64     `validate:"gt=0"`
	NewDispatchNo     string    `validate:"required"`
	NewDispatchDate   time.Time `validate:"required"`
	OldDispatchNo     string    `validate:"required"`
	OldDispatchDate   time.Time `validate:"required"`
	OrderId           string    `validate:"required"`
}

type UpdateSupplierCollectionProductsModel struct {
	OrderId           string `validate:"required"`
	OrderCollectionId int64  `validate:"gt=0"`
	ProductId         string `validate:"required"`
	Quantity          int    `validate:"gt=0"`
	ExpirationDate    time.Time
	RecordType        int `validate:"gt=0"`
}

type SendGlobalPurchaseOrderModel struct {
	MessageId       string                               `json:"MessageId"`
	TransactionDate time.Time                            `json:"TransactionDate"`
	POSlips         []SendGlobalPurchaseOrderPoSlipModel `json:"PO_Slips"`
}

type SendGlobalPurchaseOrderPoSlipModel struct {
	POReference  string                                      `json:"PO_Reference"`
	POGuidId     string                                      `json:"PO_Guid_Id"`
	PODate       time.Time                                   `json:"PO_Date"`
	SupplierCode string                                      `json:"SupplierCode"`
	PayPlanCode  string                                      `json:"PayPlanCode"`
	StoreId      string                                      `json:"StoreId"`
	ProjectCode  string                                      `json:"ProjectCode"`
	DeliveryDate time.Time                                   `json:"DeliveryDate"`
	ProductList  []SendGlobalPurchaseOrderPoSlipProductModel `json:"ProductList"`
}

type SendGlobalPurchaseOrderPoSlipProductModel struct {
	SKU      string  `json:"SKU"`
	Quantity int     `json:"Quantity"`
	Price    float64 `json:"Price"`
	VatRate  float32 `json:"VatRate"`
}

// AddOrUpdateGlobalSuppliersModel This model not exists in actively used logo api. TODO: check for validation rules..
type AddOrUpdateGlobalSuppliersModel struct {
	MessageId string                                    `validate:"required"`
	Suppliers []AddOrUpdateGlobalSuppliersSupplierModel `validate:"required"`
}

type AddOrUpdateGlobalSuppliersSupplierModel struct {
	SupplierCode   string `validate:"required"`
	SupplierName   string `validate:"required"`
	Taxnr          string `validate:"required"`
	TaxOffice      string `validate:"required"`
	TelNumber      string `validate:"required"`
	Email          string `validate:"required"`
	AddressLineOne string `validate:"required" json:"Address1"`
	AddressLineTwo string `json:"Address2"`
	Town           string `validate:"required"`
	City           string `validate:"required"`
	Country        string `validate:"required"`
	PayPlanCode    string `validate:"required"`
	ProjectCode    string `validate:"required"`
	IBAN           string `validate:"required"`
	Mark           string `validate:"required"`
	PostCode       string `validate:"required"`
}

type SendProductMaterialsModel struct {
	MaterialList []SendProductMaterialsMaterialModel `validate:"required"`
}

type SendProductMaterialsMaterialModel struct {
	MaterialId   string `validate:"required"`
	MaterialName string `validate:"required"`
}

type SendMaterialSlipsModel struct {
	MessageId       string              `validate:"required"`
	TransactionDate time.Time           `validate:"required"`
	MaterialSlips   []MaterialSlipModel `validate:"required"`
}

type SendRevertedRequestModel struct {
	MessageId       int64               `validate:"gt=0"`
	TransactionDate time.Time           `validate:"required"`
	MaterialSlips   []MaterialSlipModel `validate:"required"`
}

type MaterialSlipModel struct {
	StoreId     string
	SlipType    int
	SlipId      string
	ProductList []MaterialSlipProductModel `validate:"required"`
}

type MaterialSlipProductModel struct {
	RequestId  string
	SKU        string
	Quantity   float64
	Unit       string
	ReasonCode string
	CreatedBy  string
}

type MaterialSlipProductServiceModel struct {
	RequestId      string
	SKU            string
	Quantity       int
	Unit           string
	ReasonCode     string
	CreatedBy      string
	LogoSlipTypeId int
}

type SendOrderTipModel struct {
	MessageId           int64 `validate:"gt=0"`
	TransactionDate     time.Time
	PaymentApprovedUser string                      `validate:"required"` // valid:"OrderInfo.TotalAmount>0"
	OrderInfo           SendCompletedOrderInfoModel `validate:"required"`
	OrderDetail         []SendOrderTipOrderDetail
	OrderAddress        OrderAddress
	CollectionSource    int
	OrderSourceType     *int
}

type GetProductPropertiesModel struct {
	ProductIdList []ProductIdModel `validate:"required"`
}

type ProductIdModel struct {
	ProductId string `validate:"required"`
}

type SendProductsModel struct {
	ProductList []SendProductsProduct `validate:"required"`
}

type SendProductsProduct struct {
	ProductId         string
	Sku               string `json:"SKU"`
	ProductName       string
	BarcodeNumberList []string
	SalesVat          float64
	PurchaseVat       float64
	Segment           string
	Width             float64
	Height            float64
	Length            float64
	GrossWeight       float64
	UnitPerPackage    float64
	IsCoffeeProduct   bool
}

type GetPurchaseOrderDetailWithPriceModel struct {
	StoreId string `validate:"required"`
	BegDate time.Time
	EndDate time.Time
}

type SendCompletedTipRefundModel struct {
	MessageId           int64 `validate:"gt=0"`
	TransactionDate     time.Time
	OrderInfo           RefundedOrderInfo     `validate:"required"`
	RefundedOrderDetail []RefundedOrderDetail `validate:"required"`
	OrderSourceType     *int
}

type RefundedOrderInfo struct {
	TrackingNumber    int64    `validate:"gt=0"`
	StoreId           string   `validate:"required"`
	UserData          UserData `validate:"required"`
	BankCode          string
	PaymentMethodName string
}

type Options struct {
	OptionId    int
	Rank        int
	IsRequired  bool
	OptionItems []OptionItems
}

type OptionItems struct {
	OptionItemValue  string
	OptionItemId     int
	OptionMaterialId int
	AdditionalPrice  float32
}

type GetPurchaseOrderDetailModel struct {
	OrderId string `validate:"required"`
}

type SendSupplierOrderCollectionModel struct {
	MessageId       string `validate:"required"`
	TransactionDate time.Time
	OrderId         string `validate:"required"`
	OrderReference  string `validate:"required"`
	StoreId         string `validate:"required"`
	BillingId       string `validate:"required"`
	BillingDate     time.Time
	OrderDetail     []OrderDetail `validate:"required"`
}

type OrderDetail struct {
	Sku          string `validate:"required"`
	ProductName  string
	Quantity     int `validate:"gte=0"`
	IsOutOfOrder bool
}

type GetStorePurchaseOrdersModel struct {
	StoreIdList []string `validate:"required"`
}

type SendUser struct {
	UserName           string
	UserId             string
	IdentityCardNumber string
	FirstName          string
	LastName           string
	StoreId            string
}

type SendUsersModel struct {
	UserList []SendUser `validate:"required"`
}

type SendInterWarehouseTransferModel struct {
	MessageId           int64                          `validate:"gt=0"`
	TransactionDate     time.Time                      `validate:"required"`
	TransferId          int64                          `validate:"gt=0"`
	SourceStoreId       string                         `validate:"required"`
	SourceWarehouseType int                            `validate:"gt=0"`
	TargetStoreId       string                         `validate:"required"`
	TargetWarehouseType int                            `validate:"gt=0"`
	DispatchId          string                         `validate:"required"`
	TransferDate        time.Time                      `validate:"required"`
	OrderDetail         []SendStoreTransferOrderDetail `validate:"required"`
}

type SendStoreTransferCollectionModel struct {
	MessageId                string                     `validate:"required"`
	TransactionDate          time.Time                  `validate:"required"`
	TransferId               string                     `validate:"required"`
	TransferReference        string                     `validate:"required"`
	SourceStoreId            string                     `validate:"required"`
	SourceWarehouseType      int                        `validate:"gt=0"`
	TargetStoreId            string                     `validate:"required"`
	TargetWarehouseType      int                        `validate:"gt=0"`
	DispatchId               string                     `validate:"required"`
	TransferDate             time.Time                  `validate:"required"`
	OrderDetail              []StoreTransferOrderDetail `validate:"required"`
	DriverName               string                     `validate:"required"`
	DriverIdentityCardNumber string                     `validate:"required"`
	DriverPlate              string                     `validate:"required"`
}

type StoreTransferOrderDetail struct {
	Quantity int    `validate:"gte=0"`
	SKU      string `validate:"required"`
}

type SendStoreTransferOrderDetail struct {
	ProductId      string
	ProductName    string
	Quantity       int    `validate:"gte=0"`
	Unit           string `validate:"required"`
	LineItemId     string
	LineIndex      int
	IsBundle       bool
	WasteQuantity  int
	TotalListPrice int
	TotalPrice     int
	UnitListPrice  int
	Options        []Options
}

type SendOrderTipOrderDetail struct {
	ProductId      string `validate:"required"`
	ProductName    string
	Quantity       int    `validate:"gte=0"`
	Unit           string `validate:"required"`
	LineItemId     string
	LineIndex      int
	IsBundle       bool
	WasteQuantity  int
	TotalListPrice int
	TotalPrice     int
	UnitListPrice  int
	Options        []Options
}

type GetStoreStocksModel struct {
	StoreIdList []StoreStockStoreId `validate:"required"`
}

type StoreStockStoreId struct {
	StoreId string
}

type StoreStockList struct {
	StoreId       string `json:"StoreId"`
	WarehouseType int    `json:"WarehouseType"`
	ProductId     string `json:"ProductId"`
	MaterialCode  string `json:"MaterialCode"`
	Quantity      int    `json:"Quantity"`
}

type SendInventoryStocksModel struct {
	StoreStockCountList []InventoryStock `validate:"required"`
}

type InventoryStock struct {
	MessageId           string `validate:"required"`
	StoreId             string `validate:"required"`
	SKU                 string `validate:"required"`
	StoreCount          float64
	CenterStoreQuantity float64
}

type SendStoresModel struct {
	StoreList []Store `validate:"required"`
}

type Store struct {
	StoreId          string `json:"StoreId"`
	StoreName        string `json:"StoreName"`
	AddressText      string `json:"AddressText"`
	City             string `json:"City"`
	Email            string `json:"Email"`
	StoreType        int    `json:"StoreType"`
	SalesforceGridId string `json:"SalesforceGridId"`
}

type SendStoreRefundModel struct {
	MessageId       string    `json:"MessageId"`
	TransactionDate time.Time `json:"TransactionDate"`
	StoreId         string    `json:"StoreId"`
	SlipType        int       `json:"SlipType"`
	CarrierType     string    `json:"CarrierType"`
	SupplierCode    string    `json:"SupplierCode"`
	DispatchId      string    `json:"DispatchId"`
	DispatchDate    time.Time `json:"DispatchDate"`
	ProductList     []Product `json:"ProductList"`
}

type Product struct {
	SKU        string `json:"SKU"`
	Quantity   int    `json:"Quantity"`
	ReasonCode string `json:"ReasonCode"`
}

type GlobalPurchaseOrderCancellationModel struct {
	MessageId        string    `json:"MessageId"`
	POGuidId         string    `json:"PO_Guid_Id"`
	POReference      string    `json:"PO_Reference"`
	CancellationDate time.Time `json:"Cancellation_Date"`
}

type GetDispatchDocumentModel struct {
	DispatchNumber string `validate:"required"`
}

type SendStoreTransferShrinkageModel struct {
	MessageId                  string                   `json:"MessageId" validate:"required"`
	StoreTransferShrinkageList []StoreTransferShrinkage `json:"StoreTransferShrinkageList" validate:"required"`
}

type StoreTransferShrinkage struct {
	TransferRef        string `json:"Transfer_ref"`
	StoreId            string `json:"StoreId"`
	SKU                string `json:"sku"`
	DifferenceQuantity int    `json:"DifferenceQuantity"`
}

type SendCompletedOrderModel struct {
	MessageId       string                          `validate:"required"`
	TransactionDate time.Time                       `validate:"required"`
	OrderInfo       SendCompletedOrderInfoModel     `validate:"required"`
	OrderDetail     []SendCompletedOrderDetailModel `validate:"required"`
	OrderAddress    OrderAddress                    `validate:"required"`
}

type SendCompletedOrderInfoModel struct {
	TrackingNumber                  string        `validate:"gt=0"`
	OrderDate                       time.Time     `validate:"required"`
	CollectedDate                   time.Time     `validate:"required"`
	PaymentApprovedDate             time.Time     `validate:"required"`
	PaymentCollectedDate            time.Time     `validate:"required"`
	PaymentList                     []PaymentList `validate:"required"`
	StoreId                         string        `validate:"required"`
	UserData                        UserData      `validate:"required"`
	TotalLineItemsAmount            float64       `validate:"gte=0"`
	DeliveryFee                     float64       `validate:"gte=0"`
	TotalAmount                     float64       `validate:"gte=0"`
	TipAmount                       float64       `validate:"gte=0"`
	CouponDiscountAmount            float64       `validate:"gte=0"`
	BasketDiscountAmount            float64       `validate:"gte=0"`
	RefundAmountBeforeCollection    float64       `validate:"gte=0"`
	DeliveryFeeCouponDiscountAmount float64       `validate:"gte=0"`
	PaymentMethodName               string
	CourierName                     string
}

type SendCompletedOrderDetailModel struct {
	SKU            string `validate:"required" json:"sku"`
	Quantity       int    `validate:"gt=0"`
	LineItemId     string `validate:"required"`
	LineIndex      int    `validate:"gte=0"`
	IsBundle       bool
	TotalListPrice float64 `validate:"gt=0"`
	TotalPrice     float64 `validate:"gt=0"`
	UnitListPrice  float64 `validate:"gt=0"`
	ProductName    string
}

type SendCompletedRefundBulkModel struct {
	MessageId       string                     `validate:"required"`
	TransactionDate time.Time                  `validate:"required"`
	RefundedOrders  []SendCompletedRefundModel `validate:"required" json:"refundedOrders"`
}

type SendCompletedRefundModel struct {
	MessageId           string                            `validate:"required"`
	TransactionDate     time.Time                         `validate:"required"`
	OrderInfo           SendCompletedRefundOrderInfoModel `validate:"required"`
	RefundedOrderDetail []RefundedOrderDetail             `validate:"required"`
}

type SendCompletedRefundOrderInfoModel struct {
	TrackingNumber                          string        `validate:"gt=0"`
	RefundDate                              time.Time     `validate:"required"`
	CollectedDate                           time.Time     `validate:"required"`
	PaymentApprovedDate                     time.Time     `validate:"required"`
	PaymentCollectedDate                    time.Time     `validate:"required"`
	PaymentList                             []PaymentList `validate:"required"`
	StoreId                                 string        `validate:"required"`
	UserData                                UserData      `validate:"required"`
	RefundedLineItemsAmount                 float64       `validate:"gte=0"`
	RefundedTotalAmount                     float64       `validate:"gte=0"`
	RefundedDeliveryFee                     float64       `validate:"gte=0"`
	RefundedTipAmount                       float64       `validate:"gte=0"`
	RefundedCouponDiscountAmount            float64       `validate:"gte=0"`
	RefundedBasketDiscountAmount            float64       `validate:"gte=0"`
	RefundedDeliveryFeeCouponDiscountAmount float64       `validate:"gte=0"`
	PaymentMethodName                       string
}

type RefundedOrderDetail struct {
	LineItemId             string `validate:"required"`
	LineIndex              int    `validate:"gte=0"`
	IsBundle               bool
	SKU                    string `validate:"required"`
	ProductName            string
	Quantity               int     `validate:"gt=0"`
	TotalListPrice         float64 `validate:"gt=0"`
	TotalPrice             float64 `validate:"gt=0"`
	UnitListPrice          float64 `validate:"gt=0"`
	OriginalTotalPrice     float64 `validate:"gt=0"`
	OriginalUnitPrice      float64 `validate:"gt=0"`
	OriginalTotalListPrice float64 `validate:"gt=0"`
}

type SendNotRequiredFullRefundModel struct {
	MessageId       string                                  `validate:"required"`
	TransactionDate time.Time                               `validate:"required"`
	OrderInfo       SendNotRequiredFullRefundOrderInfoModel `validate:"required"`
	OrderAddress    OrderAddress                            `validate:"required"`
}

type SendNotRequiredFullRefundOrderInfoModel struct {
	TrackingNumber               string    `validate:"required"`
	OrderDate                    time.Time `validate:"required"`
	StoreId                      string    `validate:"required"`
	UserData                     UserData  `validate:"required"`
	RefundAmountBeforeCollection float64   `validate:"gte=0"`
	BankCode                     string
	PaymentMethodName            string
}

type PaymentList struct {
	BankCode           string
	Amount             float64 `validate:"gte=0"`
	SlipTerminalNumber string
	BankAuthCode       string
	BankStan           string
}

type UserData struct {
	UserId         string `validate:"required"`
	UserFriendlyId string `validate:"required"`
	UserFirstName  string `validate:"required"`
	UserLastName   string `validate:"required"`
}

type OrderAddress struct {
	FirstName       string `validate:"required"`
	LastName        string `validate:"required"`
	TelephoneNumber string
	AddressLine     string
	RegionName      string
	CityName        string
	Email           string `validate:"required"`
}

type OrderOfflinePaymentUserDataProxyModel struct {
	UserId         string `validate:"required"`
	UserFriendlyId string `validate:"required"`
	UserFirstName  string `validate:"required"`
	UserLastName   string `validate:"required"`
}

type OrderOfflinePaymentListProxyModel struct {
	BankCode             string    `validate:"required"`
	Amount               float64   `validate:"gte=0"`
	SlipTerminalNumber   string    `validate:"required"`
	BankAuthCode         string    `validate:"required"`
	BankStan             string    `validate:"required"`
	CourierName          string    `validate:"required"`
	PaymentCollectedDate time.Time `validate:"required"`
	PaymentCollectedUser string    `validate:"required"`
}

type OrderOfflinePaymentProxyModel struct {
	MessageId         string                                `validate:"required"`
	TransactionDate   time.Time                             `validate:"required"`
	TrackingNumber    string                                `validate:"required"`
	StoreId           string                                `validate:"required"`
	PaymentMethodName string                                `validate:"required"`
	UserData          OrderOfflinePaymentUserDataProxyModel `validate:"required"`
	PaymentList       []OrderOfflinePaymentListProxyModel   `validate:"required"`
}

type SendOrderOfflinePaymentProxyModel struct {
	OrderOfflinePaymentProxyModel
	OrderDate              time.Time `validate:"required"`
	OrderAmount            float64   `validate:"gte=0"`
	CollectedAmount        float64   `validate:"gte=0"`
	OrderPaymentMethodName string    `validate:"required"`
}

type SendRefundOrderOfflinePaymentProxyModel struct {
	OrderOfflinePaymentProxyModel
	RefundOrderDate       time.Time `validate:"required"`
	RefundOrderAmount     float64   `validate:"gte=0"`
	RefundCollectedAmount float64   `validate:"gte=0"`
}

type SalesforceAccount struct {
	SalesforceGridId               string
	StoreType                      int `validate:"gte=0,lte=2"` // 0: Store, 1: Franchise, 2: FranchiseBakkal
	CommercialName                 string
	CommercialType                 int `validate:"gte=0,lte=1"` // 0: Şirket, 1: Kişi
	TaxAdministration              string
	TaxNumber                      string
	IBAN                           string
	BillingAddress                 *SalesforceAddress
	RestaurantAddress              *SalesforceAddress
	EmailAddress                   string
	MobilePhoneNumber              string
	PhoneNumber                    string
	StoreSiteName                  string
	InvoiceDeliveryType            string
	ResponsibleSalesRepresentative string
}

type SalesforceAddress struct {
	Address1 string
	Address2 string
	Town     string
	City     string
	Country  string
	PostCode string
}

type SendSalesforceEventProxyModel struct {
	MessageId   string
	SalesForces []SalesforceAccount
}

type GetStoreStockCountApprovalStatusModel struct {
	StoreId        string    `validate:"required"`
	SentDateToLogo time.Time `validate:"required"`
}
