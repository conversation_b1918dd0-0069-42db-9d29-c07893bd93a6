package logo

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
)

type ILogoProxy interface {
	UpdateSupplierOrderCollection(ch chan *LogoProxyBaseResponse, model *UpdateSupplierOrderCollectionModel)
	UpdateSupplierCollectionProducts(ch chan *LogoProxyBaseResponse, model *UpdateSupplierCollectionProductsModel)
	SendGlobalPurchaseOrder(ch chan *LogoProxyBaseResponse, model *SendGlobalPurchaseOrderModel)
	AddOrUpdateGlobalSuppliers(ch chan *LogoProxyBaseResponse, model *AddOrUpdateGlobalSuppliersModel)
	SendProductMaterials(ch chan *LogoProxyBaseResponse, model *SendProductMaterialsModel)
	SendMaterialSlips(ch chan *LogoProxyBaseResponse, model *SendMaterialSlipsModel)
	SendRevertedRequest(ch chan *LogoProxyBaseResponse, model *SendRevertedRequestModel)
	SendCompletedOrder(ch chan *LogoProxyBaseResponse, model *SendCompletedOrderModel)
	SendOrderTip(ch chan *LogoProxyBaseResponse, model *SendOrderTipModel)
	GetProductProperties(ch chan *GetProductPropertiesResponse, model *GetProductPropertiesModel)
	SendProducts(ch chan *LogoProxyBaseResponse, model *SendProductsModel)
	GetPurchaseOrderDetailWithPrice(
		ch chan *GetPurchaseOrderDetailWithPriceResponse,
		model *GetPurchaseOrderDetailWithPriceModel,
	)
	SendCompletedFullRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedRefundModel)
	SendCompletedFullRefundBulk(ch chan *LogoProxyRefundedOrderBulkResponse, model *SendCompletedRefundBulkModel)
	SendCompletedPartialRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedRefundModel)
	SendCompletedTipRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedTipRefundModel)
	SendNotRequiredFullRefund(ch chan *LogoProxyBaseResponse, model *SendNotRequiredFullRefundModel)
	GetPurchaseOrderDetail(ch chan *LogoProxyGetPurchaseOrderDetailResponse, model *GetPurchaseOrderDetailModel)
	SendSupplierOrderCollection(ch chan *LogoProxyBaseResponse, model *SendSupplierOrderCollectionModel)
	GetStorePurchaseOrders(ch chan *LogoProxyBaseResponse, model *GetStorePurchaseOrdersModel)
	SendUsers(ch chan *LogoProxyBaseResponse, model *SendUsersModel)
	SendStores(ch chan *LogoProxyBaseResponse, model *SendStoresModel)
	SendStoreTransferCollection(ch chan *LogoProxyBaseResponse, model *SendStoreTransferCollectionModel)
	SendInterWarehouseTransfer(ch chan *LogoProxyBaseResponse, model *SendInterWarehouseTransferModel)
	SendInventoryStocks(ch chan *LogoProxyBaseResponse, model *SendInventoryStocksModel, warehouseId string)
	SendStoreRefund(ch chan *LogoProxyBaseResponse, model *SendStoreRefundModel)
	GetStoreStocks(ch chan *LogoProxyStoreStocksResponse, model *GetStoreStocksModel)
	GlobalPurchaseOrderCancellation(ch chan *LogoProxyBaseResponse, model *GlobalPurchaseOrderCancellationModel)
	GetDispatchDocument(ch chan *LogoProxyBaseResponse, model *GetDispatchDocumentModel)
	SendStoreTransferShrinkage(ch chan *LogoProxyBaseResponse, model *SendStoreTransferShrinkageModel)
	SendOrderOfflinePayment(ch chan *LogoProxyBaseResponse, model *SendOrderOfflinePaymentProxyModel)
	SendRefundOrderOfflinePayment(ch chan *LogoProxyBaseResponse, model *SendRefundOrderOfflinePaymentProxyModel)
	SendSalesforceAccounts(ch chan *LogoProxyBaseResponse, model *SendSalesforceEventProxyModel)
	GetStoreStockCountApprovalStatus(ch chan *GetStoreStockCountApprovalStatusResponse, model *GetStoreStockCountApprovalStatusModel)
}

type LogoProxy struct {
	baseUrl       *url.URL
	securityKey   string
	invoiceApiUrl string
	timeout       time.Duration
	environment   env.IEnvironment
	loggr         logger.ILogger
	validatr      validator.IValidator
	cachr         cacher.ICacher
	client        *http.Client
}

// NewLogoProxy
// Returns a new LogoProxy.
func NewLogoProxy(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
) ILogoProxy {
	secret := environment.Get(env.LogoProxySecret)
	if secret == "" {
		loggr.Panic("Couldn't get LOGO_PROXY_SECRET environment variable !")
	}

	invoiceApiUrl := environment.Get(env.InvoiceApiUrl)

	timeout, timeoutErr := strconv.Atoi(environment.Get(env.LogoProxyTimeout))
	if timeoutErr != nil {
		loggr.Panic("Couldn't convert LOGO_PROXY_TIMEOUT environment variable to int !")
	}

	logoUrl, urlErr := url.Parse(environment.Get(env.LogoProxyUrl))
	if urlErr != nil {
		loggr.Panic("Couldn't convert LOGO_URL environment variable to url.URL !")
	}

	return &LogoProxy{
		environment:   environment,
		loggr:         loggr,
		validatr:      validatr,
		cachr:         cachr,
		baseUrl:       logoUrl,
		securityKey:   secret,
		invoiceApiUrl: invoiceApiUrl,
		timeout:       time.Second * time.Duration(timeout),
		client:        &http.Client{Timeout: time.Second * time.Duration(timeout)},
	}
}

func (l *LogoProxy) UpdateSupplierOrderCollection(
	ch chan *LogoProxyBaseResponse,
	model *UpdateSupplierOrderCollectionModel,
) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	// You can override the default timeout here.
	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()
	req, requestErr := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		l.baseUrl.String()+"/EditStoreOrderDispatch",
		bytes.NewBuffer(serializedModel),
	)

	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, err := l.client.Do(req)
	if err != nil {
		ch <- &LogoProxyBaseResponse{Error: err}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't update warehouse order dispatch ! details : %v", response)
		l.loggr.Error(
			"couldn't update warehouse order dispatch !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) UpdateSupplierCollectionProducts(
	ch chan *LogoProxyBaseResponse,
	model *UpdateSupplierCollectionProductsModel,
) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()
	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/EditStoreOrderReturn",
			bytes.NewBuffer(serializedModel),
		)

	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't update warehouse order return ! details : %v", response)
		l.loggr.Error(
			"couldn't update warehouse order return !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendGlobalPurchaseOrder(ch chan *LogoProxyBaseResponse, model *SendGlobalPurchaseOrderModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/GlobalPurchaseOrder",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't send global purchase order ! details : %v", response)
		l.loggr.Error(
			"couldn't insert global purchase order !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) AddOrUpdateGlobalSuppliers(ch chan *LogoProxyBaseResponse, model *AddOrUpdateGlobalSuppliersModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/GlobalSuppliers",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add or update global suppliers ! details : %v", response)
		l.loggr.Error(
			"couldn't add or update global suppliers !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendProductMaterials(ch chan *LogoProxyBaseResponse, model *SendProductMaterialsModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/MaterialList",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't transfer materials used in coffee products ! details : %v", response)
		l.loggr.Error(
			"couldn't transfer materials used in coffee products !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendMaterialSlips(ch chan *LogoProxyBaseResponse, model *SendMaterialSlipsModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/MaterialSlip",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add material slip ! details : %v", response)
		l.loggr.Error(
			"couldn't add material slip !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	l.loggr.Info("Stock Adjustment - Successful", zap.String("model", string(serializedModel)))

	ch <- &response
}

func (l *LogoProxy) SendRevertedRequest(ch chan *LogoProxyBaseResponse, model *SendRevertedRequestModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/MaterialSlip",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add material slip ! details : %v", response)
		l.loggr.Error(
			"couldn't add material slip !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %d is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendCompletedOrder(ch chan *LogoProxyBaseResponse, model *SendCompletedOrderModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(ctx, http.MethodPost, l.baseUrl.String()+"/Order", bytes.NewBuffer(serializedModel))
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add order ! details : %v", response)
		l.loggr.Error(
			"couldn't add order !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendOrderTip(ch chan *LogoProxyBaseResponse, model *SendOrderTipModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/OrderTip",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add order tip ! details : %v", response)
		l.loggr.Error(
			"couldn't add order tip !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) GetProductProperties(ch chan *GetProductPropertiesResponse, model *GetProductPropertiesModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetProductPropertiesResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &GetProductPropertiesResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/ProductInformation",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &GetProductPropertiesResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &GetProductPropertiesResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response GetProductPropertiesResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &GetProductPropertiesResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't get product information ! details : %v", response)
		l.loggr.Error(
			"couldn't add order tip !",
			zap.String("ResultMessage", response.ResultMsg),
		)
		ch <- &GetProductPropertiesResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendProducts(ch chan *LogoProxyBaseResponse, model *SendProductsModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/Products",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add products ! details : %v", response)
		l.loggr.Error(
			"couldn't add products !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) GetPurchaseOrderDetailWithPrice(
	ch chan *GetPurchaseOrderDetailWithPriceResponse,
	model *GetPurchaseOrderDetailWithPriceModel,
) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPurchaseOrderDetailWithPriceResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &GetPurchaseOrderDetailWithPriceResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/PurchaseOrderDetail",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &GetPurchaseOrderDetailWithPriceResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &GetPurchaseOrderDetailWithPriceResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response GetPurchaseOrderDetailWithPriceResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &GetPurchaseOrderDetailWithPriceResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add purchase order detail ! details : %v", response)
		l.loggr.Error(
			"couldn't add purchase order detail !",
			zap.String("ResultMessage", response.ResultMsg),
		)
		ch <- &GetPurchaseOrderDetailWithPriceResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendCompletedFullRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedRefundModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/RefundedOrder",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add refunded order ! details : %v", response)
		l.loggr.Error(
			"couldn't add refunded order !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendCompletedFullRefundBulk(ch chan *LogoProxyRefundedOrderBulkResponse, model *SendCompletedRefundBulkModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyRefundedOrderBulkResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyRefundedOrderBulkResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/RefundedOrderBulkTransmission",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyRefundedOrderBulkResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyRefundedOrderBulkResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyRefundedOrderBulkResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyRefundedOrderBulkResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add bulk refunded order ! details : %v", response)
		l.loggr.Error(
			"couldn't add refunded order !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.Any("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyRefundedOrderBulkResponse{}
			return
		}

		ch <- &LogoProxyRefundedOrderBulkResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendCompletedPartialRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedRefundModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/RefundedOrder",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add refunded order ! details : %v", response)
		l.loggr.Error(
			"couldn't add refunded order !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendCompletedTipRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedTipRefundModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/RefundedOrder",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add refunded order ! details : %v", response)
		l.loggr.Error(
			"couldn't add refunded order !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendNotRequiredFullRefund(ch chan *LogoProxyBaseResponse, model *SendNotRequiredFullRefundModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/RefundedOrderBeforeCollection",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add refunded order before collection ! details : %v", response)
		l.loggr.Error(
			"couldn't add refunded order before collection !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) GetPurchaseOrderDetail(
	ch chan *LogoProxyGetPurchaseOrderDetailResponse,
	model *GetPurchaseOrderDetailModel,
) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyGetPurchaseOrderDetailResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyGetPurchaseOrderDetailResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreOrderDetails",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyGetPurchaseOrderDetailResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyGetPurchaseOrderDetailResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyGetPurchaseOrderDetailResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyGetPurchaseOrderDetailResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add warehouse order details ! details : %v", response)
		l.loggr.Error(
			"couldn't add warehouse order details !",
			zap.String("ResultMessage", response.ResultMsg),
		)
		ch <- &LogoProxyGetPurchaseOrderDetailResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendSupplierOrderCollection(
	ch chan *LogoProxyBaseResponse,
	model *SendSupplierOrderCollectionModel,
) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreOrderReturn",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add store order return ! details : %v", response)
		l.loggr.Error(
			"couldn't add store order return !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) GetStorePurchaseOrders(ch chan *LogoProxyBaseResponse, model *GetStorePurchaseOrdersModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreOrders",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add warehouse order return ! details : %v", response)
		l.loggr.Error(
			"couldn't add warehouse order return !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendUsers(ch chan *LogoProxyBaseResponse, model *SendUsersModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(ctx, http.MethodPost, l.baseUrl.String()+"/Users", bytes.NewBuffer(serializedModel))
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add or update users ! details : %v", response)
		l.loggr.Error(
			"couldn't add or update users return !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendStores(ch chan *LogoProxyBaseResponse, model *SendStoresModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(ctx, http.MethodPost, l.baseUrl.String()+"/Stores", bytes.NewBuffer(serializedModel))
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add or update stores ! details : %v", response)
		l.loggr.Error(
			"couldn't add or update stores return !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendStoreTransferCollection(ch chan *LogoProxyBaseResponse, model *SendStoreTransferCollectionModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreTransfer",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add warehouse transfer ! details : %v", response)
		l.loggr.Error(
			"couldn't add warehouse transfer return !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendInterWarehouseTransfer(ch chan *LogoProxyBaseResponse, model *SendInterWarehouseTransferModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreTransfer",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add warehouse transfer ! details : %v", response)
		l.loggr.Error(
			"couldn't add warehouse transfer return !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendInventoryStocks(ch chan *LogoProxyBaseResponse, model *SendInventoryStocksModel, warehouseId string) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreStockCount",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add warehouse stock count ! details : %v", response)
		l.loggr.Error(
			"couldn't add warehouse stock count ! WarehouseId: "+warehouseId,
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
			zap.String("model", string(serializedModel)),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	l.loggr.Info("Stock count sent successfully. WarehouseId: " + warehouseId)

	ch <- &response
}

func (l *LogoProxy) SendStoreRefund(ch chan *LogoProxyBaseResponse, model *SendStoreRefundModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreRefund",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't add warehouse refund ! details : %v", response)
		l.loggr.Error(
			"couldn't add warehouse refund !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	l.loggr.Info("Store Refund - Successful", zap.String("model", string(serializedModel)))

	ch <- &response
}

func (l *LogoProxy) GetStoreStocks(ch chan *LogoProxyStoreStocksResponse, model *GetStoreStocksModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyStoreStocksResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyStoreStocksResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreStocks",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyStoreStocksResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyStoreStocksResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyStoreStocksResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyStoreStocksResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't get warehouse stocks ! details : %v", response)
		l.loggr.Error(
			"couldn't get warehouse stocks !",
			zap.String("ResultMessage", response.ResultMsg),
		)
		ch <- &LogoProxyStoreStocksResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) GlobalPurchaseOrderCancellation(ch chan *LogoProxyBaseResponse, model *GlobalPurchaseOrderCancellationModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/GlobalPurchaseOrderCancellation",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't cancel purchase order ! details : %v", response)
		l.loggr.Error(
			"couldn't cancel purchase order !",
			zap.String("ResultMessage", response.ResultMsg),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("Message ID: %s is duplicate record.", model.MessageId))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) addDefaultHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("SecurityKey", l.securityKey)
}

func (l *LogoProxy) GetDispatchDocument(ch chan *LogoProxyBaseResponse, model *GetDispatchDocumentModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr, ResultCode: http.StatusBadRequest}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr, ResultCode: http.StatusBadRequest}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Second)
	defer cancel()

	url := fmt.Sprintf(l.invoiceApiUrl, model.DispatchNumber)

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodGet,
			url,
			bytes.NewBuffer(serializedModel),
		)

	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr, ResultCode: http.StatusBadRequest}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr, ResultCode: http.StatusInternalServerError}
		return
	}
	defer resp.Body.Close()

	htmlData, err := ioutil.ReadAll(resp.Body)

	if err != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr, ResultCode: http.StatusInternalServerError}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't get dispatch document! details : %v", resp)
		l.loggr.Error(
			"Couldn't get dispatch document!",
			zap.String("ResultMessage", resp.Status),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse, ResultCode: http.StatusBadRequest}
		return
	}

	response := LogoProxyBaseResponse{
		Result:     string(htmlData),
		ResultCode: http.StatusOK,
		Error:      nil,
	}

	ch <- &response
}

func (l *LogoProxy) SendStoreTransferShrinkage(ch chan *LogoProxyBaseResponse, model *SendStoreTransferShrinkageModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/StoreTransferShrinkage",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't send warehouse transfer shrinkage! details : %v", response)
		l.loggr.Error(
			"couldn't warehouse transfer shrinkage !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}
	l.loggr.Info("Transfer Shrinkage - Successful", zap.String("model", string(serializedModel)))

	ch <- &response
}

func (l *LogoProxy) SendOrderOfflinePayment(ch chan *LogoProxyBaseResponse, model *SendOrderOfflinePaymentProxyModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/OrderPayment",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	l.loggr.Info("Send order offline payment. TrackingNumber: "+model.TrackingNumber,
		zap.Any("SendOrderOfflinePaymentProxyModel", model),
		zap.Any("SendOrderOfflinePaymentProxyResponse", response))

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't send order offline payment! details : %v", response)
		l.loggr.Error(
			"couldn't send order offline payment !",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("TrackingNumber: %s is duplicate record.", model.TrackingNumber))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendRefundOrderOfflinePayment(ch chan *LogoProxyBaseResponse, model *SendRefundOrderOfflinePaymentProxyModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/OrderRefundPayment",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	l.loggr.Info("Send refund order offline payment. TrackingNumber: "+model.TrackingNumber,
		zap.Any("SendRefundOrderOfflinePayment", model),
		zap.Any("SendRefundOrderOfflinePaymentProxyResponse", response))

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't send refund order payment! details : %v", response)
		l.loggr.Error(
			"couldn't send refund order payment!",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)

		if resp.StatusCode == http.StatusConflict {
			l.loggr.Info(fmt.Sprintf("TrackingNumber: %s is duplicate record.", model.TrackingNumber))
			ch <- &LogoProxyBaseResponse{}
			return
		}

		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) SendSalesforceAccounts(ch chan *LogoProxyBaseResponse, model *SendSalesforceEventProxyModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &LogoProxyBaseResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &LogoProxyBaseResponse{Error: serializationErr}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			l.baseUrl.String()+"/GlobalSalesForces",
			bytes.NewBuffer(serializedModel),
		)
	if requestErr != nil {
		ch <- &LogoProxyBaseResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &LogoProxyBaseResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response LogoProxyBaseResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &LogoProxyBaseResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't send salesforce event! details : %v", response)
		l.loggr.Error(
			"couldn't send salesforce event!",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
		)
		ch <- &LogoProxyBaseResponse{Error: errorResponse}
		return
	}

	ch <- &response
}

func (l *LogoProxy) GetStoreStockCountApprovalStatus(ch chan *GetStoreStockCountApprovalStatusResponse, model *GetStoreStockCountApprovalStatusModel) {
	modelErr := l.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetStoreStockCountApprovalStatusResponse{Error: modelErr}
		return
	}

	serializedModel, serializationErr := json.Marshal(model)
	if serializationErr != nil {
		ch <- &GetStoreStockCountApprovalStatusResponse{Error: serializationErr}
		return
	}

	thisTime := model.SentDateToLogo.Add(-time.Minute * 5)
	begDate := fmt.Sprintf("%d-%02d-%02dT%02d%s%02d%s%02d",
		thisTime.Year(), thisTime.Month(), thisTime.Day(),
		thisTime.Hour(), "%3A", thisTime.Minute(), "%3A", thisTime.Second())
	endDate := fmt.Sprintf("%d-%02d-%02dT%02d%s%02d%s%02d",
		time.Now().Year(), time.Now().Month(), time.Now().Day(),
		time.Now().Hour(), "%3A", time.Now().Minute(), "%3A", time.Now().Second())

	url := fmt.Sprintf("%s/StoreStockCount?BegDate=%s&EndDate=%s&StoreId=%s", l.baseUrl.String(), begDate, endDate, model.StoreId)

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()
	req, requestErr :=
		http.NewRequestWithContext(
			ctx,
			http.MethodGet,
			url,
			bytes.NewBuffer(serializedModel),
		)

	if requestErr != nil {
		ch <- &GetStoreStockCountApprovalStatusResponse{Error: requestErr}
		return
	}

	l.addDefaultHeaders(req)

	resp, respErr := l.client.Do(req)
	if respErr != nil {
		ch <- &GetStoreStockCountApprovalStatusResponse{Error: respErr}
		return
	}
	defer resp.Body.Close()

	var response GetStoreStockCountApprovalStatusResponse
	decodeErr := json.NewDecoder(resp.Body).Decode(&response)
	if decodeErr != nil {
		ch <- &GetStoreStockCountApprovalStatusResponse{Error: decodeErr}
		return
	}

	if resp.StatusCode != http.StatusOK {
		errorResponse := fmt.Errorf("couldn't get store stock count approval status! details : %v", response)
		l.loggr.Error(
			"couldn't get store stock count approval status!",
			zap.String("ResultMessage", response.ResultMsg),
		)
		ch <- &GetStoreStockCountApprovalStatusResponse{Error: errorResponse}
		return
	}

	ch <- &response
}
