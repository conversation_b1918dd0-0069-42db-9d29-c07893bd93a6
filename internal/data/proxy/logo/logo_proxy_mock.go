// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/proxy/logo/logo_proxy.go

// Package logo is a generated GoMock package.
package logo

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockILogoProxy is a mock of ILogoProxy interface.
type MockILogoProxy struct {
	ctrl     *gomock.Controller
	recorder *MockILogoProxyMockRecorder
}

// MockILogoProxyMockRecorder is the mock recorder for MockILogoProxy.
type MockILogoProxyMockRecorder struct {
	mock *MockILogoProxy
}

// NewMockILogoProxy creates a new mock instance.
func NewMockILogoProxy(ctrl *gomock.Controller) *MockILogoProxy {
	mock := &MockILogoProxy{ctrl: ctrl}
	mock.recorder = &MockILogoProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILogoProxy) EXPECT() *MockILogoProxyMockRecorder {
	return m.recorder
}

// AddOrUpdateGlobalSuppliers mocks base method.
func (m *MockILogoProxy) AddOrUpdateGlobalSuppliers(ch chan *LogoProxyBaseResponse, model *AddOrUpdateGlobalSuppliersModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddOrUpdateGlobalSuppliers", ch, model)
}

// AddOrUpdateGlobalSuppliers indicates an expected call of AddOrUpdateGlobalSuppliers.
func (mr *MockILogoProxyMockRecorder) AddOrUpdateGlobalSuppliers(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOrUpdateGlobalSuppliers", reflect.TypeOf((*MockILogoProxy)(nil).AddOrUpdateGlobalSuppliers), ch, model)
}

// GetDispatchDocument mocks base method.
func (m *MockILogoProxy) GetDispatchDocument(ch chan *LogoProxyBaseResponse, model *GetDispatchDocumentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetDispatchDocument", ch, model)
}

// GetDispatchDocument indicates an expected call of GetDispatchDocument.
func (mr *MockILogoProxyMockRecorder) GetDispatchDocument(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDispatchDocument", reflect.TypeOf((*MockILogoProxy)(nil).GetDispatchDocument), ch, model)
}

// GetProductProperties mocks base method.
func (m *MockILogoProxy) GetProductProperties(ch chan *GetProductPropertiesResponse, model *GetProductPropertiesModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProductProperties", ch, model)
}

// GetProductProperties indicates an expected call of GetProductProperties.
func (mr *MockILogoProxyMockRecorder) GetProductProperties(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductProperties", reflect.TypeOf((*MockILogoProxy)(nil).GetProductProperties), ch, model)
}

// GetPurchaseOrderDetail mocks base method.
func (m *MockILogoProxy) GetPurchaseOrderDetail(ch chan *LogoProxyGetPurchaseOrderDetailResponse, model *GetPurchaseOrderDetailModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPurchaseOrderDetail", ch, model)
}

// GetPurchaseOrderDetail indicates an expected call of GetPurchaseOrderDetail.
func (mr *MockILogoProxyMockRecorder) GetPurchaseOrderDetail(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseOrderDetail", reflect.TypeOf((*MockILogoProxy)(nil).GetPurchaseOrderDetail), ch, model)
}

// GetPurchaseOrderDetailWithPrice mocks base method.
func (m *MockILogoProxy) GetPurchaseOrderDetailWithPrice(ch chan *GetPurchaseOrderDetailWithPriceResponse, model *GetPurchaseOrderDetailWithPriceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPurchaseOrderDetailWithPrice", ch, model)
}

// GetPurchaseOrderDetailWithPrice indicates an expected call of GetPurchaseOrderDetailWithPrice.
func (mr *MockILogoProxyMockRecorder) GetPurchaseOrderDetailWithPrice(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseOrderDetailWithPrice", reflect.TypeOf((*MockILogoProxy)(nil).GetPurchaseOrderDetailWithPrice), ch, model)
}

// GetStorePurchaseOrders mocks base method.
func (m *MockILogoProxy) GetStorePurchaseOrders(ch chan *LogoProxyBaseResponse, model *GetStorePurchaseOrdersModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetStorePurchaseOrders", ch, model)
}

// GetStorePurchaseOrders indicates an expected call of GetStorePurchaseOrders.
func (mr *MockILogoProxyMockRecorder) GetStorePurchaseOrders(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStorePurchaseOrders", reflect.TypeOf((*MockILogoProxy)(nil).GetStorePurchaseOrders), ch, model)
}

// GetStoreStockCountApprovalStatus mocks base method.
func (m *MockILogoProxy) GetStoreStockCountApprovalStatus(ch chan *GetStoreStockCountApprovalStatusResponse, model *GetStoreStockCountApprovalStatusModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetStoreStockCountApprovalStatus", ch, model)
}

// GetStoreStockCountApprovalStatus indicates an expected call of GetStoreStockCountApprovalStatus.
func (mr *MockILogoProxyMockRecorder) GetStoreStockCountApprovalStatus(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoreStockCountApprovalStatus", reflect.TypeOf((*MockILogoProxy)(nil).GetStoreStockCountApprovalStatus), ch, model)
}

// GetStoreStocks mocks base method.
func (m *MockILogoProxy) GetStoreStocks(ch chan *LogoProxyStoreStocksResponse, model *GetStoreStocksModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetStoreStocks", ch, model)
}

// GetStoreStocks indicates an expected call of GetStoreStocks.
func (mr *MockILogoProxyMockRecorder) GetStoreStocks(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoreStocks", reflect.TypeOf((*MockILogoProxy)(nil).GetStoreStocks), ch, model)
}

// GlobalPurchaseOrderCancellation mocks base method.
func (m *MockILogoProxy) GlobalPurchaseOrderCancellation(ch chan *LogoProxyBaseResponse, model *GlobalPurchaseOrderCancellationModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GlobalPurchaseOrderCancellation", ch, model)
}

// GlobalPurchaseOrderCancellation indicates an expected call of GlobalPurchaseOrderCancellation.
func (mr *MockILogoProxyMockRecorder) GlobalPurchaseOrderCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GlobalPurchaseOrderCancellation", reflect.TypeOf((*MockILogoProxy)(nil).GlobalPurchaseOrderCancellation), ch, model)
}

// SendCompletedFullRefund mocks base method.
func (m *MockILogoProxy) SendCompletedFullRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedRefundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendCompletedFullRefund", ch, model)
}

// SendCompletedFullRefund indicates an expected call of SendCompletedFullRefund.
func (mr *MockILogoProxyMockRecorder) SendCompletedFullRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCompletedFullRefund", reflect.TypeOf((*MockILogoProxy)(nil).SendCompletedFullRefund), ch, model)
}

// SendCompletedFullRefundBulk mocks base method.
func (m *MockILogoProxy) SendCompletedFullRefundBulk(ch chan *LogoProxyRefundedOrderBulkResponse, model *SendCompletedRefundBulkModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendCompletedFullRefundBulk", ch, model)
}

// SendCompletedFullRefundBulk indicates an expected call of SendCompletedFullRefundBulk.
func (mr *MockILogoProxyMockRecorder) SendCompletedFullRefundBulk(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCompletedFullRefundBulk", reflect.TypeOf((*MockILogoProxy)(nil).SendCompletedFullRefundBulk), ch, model)
}

// SendCompletedOrder mocks base method.
func (m *MockILogoProxy) SendCompletedOrder(ch chan *LogoProxyBaseResponse, model *SendCompletedOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendCompletedOrder", ch, model)
}

// SendCompletedOrder indicates an expected call of SendCompletedOrder.
func (mr *MockILogoProxyMockRecorder) SendCompletedOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCompletedOrder", reflect.TypeOf((*MockILogoProxy)(nil).SendCompletedOrder), ch, model)
}

// SendCompletedPartialRefund mocks base method.
func (m *MockILogoProxy) SendCompletedPartialRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedRefundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendCompletedPartialRefund", ch, model)
}

// SendCompletedPartialRefund indicates an expected call of SendCompletedPartialRefund.
func (mr *MockILogoProxyMockRecorder) SendCompletedPartialRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCompletedPartialRefund", reflect.TypeOf((*MockILogoProxy)(nil).SendCompletedPartialRefund), ch, model)
}

// SendCompletedTipRefund mocks base method.
func (m *MockILogoProxy) SendCompletedTipRefund(ch chan *LogoProxyBaseResponse, model *SendCompletedTipRefundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendCompletedTipRefund", ch, model)
}

// SendCompletedTipRefund indicates an expected call of SendCompletedTipRefund.
func (mr *MockILogoProxyMockRecorder) SendCompletedTipRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCompletedTipRefund", reflect.TypeOf((*MockILogoProxy)(nil).SendCompletedTipRefund), ch, model)
}

// SendGlobalPurchaseOrder mocks base method.
func (m *MockILogoProxy) SendGlobalPurchaseOrder(ch chan *LogoProxyBaseResponse, model *SendGlobalPurchaseOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendGlobalPurchaseOrder", ch, model)
}

// SendGlobalPurchaseOrder indicates an expected call of SendGlobalPurchaseOrder.
func (mr *MockILogoProxyMockRecorder) SendGlobalPurchaseOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGlobalPurchaseOrder", reflect.TypeOf((*MockILogoProxy)(nil).SendGlobalPurchaseOrder), ch, model)
}

// SendInterWarehouseTransfer mocks base method.
func (m *MockILogoProxy) SendInterWarehouseTransfer(ch chan *LogoProxyBaseResponse, model *SendInterWarehouseTransferModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendInterWarehouseTransfer", ch, model)
}

// SendInterWarehouseTransfer indicates an expected call of SendInterWarehouseTransfer.
func (mr *MockILogoProxyMockRecorder) SendInterWarehouseTransfer(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendInterWarehouseTransfer", reflect.TypeOf((*MockILogoProxy)(nil).SendInterWarehouseTransfer), ch, model)
}

// SendInventoryStocks mocks base method.
func (m *MockILogoProxy) SendInventoryStocks(ch chan *LogoProxyBaseResponse, model *SendInventoryStocksModel, warehouseId string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendInventoryStocks", ch, model, warehouseId)
}

// SendInventoryStocks indicates an expected call of SendInventoryStocks.
func (mr *MockILogoProxyMockRecorder) SendInventoryStocks(ch, model, warehouseId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendInventoryStocks", reflect.TypeOf((*MockILogoProxy)(nil).SendInventoryStocks), ch, model, warehouseId)
}

// SendMaterialSlips mocks base method.
func (m *MockILogoProxy) SendMaterialSlips(ch chan *LogoProxyBaseResponse, model *SendMaterialSlipsModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendMaterialSlips", ch, model)
}

// SendMaterialSlips indicates an expected call of SendMaterialSlips.
func (mr *MockILogoProxyMockRecorder) SendMaterialSlips(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMaterialSlips", reflect.TypeOf((*MockILogoProxy)(nil).SendMaterialSlips), ch, model)
}

// SendNotRequiredFullRefund mocks base method.
func (m *MockILogoProxy) SendNotRequiredFullRefund(ch chan *LogoProxyBaseResponse, model *SendNotRequiredFullRefundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendNotRequiredFullRefund", ch, model)
}

// SendNotRequiredFullRefund indicates an expected call of SendNotRequiredFullRefund.
func (mr *MockILogoProxyMockRecorder) SendNotRequiredFullRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendNotRequiredFullRefund", reflect.TypeOf((*MockILogoProxy)(nil).SendNotRequiredFullRefund), ch, model)
}

// SendOrderOfflinePayment mocks base method.
func (m *MockILogoProxy) SendOrderOfflinePayment(ch chan *LogoProxyBaseResponse, model *SendOrderOfflinePaymentProxyModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderOfflinePayment", ch, model)
}

// SendOrderOfflinePayment indicates an expected call of SendOrderOfflinePayment.
func (mr *MockILogoProxyMockRecorder) SendOrderOfflinePayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderOfflinePayment", reflect.TypeOf((*MockILogoProxy)(nil).SendOrderOfflinePayment), ch, model)
}

// SendOrderTip mocks base method.
func (m *MockILogoProxy) SendOrderTip(ch chan *LogoProxyBaseResponse, model *SendOrderTipModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderTip", ch, model)
}

// SendOrderTip indicates an expected call of SendOrderTip.
func (mr *MockILogoProxyMockRecorder) SendOrderTip(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderTip", reflect.TypeOf((*MockILogoProxy)(nil).SendOrderTip), ch, model)
}

// SendProductMaterials mocks base method.
func (m *MockILogoProxy) SendProductMaterials(ch chan *LogoProxyBaseResponse, model *SendProductMaterialsModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendProductMaterials", ch, model)
}

// SendProductMaterials indicates an expected call of SendProductMaterials.
func (mr *MockILogoProxyMockRecorder) SendProductMaterials(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendProductMaterials", reflect.TypeOf((*MockILogoProxy)(nil).SendProductMaterials), ch, model)
}

// SendProducts mocks base method.
func (m *MockILogoProxy) SendProducts(ch chan *LogoProxyBaseResponse, model *SendProductsModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendProducts", ch, model)
}

// SendProducts indicates an expected call of SendProducts.
func (mr *MockILogoProxyMockRecorder) SendProducts(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendProducts", reflect.TypeOf((*MockILogoProxy)(nil).SendProducts), ch, model)
}

// SendRefundOrderOfflinePayment mocks base method.
func (m *MockILogoProxy) SendRefundOrderOfflinePayment(ch chan *LogoProxyBaseResponse, model *SendRefundOrderOfflinePaymentProxyModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendRefundOrderOfflinePayment", ch, model)
}

// SendRefundOrderOfflinePayment indicates an expected call of SendRefundOrderOfflinePayment.
func (mr *MockILogoProxyMockRecorder) SendRefundOrderOfflinePayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRefundOrderOfflinePayment", reflect.TypeOf((*MockILogoProxy)(nil).SendRefundOrderOfflinePayment), ch, model)
}

// SendRevertedRequest mocks base method.
func (m *MockILogoProxy) SendRevertedRequest(ch chan *LogoProxyBaseResponse, model *SendRevertedRequestModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendRevertedRequest", ch, model)
}

// SendRevertedRequest indicates an expected call of SendRevertedRequest.
func (mr *MockILogoProxyMockRecorder) SendRevertedRequest(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRevertedRequest", reflect.TypeOf((*MockILogoProxy)(nil).SendRevertedRequest), ch, model)
}

// SendSalesforceAccounts mocks base method.
func (m *MockILogoProxy) SendSalesforceAccounts(ch chan *LogoProxyBaseResponse, model *SendSalesforceEventProxyModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSalesforceAccounts", ch, model)
}

// SendSalesforceAccounts indicates an expected call of SendSalesforceAccounts.
func (mr *MockILogoProxyMockRecorder) SendSalesforceAccounts(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSalesforceAccounts", reflect.TypeOf((*MockILogoProxy)(nil).SendSalesforceAccounts), ch, model)
}

// SendStoreRefund mocks base method.
func (m *MockILogoProxy) SendStoreRefund(ch chan *LogoProxyBaseResponse, model *SendStoreRefundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendStoreRefund", ch, model)
}

// SendStoreRefund indicates an expected call of SendStoreRefund.
func (mr *MockILogoProxyMockRecorder) SendStoreRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStoreRefund", reflect.TypeOf((*MockILogoProxy)(nil).SendStoreRefund), ch, model)
}

// SendStoreTransferCollection mocks base method.
func (m *MockILogoProxy) SendStoreTransferCollection(ch chan *LogoProxyBaseResponse, model *SendStoreTransferCollectionModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendStoreTransferCollection", ch, model)
}

// SendStoreTransferCollection indicates an expected call of SendStoreTransferCollection.
func (mr *MockILogoProxyMockRecorder) SendStoreTransferCollection(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStoreTransferCollection", reflect.TypeOf((*MockILogoProxy)(nil).SendStoreTransferCollection), ch, model)
}

// SendStoreTransferShrinkage mocks base method.
func (m *MockILogoProxy) SendStoreTransferShrinkage(ch chan *LogoProxyBaseResponse, model *SendStoreTransferShrinkageModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendStoreTransferShrinkage", ch, model)
}

// SendStoreTransferShrinkage indicates an expected call of SendStoreTransferShrinkage.
func (mr *MockILogoProxyMockRecorder) SendStoreTransferShrinkage(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStoreTransferShrinkage", reflect.TypeOf((*MockILogoProxy)(nil).SendStoreTransferShrinkage), ch, model)
}

// SendStores mocks base method.
func (m *MockILogoProxy) SendStores(ch chan *LogoProxyBaseResponse, model *SendStoresModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendStores", ch, model)
}

// SendStores indicates an expected call of SendStores.
func (mr *MockILogoProxyMockRecorder) SendStores(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStores", reflect.TypeOf((*MockILogoProxy)(nil).SendStores), ch, model)
}

// SendSupplierOrderCollection mocks base method.
func (m *MockILogoProxy) SendSupplierOrderCollection(ch chan *LogoProxyBaseResponse, model *SendSupplierOrderCollectionModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSupplierOrderCollection", ch, model)
}

// SendSupplierOrderCollection indicates an expected call of SendSupplierOrderCollection.
func (mr *MockILogoProxyMockRecorder) SendSupplierOrderCollection(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSupplierOrderCollection", reflect.TypeOf((*MockILogoProxy)(nil).SendSupplierOrderCollection), ch, model)
}

// SendUsers mocks base method.
func (m *MockILogoProxy) SendUsers(ch chan *LogoProxyBaseResponse, model *SendUsersModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendUsers", ch, model)
}

// SendUsers indicates an expected call of SendUsers.
func (mr *MockILogoProxyMockRecorder) SendUsers(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendUsers", reflect.TypeOf((*MockILogoProxy)(nil).SendUsers), ch, model)
}

// UpdateSupplierCollectionProducts mocks base method.
func (m *MockILogoProxy) UpdateSupplierCollectionProducts(ch chan *LogoProxyBaseResponse, model *UpdateSupplierCollectionProductsModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateSupplierCollectionProducts", ch, model)
}

// UpdateSupplierCollectionProducts indicates an expected call of UpdateSupplierCollectionProducts.
func (mr *MockILogoProxyMockRecorder) UpdateSupplierCollectionProducts(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSupplierCollectionProducts", reflect.TypeOf((*MockILogoProxy)(nil).UpdateSupplierCollectionProducts), ch, model)
}

// UpdateSupplierOrderCollection mocks base method.
func (m *MockILogoProxy) UpdateSupplierOrderCollection(ch chan *LogoProxyBaseResponse, model *UpdateSupplierOrderCollectionModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateSupplierOrderCollection", ch, model)
}

// UpdateSupplierOrderCollection indicates an expected call of UpdateSupplierOrderCollection.
func (mr *MockILogoProxyMockRecorder) UpdateSupplierOrderCollection(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSupplierOrderCollection", reflect.TypeOf((*MockILogoProxy)(nil).UpdateSupplierOrderCollection), ch, model)
}
