package logo

import "time"

type LogoProxyBaseResponse struct {
	ResultCode int    `json:"ResultCode"`
	ResultMsg  string `json:"ResultMsg"`
	Result     string `json:"Result"`
	Error      error  `json:"-"`
}

type LogoProxyStoreStocksResponse struct {
	ResultCode int
	ResultMsg  string
	Result     struct {
		StoreStockList []LogoProxyStoreStockList `json:"StoreStockList"`
	}
	Error error `json:"-"`
}
type LogoProxyStoreStockList struct {
	StoreId       string `json:"StoreId"`
	WarehouseType int    `json:"WarehouseType"`
	ProductId     string `json:"ProductId"`
	MaterialCode  string `json:"MaterialCode"`
	Quantity      int    `json:"Quantity"`
}

type GetProductPropertiesResponse struct {
	ResultCode int
	ResultMsg  string
	Result     []GetProductPropertiesResult
	Error      error `json:"-"`
}

type ProductInformation struct {
	ProductID           string
	Width               int
	Height              int
	Length              int
	GrossWeight         int
	SupplierProductCode string
	ShelfLife           int
	PalletType          string
	UnitPerPackage      int
	NetPurchasePrice    int
}

type GetProductPropertiesResult struct {
	ProductInformationList []ProductInformation
}

type GetPurchaseOrderDetailWithPriceResponse struct {
	ResultCode int
	ResultMsg  string
	Result     []GetPurchaseOrderDetailWithPriceResult
	Error      error `json:"-"`
}

type GetPurchaseOrderDetailWithPriceResultProducts struct {
	ProductID    string
	MaterialCode string
	Quantity     int
	Price        int
}

type GetPurchaseOrderDetailWithPriceResult struct {
	SupplierCode string
	OrderID      string
	OrderDate    time.Time
	StoreID      string
	Products     []GetPurchaseOrderDetailWithPriceResultProducts
}

type LogoProxyGetPurchaseOrderDetailResponse struct {
	ResultCode int
	ResultMsg  string
	Result     []StoreOrderDetailsResult
	Error      error `json:"-"`
}

type StoreOrderDetailsResult struct {
	StoreId    int
	SupplierId string
	OrderDate  string
	OrderItems []OrderItems
}

type OrderItems struct {
	MaterialCode string
	ProductName  string
	Unit         string
	Quantity     int
}

type GetStoreStockCountApprovalStatusResponse struct {
	ResultCode int
	ResultMsg  string
	Result     []CountDetail
	Error      error `json:"-"`
}

type CountDetail struct {
	Status                 int
	StoreId                string
	SKU                    string
	StoreCount             int
	LogoStockBeforeProcess int
	LogoStockAfterProcess  int
}

type LogoProxyRefundedOrderBulkResponse struct {
	ResultCode int                       `json:"ResultCode"`
	ResultMsg  string                    `json:"ResultMsg"`
	Result     []RefundedOrderBulkResult `json:"Result"`
	Error      error                     `json:"-"`
}

type RefundedOrderBulkResult struct {
	TrackingNumber string `json:"TrackingNumber"`
	ResultCode     int    `json:"ResultCode"`
	ResultMsg      string `json:"ResultMsg"`
}
