package supplier

import (
	"context"
	"crypto/tls"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"
	"time"

	pbV4 "github.com/deliveryhero/dh-nv-proto-golang/packages/supplier_portal/v4/supplierpb"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type ISupplierProxy interface {
	GetSuppliers(ch chan *GetSuppliersResponse, model GetSuppliersModel)
	GetSupplierListWithDetails(ch chan *GetSupplierListWithDetailsResponse, model GetSupplierListWithDetailModel)
}

type SupplierProxy struct {
	environment  env.IEnvironment
	loggr        logger.ILogger
	validatr     validator.IValidator
	cachr        cacher.ICacher
	grpcV4Client pbV4.SupplierServiceClient
	grpcTimeout  int
}

func NewSupplierProxy(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) ISupplierProxy {

	adress := environment.Get(env.SupplierProxyGrpcUrl)
	timeout, timeoutErr := strconv.Atoi(environment.Get(env.SupplierProxyGrpcTimeout))

	if timeoutErr != nil {
		loggr.Error("Couldn't convert grpc timeout to int !")
	}

	creds := credentials.NewTLS(&tls.Config{InsecureSkipVerify: false})
	ctx, _ := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	conn, err := grpc.DialContext(ctx, adress, grpc.WithTransportCredentials(creds))

	if err != nil {
		loggr.Error("Couldn't establish grpc dial connection !", zap.String("Error", err.Error()))
	}

	return &SupplierProxy{
		environment:  environment,
		loggr:        loggr,
		validatr:     validatr,
		cachr:        cachr,
		grpcV4Client: pbV4.NewSupplierServiceClient(conn),
		grpcTimeout:  timeout,
	}
}

func (s *SupplierProxy) GetSuppliers(ch chan *GetSuppliersResponse, model GetSuppliersModel) {
	validationErr := s.validatr.ValidateStruct(model)
	if validationErr != nil {
		ch <- &GetSuppliersResponse{
			Error: validationErr,
		}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*time.Duration(s.grpcTimeout))
	defer cancel()

	response, err := s.grpcV4Client.GetSuppliersUpdatedAfter(ctx, &pbV4.GetSuppliersUpdatedAfterRequest{
		Page:      model.Page,
		PageSize:  model.PageSize,
		UpdatedAt: model.UpdatedAt,
	})

	if err != nil {
		ch <- &GetSuppliersResponse{
			Error:    err,
			Response: nil,
		}
		return
	}

	ch <- &GetSuppliersResponse{
		Response: response,
		Error:    nil,
	}
}

func (s *SupplierProxy) GetSupplierListWithDetails(ch chan *GetSupplierListWithDetailsResponse, model GetSupplierListWithDetailModel) {
	validationErr := s.validatr.ValidateStruct(model)
	if validationErr != nil {
		ch <- &GetSupplierListWithDetailsResponse{
			Error: validationErr,
		}
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*time.Duration(s.grpcTimeout))
	defer cancel()

	response, err := s.grpcV4Client.GetByIdsWithDetails(ctx, &pbV4.GetByIdsWithDetailsRequest{
		Ids: model.Ids,
	})

	if err != nil {
		ch <- &GetSupplierListWithDetailsResponse{
			Error:        err,
			SupplierList: nil,
		}
		return
	}

	ch <- &GetSupplierListWithDetailsResponse{
		SupplierList: response.Suppliers,
		Error:        nil,
	}
}
