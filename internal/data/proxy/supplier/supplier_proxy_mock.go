// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/proxy/supplier/supplier_proxy.go

// Package supplier is a generated GoMock package.
package supplier

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISupplierProxy is a mock of ISupplierProxy interface.
type MockISupplierProxy struct {
	ctrl     *gomock.Controller
	recorder *MockISupplierProxyMockRecorder
}

// MockISupplierProxyMockRecorder is the mock recorder for MockISupplierProxy.
type MockISupplierProxyMockRecorder struct {
	mock *MockISupplierProxy
}

// NewMockISupplierProxy creates a new mock instance.
func NewMockISupplierProxy(ctrl *gomock.Controller) *MockISupplierProxy {
	mock := &MockISupplierProxy{ctrl: ctrl}
	mock.recorder = &MockISupplierProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISupplierProxy) EXPECT() *MockISupplierProxyMockRecorder {
	return m.recorder
}

// GetSupplierListWithDetails mocks base method.
func (m *MockISupplierProxy) GetSupplierListWithDetails(ch chan *GetSupplierListWithDetailsResponse, model GetSupplierListWithDetailModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetSupplierListWithDetails", ch, model)
}

// GetSupplierListWithDetails indicates an expected call of GetSupplierListWithDetails.
func (mr *MockISupplierProxyMockRecorder) GetSupplierListWithDetails(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupplierListWithDetails", reflect.TypeOf((*MockISupplierProxy)(nil).GetSupplierListWithDetails), ch, model)
}

// GetSuppliers mocks base method.
func (m *MockISupplierProxy) GetSuppliers(ch chan *GetSuppliersResponse, model GetSuppliersModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetSuppliers", ch, model)
}

// GetSuppliers indicates an expected call of GetSuppliers.
func (mr *MockISupplierProxyMockRecorder) GetSuppliers(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuppliers", reflect.TypeOf((*MockISupplierProxy)(nil).GetSuppliers), ch, model)
}
