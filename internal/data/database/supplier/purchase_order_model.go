package supplier

import "time"

type AddPurchaseOrderModel struct {
	OrderId        string `json:"order_id" validate:"required"`
	OrderReference string `json:"order_reference" validate:"required"`
	OrderType      string `json:"order_type" validate:"required"`
	EventType      string `json:"event_type" validate:"required"`
}

type GetPurchaseOrderModel struct {
	OrderId   string `json:"order_id" validate:"required"`
	EventType string `json:"event_type" validate:"required"`
}

type GetReturnReasonModel struct {
	Reason string `json:"reason" validate:"required"`
}

type AddPurchaseOrderRefundModel struct {
	OrderId        string `json:"order_id" validate:"required"`
	OrderReference string `json:"order_reference" validate:"required"`
}

type GetPurchaseOrderRefundModel struct {
	OrderReference string `json:"order_reference" validate:"required"`
}

type AddPurchaseOrderInboundModel struct {
	MessageId      string    `json:"message_id"`
	OrderId        string    `json:"order_id" validate:"required"`
	OrderReference string    `json:"order_reference"`
	BillingId      string    `json:"billing_id"`
	BillingDate    time.Time `json:"billing_date"`
	OrderType      string    `json:"order_type"`
}

type GetPurchaseOrderInboundModel struct {
	OrderReference string `json:"order_reference"`
}

type GetProductReturnModel struct {
	ContentType     string `json:"content_type" validate:"required"`
	ReturnReference string `json:"return_reference" validate:"required"`
}

type AddProductReturnModel struct {
	ContentType     string    `json:"content_type" validate:"required"`
	ReturnId        string    `json:"return_id" validate:"required"`
	ReturnReference string    `json:"return_reference" validate:"required"`
	WarehouseId     string    `json:"warehouse_id" validate:"required"`
	ReasonType      string    `json:"reason_type" validate:"required"`
	CompletedAt     time.Time `json:"completed_at" validate:"required"`
}
