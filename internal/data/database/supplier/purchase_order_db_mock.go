// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/supplier/purchase_order_db.go

// Package supplier is a generated GoMock package.
package supplier

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIPurchaseOrderDb is a mock of IPurchaseOrderDb interface.
type MockIPurchaseOrderDb struct {
	ctrl     *gomock.Controller
	recorder *MockIPurchaseOrderDbMockRecorder
}

// MockIPurchaseOrderDbMockRecorder is the mock recorder for MockIPurchaseOrderDb.
type MockIPurchaseOrderDbMockRecorder struct {
	mock *MockIPurchaseOrderDb
}

// NewMockIPurchaseOrderDb creates a new mock instance.
func NewMockIPurchaseOrderDb(ctrl *gomock.Controller) *MockIPurchaseOrderDb {
	mock := &MockIPurchaseOrderDb{ctrl: ctrl}
	mock.recorder = &MockIPurchaseOrderDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPurchaseOrderDb) EXPECT() *MockIPurchaseOrderDbMockRecorder {
	return m.recorder
}

// AddProductReturn mocks base method.
func (m *MockIPurchaseOrderDb) AddProductReturn(ch chan *PurchaseOrderDbBaseResponse, model *AddProductReturnModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddProductReturn", ch, model)
}

// AddProductReturn indicates an expected call of AddProductReturn.
func (mr *MockIPurchaseOrderDbMockRecorder) AddProductReturn(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddProductReturn", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).AddProductReturn), ch, model)
}

// AddPurchaseOrder mocks base method.
func (m *MockIPurchaseOrderDb) AddPurchaseOrder(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddPurchaseOrder", ch, model)
}

// AddPurchaseOrder indicates an expected call of AddPurchaseOrder.
func (mr *MockIPurchaseOrderDbMockRecorder) AddPurchaseOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPurchaseOrder", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).AddPurchaseOrder), ch, model)
}

// AddPurchaseOrderInbound mocks base method.
func (m *MockIPurchaseOrderDb) AddPurchaseOrderInbound(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderInboundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddPurchaseOrderInbound", ch, model)
}

// AddPurchaseOrderInbound indicates an expected call of AddPurchaseOrderInbound.
func (mr *MockIPurchaseOrderDbMockRecorder) AddPurchaseOrderInbound(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPurchaseOrderInbound", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).AddPurchaseOrderInbound), ch, model)
}

// AddPurchaseOrderRefund mocks base method.
func (m *MockIPurchaseOrderDb) AddPurchaseOrderRefund(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderRefundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddPurchaseOrderRefund", ch, model)
}

// AddPurchaseOrderRefund indicates an expected call of AddPurchaseOrderRefund.
func (mr *MockIPurchaseOrderDbMockRecorder) AddPurchaseOrderRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPurchaseOrderRefund", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).AddPurchaseOrderRefund), ch, model)
}

// GetProductReturn mocks base method.
func (m *MockIPurchaseOrderDb) GetProductReturn(ch chan *GetProductReturnResponse, model *GetProductReturnModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProductReturn", ch, model)
}

// GetProductReturn indicates an expected call of GetProductReturn.
func (mr *MockIPurchaseOrderDbMockRecorder) GetProductReturn(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductReturn", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).GetProductReturn), ch, model)
}

// GetPurchaseOrder mocks base method.
func (m *MockIPurchaseOrderDb) GetPurchaseOrder(ch chan *GetPurchaseOrderDbResponse, model *GetPurchaseOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPurchaseOrder", ch, model)
}

// GetPurchaseOrder indicates an expected call of GetPurchaseOrder.
func (mr *MockIPurchaseOrderDbMockRecorder) GetPurchaseOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseOrder", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).GetPurchaseOrder), ch, model)
}

// GetPurchaseOrderInbound mocks base method.
func (m *MockIPurchaseOrderDb) GetPurchaseOrderInbound(ch chan *GetPurchaseOrderInboundResponse, model *GetPurchaseOrderInboundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPurchaseOrderInbound", ch, model)
}

// GetPurchaseOrderInbound indicates an expected call of GetPurchaseOrderInbound.
func (mr *MockIPurchaseOrderDbMockRecorder) GetPurchaseOrderInbound(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseOrderInbound", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).GetPurchaseOrderInbound), ch, model)
}

// GetPurchaseOrderRefund mocks base method.
func (m *MockIPurchaseOrderDb) GetPurchaseOrderRefund(ch chan *GetPurchaseOrderRefundResponse, model *GetPurchaseOrderRefundModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPurchaseOrderRefund", ch, model)
}

// GetPurchaseOrderRefund indicates an expected call of GetPurchaseOrderRefund.
func (mr *MockIPurchaseOrderDbMockRecorder) GetPurchaseOrderRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseOrderRefund", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).GetPurchaseOrderRefund), ch, model)
}

// GetReturnReason mocks base method.
func (m *MockIPurchaseOrderDb) GetReturnReason(ch chan *GetReturnReasonResponse, model *GetReturnReasonModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetReturnReason", ch, model)
}

// GetReturnReason indicates an expected call of GetReturnReason.
func (mr *MockIPurchaseOrderDbMockRecorder) GetReturnReason(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReturnReason", reflect.TypeOf((*MockIPurchaseOrderDb)(nil).GetReturnReason), ch, model)
}
