package supplier

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	_ "github.com/lib/pq"
)

type IPurchaseOrderDb interface {
	AddPurchaseOrder(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderModel)
	GetPurchaseOrder(ch chan *GetPurchaseOrderDbResponse, model *GetPurchaseOrderModel)
	GetReturnReason(ch chan *GetReturnReasonResponse, model *GetReturnReasonModel)
	AddPurchaseOrderRefund(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderRefundModel)
	GetPurchaseOrderRefund(ch chan *GetPurchaseOrderRefundResponse, model *GetPurchaseOrderRefundModel)
	AddPurchaseOrderInbound(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderInboundModel)
	GetPurchaseOrderInbound(ch chan *GetPurchaseOrderInboundResponse, model *GetPurchaseOrderInboundModel)
	GetProductReturn(ch chan *GetProductReturnResponse, model *GetProductReturnModel)
	AddProductReturn(ch chan *PurchaseOrderDbBaseResponse, model *AddProductReturnModel)
}

type PurchaseOrderDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

// NewPurchaseOrderDb
// Returns a new PurchaseOrderDb.
func NewPurchaseOrderDb(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IPurchaseOrderDb {
	db := PurchaseOrderDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

// AddPurchaseOrder
// Add purchase order to postgresql db.
func (d *PurchaseOrderDb) AddPurchaseOrder(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `insert into purchase_orders (order_id, order_reference,order_type,event_type,created_date) values ($1, $2,$3, $4, current_timestamp)`

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.OrderReference, model.OrderType, model.EventType)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: err}
		return
	}

	if rows != 1 {
		ch <- &PurchaseOrderDbBaseResponse{Error: errors.New("could not add record")}
		return
	}

	ch <- &PurchaseOrderDbBaseResponse{}
}

func (d *PurchaseOrderDb) GetPurchaseOrder(ch chan *GetPurchaseOrderDbResponse, model *GetPurchaseOrderModel) {
	err := d.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &GetPurchaseOrderDbResponse{Error: err}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetPurchaseOrderDbResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select id, order_id, order_reference, order_type, event_type, created_date from purchase_orders
	where order_id = $1 and event_type = $2`

	var purchaseOrder GetPurchaseOrderDbResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId, model.EventType).Scan(
		&purchaseOrder.Id,
		&purchaseOrder.OrderId,
		&purchaseOrder.OrderReference,
		&purchaseOrder.OrderType,
		&purchaseOrder.EventType,
		&purchaseOrder.CreatedDate)

	if dbErr != nil || dbErr == sql.ErrNoRows {
		ch <- &GetPurchaseOrderDbResponse{Error: dbErr}
		return
	}

	ch <- &purchaseOrder
	return
}

// GetReturnReason
// Gets return reason from postgresql db.
func (d *PurchaseOrderDb) GetReturnReason(ch chan *GetReturnReasonResponse, model *GetReturnReasonModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetReturnReasonResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetReturnReasonResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()
	query := `select id, code, reason from return_reasons where reason = $1`

	var returnReason GetReturnReasonResponse
	dbErr := connection.QueryRowContext(ctx, query, model.Reason).Scan(&returnReason.Id, &returnReason.Code, &returnReason.Reason)
	if dbErr != nil {
		ch <- &GetReturnReasonResponse{Error: err}
		return
	}

	ch <- &returnReason
}

func (d *PurchaseOrderDb) AddPurchaseOrderRefund(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderRefundModel) {
	err := d.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `insert into purchase_order_refunds(order_id,order_reference,created_date) values ($1, $2, current_timestamp)`
	result, err := connection.ExecContext(ctx, query, model.OrderId, model.OrderReference)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}
	affectedRowCount, err := result.RowsAffected()
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}
	if affectedRowCount == 0 {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: errors.New("could not added"),
		}
		return
	}

	ch <- &PurchaseOrderDbBaseResponse{Error: nil}
	return
}

func (d *PurchaseOrderDb) GetPurchaseOrderRefund(ch chan *GetPurchaseOrderRefundResponse, model *GetPurchaseOrderRefundModel) {
	err := d.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &GetPurchaseOrderRefundResponse{
			Error: err,
		}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetPurchaseOrderRefundResponse{
			Error: err,
		}
		return
	}

	defer connection.Close()
	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select id, order_id, order_reference, created_date from purchase_order_refunds
  			  where order_reference = $1`

	var purchaseOrder GetPurchaseOrderRefundResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderReference).Scan(
		&purchaseOrder.Id,
		&purchaseOrder.OrderId,
		&purchaseOrder.OrderReference,
		&purchaseOrder.CreatedDate)

	if dbErr != nil {
		ch <- &GetPurchaseOrderRefundResponse{
			Error: dbErr,
		}
		return
	}

	ch <- &purchaseOrder
	return
}

func (d *PurchaseOrderDb) AddPurchaseOrderInbound(ch chan *PurchaseOrderDbBaseResponse, model *AddPurchaseOrderInboundModel) {
	err := d.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `insert into purchase_order_inbounds(message_id, order_id, order_reference, billing_id, billing_date, created_date, order_type) 
			  values ($1, $2, $3, $4, $5, current_timestamp, $6)`
	result, err := connection.ExecContext(ctx, query, model.MessageId, model.OrderId, model.OrderReference, model.BillingId, model.BillingDate, model.OrderType)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}
	affectedRowCount, err := result.RowsAffected()
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: err,
		}
		return
	}
	if affectedRowCount == 0 {
		ch <- &PurchaseOrderDbBaseResponse{
			Error: errors.New("could not added"),
		}
		return
	}

	ch <- &PurchaseOrderDbBaseResponse{Error: nil}
	return
}

func (d *PurchaseOrderDb) GetPurchaseOrderInbound(ch chan *GetPurchaseOrderInboundResponse, model *GetPurchaseOrderInboundModel) {
	err := d.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &GetPurchaseOrderInboundResponse{
			Error: err,
		}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetPurchaseOrderInboundResponse{
			Error: err,
		}
		return
	}

	defer connection.Close()
	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select id, message_id,order_id, order_reference, billing_id, billing_date, created_date, order_type from purchase_order_inbounds
  			  where order_reference = $1`

	var purchaseOrderInbound GetPurchaseOrderInboundResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderReference).Scan(
		&purchaseOrderInbound.Id,
		&purchaseOrderInbound.MessageId,
		&purchaseOrderInbound.OrderId,
		&purchaseOrderInbound.OrderReference,
		&purchaseOrderInbound.BillingId,
		&purchaseOrderInbound.BillingDate,
		&purchaseOrderInbound.CreatedDate,
		&purchaseOrderInbound.OrderType)

	if dbErr != nil {
		ch <- &GetPurchaseOrderInboundResponse{
			Error: dbErr,
		}
		return
	}

	ch <- &purchaseOrderInbound
	return
}

func (d *PurchaseOrderDb) GetProductReturn(ch chan *GetProductReturnResponse, model *GetProductReturnModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetProductReturnResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetProductReturnResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select return_reference from product_returns where content_type = $1 and return_reference = $2`

	var response GetProductReturnResponse
	dbErr := connection.QueryRowContext(ctx, query, model.ContentType, model.ReturnReference).Scan(&response.ReturnReference)

	if dbErr != nil {
		if dbErr == sql.ErrNoRows {
			ch <- &GetProductReturnResponse{ReturnReference: ""}
			return
		}
		ch <- &GetProductReturnResponse{Error: dbErr}
		return
	}

	ch <- &response
}

func (d *PurchaseOrderDb) AddProductReturn(ch chan *PurchaseOrderDbBaseResponse, model *AddProductReturnModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `insert into product_returns (content_type, return_id, return_reference, warehouse_id, reason_type, completed_at) values ($1, $2, $3, $4, $5, $6)`

	result, err := connection.ExecContext(ctx, query, model.ContentType, model.ReturnId, model.ReturnReference, model.WarehouseId, model.ReasonType, model.CompletedAt)
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &PurchaseOrderDbBaseResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &PurchaseOrderDbBaseResponse{Error: errors.New("could not add record")}
		return
	}

	ch <- &PurchaseOrderDbBaseResponse{}
}
