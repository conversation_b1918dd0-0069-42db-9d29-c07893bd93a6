package supplier

import "time"

type PurchaseOrderDbBaseResponse struct {
	Error error `json:"-"`
}

type GetPurchaseOrderDbResponse struct {
	Id             int64     `json:"id"`
	OrderId        string    `json:"order_id"`
	OrderReference string    `json:"order_reference"`
	OrderType      string    `json:"order_type"`
	EventType      string    `json:"event_type"`
	CreatedDate    time.Time `json:"created_date"`
	Error          error     `json:"-"`
}

type GetReturnReasonResponse struct {
	Id     int32  `json:"id"`
	Code   string `json:"code"`
	Reason string `json:"reason"`
	Error  error  `json:"-"`
}

type GetPurchaseOrderRefundResponse struct {
	Id             int64     `json:"id"`
	OrderId        string    `json:"order_id" validate:"required"`
	OrderReference string    `json:"order_reference"`
	CreatedDate    time.Time `json:"created_date"`
	Error          error     `json:"-"`
}

type GetPurchaseOrderInboundResponse struct {
	Id             int64     `json:"id"`
	MessageId      string    `json:"message_id"`
	OrderId        string    `json:"order_id" validate:"required"`
	OrderReference string    `json:"order_reference"`
	BillingId      string    `json:"billing_id"`
	BillingDate    time.Time `json:"billing_date"`
	CreatedDate    time.Time `json:"created_date"`
	OrderType      string    `json:"order_type"`
	Error          error     `json:"-"`
}

type GetProductReturnResponse struct {
	ReturnReference string `json:"return_reference"`
	Error           error  `json:"-"`
}
