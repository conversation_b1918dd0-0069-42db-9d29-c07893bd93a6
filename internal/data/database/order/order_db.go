package order

import (
	"context"
	"database/sql"
	"errors"
	"strconv"
	"strings"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type IOrderDb interface {
	GetOrder(ch chan *GetOrderResponse, model *GetOrderModel)
	AddOrder(ch chan *AddOrderResponse, model *AddOrderModel)
	GetOrderIsDelivered(ch chan *GetOrderIsDeliveredResponse, model *GetOrderIsDeliveredModel)
	MarkOrderAsDelivered(ch chan *MarkOrderAsDeliveredResponse, model *MarkOrderAsDeliveredModel)
	GetOrderPayment(ch chan *GetOrderPaymentResponse, model *GetOrderPaymentModel)
	MarkOrderPaymentAsDelivered(ch chan *MarkOrderPaymentAsDeliveredResponse, model *MarkOrderPaymentAsDeliveredModel)
	GetOrderWithPaymentDetail(ch chan *GetOrderWithPaymentDetailResponse, model *GetOrderWithPaymentDetailModel)
	MarkOrderPaymentAsCompleted(ch chan *MarkOrderPaymentAsCompletedResponse, model *MarkOrderPaymentAsCompletedModel)
	AddOrderPayment(ch chan *AddOrderPaymentResponse, model *AddOrderPaymentModel)
	UpdateOrderPayment(ch chan *UpdateOrderPaymentResponse, model *UpdateOrderPaymentModel)
	AddOrderCancellation(ch chan *AddOrderCancellationResponse, model *AddOrderCancellationModel)
	GetOrderCancellation(ch chan *GetOrderCancellationResponse, model *GetOrderCancellationModel)
	GetMahalleValeOrder(ch chan *GetMahalleValeOrderResponse, model *GetMahalleValeOrderModel)
	AddMahalleValeOrder(ch chan *AddMahalleValeOrderResponse, model *AddMahalleValeOrderModel)
	MarkMahalleValeOrderAsSent(ch chan *MarkMahalleValeOrderAsSentResponse, model *MarkMahalleValeOrderAsSentModel)
	GetMahalleValeOrderCancellation(ch chan *GetMahalleValeOrderCancellationResponse, model *GetMahalleValeOrderCancellationModel)
	AddMahalleValeOrderCancellation(ch chan *AddMahalleValeOrderCancellationResponse, model *AddMahalleValeOrderCancellationModel)
	MarkMahalleValeOrderCancellationAsSent(ch chan *MarkMahalleValeOrderCancellationAsSentResponse, model *MarkMahalleValeOrderCancellationAsSentModel)
	GetSoftposOrderWithPaymentDetail(ch chan *GetOrderWithPaymentDetailResponse, model *GetSoftposOrderWithPaymentDetailModel)
	GetWarehouseById(ch chan *GetWarehouseByIdResponse, model *GetWarehouseByIdModel)
}

type OrderDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

// NewOrderDb
// Returns a new OrderDb.
func NewOrderDb(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IOrderDb {
	db := OrderDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

// GetOrder
// Gets order
func (d *OrderDb) GetOrder(ch chan *GetOrderResponse, model *GetOrderModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetOrderResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetOrderResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT order_details FROM orders WHERE order_id = $1`

	var order GetOrderResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId).Scan(&order.OrderDetails)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &GetOrderResponse{OrderDetails: "", Error: nil}
		return
	}

	if dbErr != nil {
		ch <- &GetOrderResponse{Error: dbErr}
		return
	}

	ch <- &order
	return
}

// AddOrder
// Adds order.
func (d *OrderDb) AddOrder(ch chan *AddOrderResponse, model *AddOrderModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddOrderResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddOrderResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `INSERT INTO orders as o (order_id,
									   order_details,
									   timestamp,
    								   store_id,
    								   user_id,
    								   user_friendly_id,
    								   user_first_name,
    								   user_last_name)
			  VALUES ($1,
					  $2,
					  $3,
			          $4,
			          $5,
			          $6,
			          $7,
			          $8)`

	paymentQuery := `INSERT INTO orders_payment
    			(order_id,
                 payment_type_id,
                 payment_method_id,
                 status,
                 payment_operation_type,
                 payment_operation_type_date,
                 amount, transaction_id)
		      VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING ID`

	tx, err := connection.BeginTx(ctx, nil)
	if err != nil {
		ch <- &AddOrderResponse{Error: err}
		return
	}
	defer tx.Rollback()

	result, err := tx.ExecContext(ctx, query, model.OrderId, model.OrderDetails, model.Timestamp, model.StoreId, model.UserId, model.UserFriendlyId, model.UserFirstName, model.UserLastName)

	if err != nil {
		ch <- &AddOrderResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddOrderResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &AddOrderResponse{Error: errors.New("could not add order: " + model.OrderId)}
		return
	}

	var paymentId int64

	dbErr := tx.QueryRowContext(ctx, paymentQuery,
		model.OrderPayment.OrderId,
		model.OrderPayment.PaymentTypeId,
		model.OrderPayment.PaymentMethodId,
		model.OrderPayment.Status,
		model.OrderPayment.PaymentOperationType,
		model.OrderPayment.PaymentOperationTypeDate,
		model.OrderPayment.Amount,
		model.OrderPayment.TransactionId).Scan(&paymentId)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &AddOrderResponse{Error: errors.New("could not add order payment: " + model.OrderId), PaymentId: 0}
		return
	}

	if dbErr != nil {
		ch <- &AddOrderResponse{Error: dbErr}
		return
	}

	err = tx.Commit()

	if err != nil {
		ch <- &AddOrderResponse{Error: err}
		return
	}

	ch <- &AddOrderResponse{Error: nil, PaymentId: paymentId}
	return
}

// GetOrderIsDelivered
// Gets if order is delivered to Logo.
func (d *OrderDb) GetOrderIsDelivered(ch chan *GetOrderIsDeliveredResponse, model *GetOrderIsDeliveredModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetOrderIsDeliveredResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetOrderIsDeliveredResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT order_id, is_delivered FROM orders WHERE order_id = $1`

	var orderIsDelivered GetOrderIsDeliveredResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId).Scan(&orderIsDelivered.OrderId, &orderIsDelivered.IsDelivered)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &GetOrderIsDeliveredResponse{OrderId: "", Error: nil}
		return
	}

	if dbErr != nil {
		ch <- &GetOrderIsDeliveredResponse{Error: dbErr}
		return
	}

	ch <- &orderIsDelivered
	return
}

// MarkOrderAsDelivered
// Marks order as delivered.
func (d *OrderDb) MarkOrderAsDelivered(ch chan *MarkOrderAsDeliveredResponse, model *MarkOrderAsDeliveredModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkOrderAsDeliveredResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &MarkOrderAsDeliveredResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE orders SET is_delivered = true WHERE order_id = $1`

	result, err := connection.ExecContext(ctx, query, model.OrderId)

	if err != nil {
		ch <- &MarkOrderAsDeliveredResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkOrderAsDeliveredResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &MarkOrderAsDeliveredResponse{Error: errors.New("could not mark order as delivered: " + model.OrderId)}
		return
	}

	ch <- &MarkOrderAsDeliveredResponse{Error: nil}
	return
}

// GetOrderPayment
// Gets payment.
func (d *OrderDb) GetOrderPayment(ch chan *GetOrderPaymentResponse, model *GetOrderPaymentModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetOrderPaymentResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetOrderPaymentResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT id,order_id, 
				   payment_type_id,
				   payment_method_id, 
				   status, 
				   payment_operation_type,
				   payment_operation_type_date,
				   amount,
       			   transaction_id 
			FROM orders_payment
			WHERE order_id = $1
			  AND payment_operation_type = $2 AND transaction_id = $3`

	var payment GetOrderPaymentResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId, model.PaymentOperationType, model.TransactionId).Scan(
		&payment.PaymentId,
		&payment.OrderId,
		&payment.PaymentTypeId,
		&payment.PaymentMethodId,
		&payment.Status,
		&payment.PaymentOperationType,
		&payment.PaymentOperationTypeDate,
		&payment.Amount,
		&payment.TransactionId)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &GetOrderPaymentResponse{OrderId: "", Error: nil}
		return
	}

	if dbErr != nil {
		ch <- &GetOrderPaymentResponse{Error: dbErr}
		return
	}

	ch <- &payment
	return
}

// MarkOrderPaymentAsDelivered
// Marks order payment as delivered.
func (d *OrderDb) MarkOrderPaymentAsDelivered(ch chan *MarkOrderPaymentAsDeliveredResponse, model *MarkOrderPaymentAsDeliveredModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkOrderPaymentAsDeliveredResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &MarkOrderPaymentAsDeliveredResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE orders_payment SET status=2 WHERE order_id = $1 and id = $2`

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.PaymentId)

	if err != nil {
		ch <- &MarkOrderPaymentAsDeliveredResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkOrderPaymentAsDeliveredResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &MarkOrderPaymentAsDeliveredResponse{Error: errors.New("could not mark order payment as delivered: " + model.OrderId + " paymentId:" + strconv.FormatInt(model.PaymentId, 10))}
		return
	}

	ch <- &MarkOrderPaymentAsDeliveredResponse{Error: nil}
	return
}

// GetOrderWithPaymentDetail
// Gets order with payment detail.
func (d *OrderDb) GetOrderWithPaymentDetail(ch chan *GetOrderWithPaymentDetailResponse, model *GetOrderWithPaymentDetailModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetOrderWithPaymentDetailResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetOrderWithPaymentDetailResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT
                     op.id order_payment_id,
					 op.order_id,
					 op.payment_type_id,
					 op.payment_method_id,
					 op.status,
       				 op.payment_operation_type,
       				 op.payment_operation_type_date,
       				 op.Amount,
       				 o.store_id,
                     o.user_id,
                     o.user_friendly_id,
                     o.user_first_name,
                     o.user_last_name
				FROM orders_payment op
				INNER JOIN Orders o ON op.order_id=o.order_id
				WHERE op.order_id=$1 AND op.id=$2`

	var order GetOrderWithPaymentDetailResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId, model.TransactionId).Scan(&order.OrderPaymentId,
		&order.OrderId,
		&order.PaymentTypeId,
		&order.PaymentMethodId,
		&order.Status,
		&order.PaymentOperationType,
		&order.PaymentOperationDate,
		&order.Amount,
		&order.StoreId,
		&order.UserId,
		&order.UserFriendlyId,
		&order.UserFirstname,
		&order.UserLastname)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &GetOrderWithPaymentDetailResponse{Error: nil}
		return
	}

	if dbErr != nil {
		ch <- &GetOrderWithPaymentDetailResponse{Error: dbErr}
		return
	}

	ch <- &order
	return
}

// MarkOrderPaymentAsCompleted
// Mark Order Payment As Completed.
func (d *OrderDb) MarkOrderPaymentAsCompleted(ch chan *MarkOrderPaymentAsCompletedResponse, model *MarkOrderPaymentAsCompletedModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkOrderPaymentAsCompletedResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &MarkOrderPaymentAsCompletedResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE orders_payment SET status = 3 WHERE order_id = $1 AND id = $2`

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.TransactionId)

	if err != nil {
		ch <- &MarkOrderPaymentAsCompletedResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkOrderPaymentAsCompletedResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &MarkOrderPaymentAsCompletedResponse{Error: errors.New("could not mark order payment as completed: " + model.OrderId)}
		return
	}

	ch <- &MarkOrderPaymentAsCompletedResponse{Error: nil}
	return
}

// AddOrderPayment
// Adds order payment.
func (d *OrderDb) AddOrderPayment(ch chan *AddOrderPaymentResponse, model *AddOrderPaymentModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddOrderPaymentResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddOrderPaymentResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `INSERT INTO orders_payment
    			(order_id,
                 payment_type_id,
                 payment_method_id,
                 status,
                 payment_operation_type,
                 payment_operation_type_date,
                 amount,
    			 transaction_id)
		      VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING ID`

	var paymentId int64

	dbErr := connection.QueryRowContext(ctx, query,
		model.OrderId,
		model.PaymentTypeId,
		model.PaymentMethodId,
		model.Status,
		model.PaymentOperationType,
		model.PaymentOperationTypeDate,
		model.Amount,
		model.TransactionId).Scan(&paymentId)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &AddOrderPaymentResponse{Error: errors.New("could not add order payment: " + model.OrderId), PaymentId: 0}
		return
	}

	if dbErr != nil {
		ch <- &AddOrderPaymentResponse{Error: dbErr}
		return
	}

	ch <- &AddOrderPaymentResponse{Error: nil, PaymentId: paymentId}
	return
}

// UpdateOrderPayment
// Updates order payment.
func (d *OrderDb) UpdateOrderPayment(ch chan *UpdateOrderPaymentResponse, model *UpdateOrderPaymentModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateOrderPaymentResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &UpdateOrderPaymentResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE orders_payment set amount = $3 WHERE order_id = $1 AND id = $2`

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.PaymentId, model.Amount)

	if err != nil {
		ch <- &UpdateOrderPaymentResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &UpdateOrderPaymentResponse{Error: err}
		return
	}
	if rows < 1 {
		ch <- &UpdateOrderPaymentResponse{Error: errors.New("could not update order payment: " + model.OrderId)}
		return
	}

	ch <- &UpdateOrderPaymentResponse{Error: nil}
	return
}

func (d *OrderDb) AddOrderCancellation(ch chan *AddOrderCancellationResponse, model *AddOrderCancellationModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddOrderCancellationResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddOrderCancellationResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `INSERT INTO order_cancellations
    			(order_id,
                 transaction_type,
                 created_at)
				SELECT CAST($1 AS VARCHAR), $2, $3
					WHERE
						NOT EXISTS (
							SELECT 1 FROM order_cancellations as oc WHERE oc.order_id = $1 AND oc.transaction_type=$2
						)`

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.OrderTransactionType, time.Now())
	if err != nil {
		ch <- &AddOrderCancellationResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddOrderCancellationResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &AddOrderCancellationResponse{Error: err}
		return
	}

	ch <- &AddOrderCancellationResponse{Error: nil}
	return
}

func (d *OrderDb) GetOrderCancellation(ch chan *GetOrderCancellationResponse, model *GetOrderCancellationModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetOrderCancellationResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetOrderCancellationResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT id,
       				 order_id, 
				     transaction_type,
				     created_at
			FROM order_cancellations
			WHERE order_id = $1
			  AND transaction_type = $2`

	var transaction GetOrderCancellationResponse

	dbErr := connection.QueryRowContext(ctx, query, model.OrderId, model.OrderTransactionType).Scan(
		&transaction.Id,
		&transaction.OrderId,
		&transaction.TransactionType,
		&transaction.CreatedAt)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &GetOrderCancellationResponse{OrderId: "", Error: nil}
		return
	}

	if dbErr != nil {
		ch <- &GetOrderCancellationResponse{Error: dbErr}
		return
	}

	ch <- &transaction
	return
}

func (d *OrderDb) GetMahalleValeOrder(ch chan *GetMahalleValeOrderResponse, model *GetMahalleValeOrderModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetMahalleValeOrderResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetMahalleValeOrderResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT order_id, vendor_type, vendor_name, payment_method, amount, is_send_to_logistic FROM mahalle_vale_orders WHERE order_id = $1`

	var order GetMahalleValeOrderResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId).Scan(&order.OrderId, &order.VendorType, &order.StoreName,
		&order.PaymentMethod, &order.Amount, &order.IsSendToLogistic)

	if dbErr != nil {
		if dbErr == sql.ErrNoRows {
			ch <- &GetMahalleValeOrderResponse{}
			return
		}
		ch <- &GetMahalleValeOrderResponse{Error: dbErr}
		return
	}

	ch <- &order
}

func (d *OrderDb) AddMahalleValeOrder(ch chan *AddMahalleValeOrderResponse, model *AddMahalleValeOrderModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddMahalleValeOrderResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddMahalleValeOrderResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `insert into mahalle_vale_orders (order_type, order_id, timestamp, vendor_id, vendor_type, vendor_name, 
				user_id, user_first_name, user_last_name, payment_method, amount)
					select $1, CAST($2 AS VARCHAR), $3, $4, $5, $6, $7, $8, $9, $10, $11
                                where not exists (
                                    select  1
                                    from    mahalle_vale_orders as o
                                    where   o.order_id = $2
                                );`

	result, err := connection.ExecContext(ctx, query, model.OrderType, model.OrderId, model.Timestamp,
		model.VendorId, model.VendorType, model.VendorName, model.UserId, model.UserFirstName,
		model.UserLastName, model.PaymentMethodName, model.Amount)
	if err != nil {
		ch <- &AddMahalleValeOrderResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddMahalleValeOrderResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &AddMahalleValeOrderResponse{Error: err}
		return
	}

	ch <- &AddMahalleValeOrderResponse{}
}

func (d *OrderDb) MarkMahalleValeOrderAsSent(ch chan *MarkMahalleValeOrderAsSentResponse, model *MarkMahalleValeOrderAsSentModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkMahalleValeOrderAsSentResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &MarkMahalleValeOrderAsSentResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE mahalle_vale_orders SET is_send_to_logistic = true, sent_date = current_timestamp WHERE order_id = $1`

	result, err := connection.ExecContext(ctx, query, model.OrderId)

	if err != nil {
		ch <- &MarkMahalleValeOrderAsSentResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkMahalleValeOrderAsSentResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &MarkMahalleValeOrderAsSentResponse{Error: errors.New("could not mark mahalle-vale order as sent: " + model.OrderId)}
		return
	}

	ch <- &MarkMahalleValeOrderAsSentResponse{}
}

func (d *OrderDb) GetMahalleValeOrderCancellation(ch chan *GetMahalleValeOrderCancellationResponse, model *GetMahalleValeOrderCancellationModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetMahalleValeOrderCancellationResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetMahalleValeOrderCancellationResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT order_id, is_send_to_logistic FROM mahalle_vale_order_cancellations WHERE order_id = $1`

	var order GetMahalleValeOrderCancellationResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId).Scan(&order.OrderId, &order.IsSendToLogistic)

	if dbErr != nil {
		if dbErr == sql.ErrNoRows {
			ch <- &GetMahalleValeOrderCancellationResponse{}
			return
		}
		ch <- &GetMahalleValeOrderCancellationResponse{Error: dbErr}
		return
	}

	ch <- &order
}

func (d *OrderDb) AddMahalleValeOrderCancellation(ch chan *AddMahalleValeOrderCancellationResponse, model *AddMahalleValeOrderCancellationModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddMahalleValeOrderCancellationResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddMahalleValeOrderCancellationResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `insert into mahalle_vale_order_cancellations (order_id, timestamp)
					select CAST($1 AS VARCHAR), $2
                                where not exists (
                                    select  1
                                    from    mahalle_vale_order_cancellations as oc
                                    where   oc.order_id = $1
                                );`

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.Timestamp)
	if err != nil {
		ch <- &AddMahalleValeOrderCancellationResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddMahalleValeOrderCancellationResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &AddMahalleValeOrderCancellationResponse{Error: err}
		return
	}

	ch <- &AddMahalleValeOrderCancellationResponse{}
}

func (d *OrderDb) MarkMahalleValeOrderCancellationAsSent(ch chan *MarkMahalleValeOrderCancellationAsSentResponse, model *MarkMahalleValeOrderCancellationAsSentModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkMahalleValeOrderCancellationAsSentResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &MarkMahalleValeOrderCancellationAsSentResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE mahalle_vale_order_cancellations SET is_send_to_logistic = true, sent_date = current_timestamp WHERE order_id = $1`

	result, err := connection.ExecContext(ctx, query, model.OrderId)

	if err != nil {
		ch <- &MarkMahalleValeOrderCancellationAsSentResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkMahalleValeOrderCancellationAsSentResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &MarkMahalleValeOrderCancellationAsSentResponse{Error: errors.New("could not mark mahalle-vale order cancellation as sent: " + model.OrderId)}
		return
	}

	ch <- &MarkMahalleValeOrderCancellationAsSentResponse{}
}

func (d *OrderDb) GetSoftposOrderWithPaymentDetail(ch chan *GetOrderWithPaymentDetailResponse, model *GetSoftposOrderWithPaymentDetailModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetOrderWithPaymentDetailResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetOrderWithPaymentDetailResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT
                     op.id order_payment_id,
					 op.order_id,
					 op.payment_type_id,
					 op.payment_method_id,
					 op.status,
       				 op.payment_operation_type,
       				 op.payment_operation_type_date,
       				 op.Amount,
       				 o.store_id,
                     o.user_id,
                     o.user_friendly_id,
                     o.user_first_name,
                     o.user_last_name
				FROM orders_payment op
				INNER JOIN Orders o ON op.order_id=o.order_id
				WHERE op.order_id=$1 AND op.payment_operation_type=$2`

	var order GetOrderWithPaymentDetailResponse
	dbErr := connection.QueryRowContext(ctx, query, model.OrderId, model.PaymentOperationType).Scan(&order.OrderPaymentId,
		&order.OrderId,
		&order.PaymentTypeId,
		&order.PaymentMethodId,
		&order.Status,
		&order.PaymentOperationType,
		&order.PaymentOperationDate,
		&order.Amount,
		&order.StoreId,
		&order.UserId,
		&order.UserFriendlyId,
		&order.UserFirstname,
		&order.UserLastname)

	if dbErr != nil && dbErr == sql.ErrNoRows {
		ch <- &GetOrderWithPaymentDetailResponse{Error: nil}
		return
	}

	if dbErr != nil {
		ch <- &GetOrderWithPaymentDetailResponse{Error: dbErr}
		return
	}

	ch <- &order
	return
}

// GetWarehouseById
// Gets warehouse info by id.
func (d *OrderDb) GetWarehouseById(ch chan *GetWarehouseByIdResponse, model *GetWarehouseByIdModel) {

	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetWarehouseByIdResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetWarehouseByIdResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `  SELECT w.id,
         			   w.salesforce_grid_id,
					   w.warehouse_id,
					   w.warehouse_name,
         			   wsi.warehouse_type,
					   w.address,
					   w.city
				FROM warehouses w
						 INNER JOIN warehouses_salesforce_info wsi
									ON w.salesforce_grid_id = wsi.salesforce_grid_id
				WHERE w.warehouse_id = $1`

	row := connection.QueryRowContext(ctx, query, strings.ToLower(model.WarehouseId))
	if row.Err() != nil {
		ch <- &GetWarehouseByIdResponse{
			Error: row.Err(),
		}
		return
	}

	var warehouse Warehouse
	scanErr := row.Scan(&warehouse.Id, &warehouse.SalesforceGridId, &warehouse.WarehouseId, &warehouse.WarehouseName, &warehouse.WarehouseType, &warehouse.Address, &warehouse.City)

	if scanErr != nil && scanErr == sql.ErrNoRows {
		ch <- &GetWarehouseByIdResponse{Error: errors.New("could not find warehouse data: " + model.WarehouseId)}
		return
	}

	response := GetWarehouseByIdResponse{Warehouse: warehouse}
	if scanErr != nil {
		ch <- &GetWarehouseByIdResponse{
			Error: scanErr,
		}
		return
	}
	ch <- &response
}
