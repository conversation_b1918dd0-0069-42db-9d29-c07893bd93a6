package order

import "time"

type GetOrderModel struct {
	OrderId string `validate:"required"`
}

type AddOrderModel struct {
	OrderId        string               `validate:"required"`
	OrderDetails   string               `validate:"required"`
	Timestamp      time.Time            `validate:"required"`
	StoreId        string               `validate:"required"`
	UserId         string               `validate:"required"`
	UserFriendlyId string               `validate:"required"`
	UserFirstName  string               `validate:"required"`
	UserLastName   string               `validate:"required"`
	OrderPayment   AddOrderPaymentModel `validate:"required"`
}

type GetOrderIsDeliveredModel struct {
	OrderId string `validate:"required"`
}

type MarkOrderAsDeliveredModel struct {
	OrderId string `validate:"required"`
}

type AddOrderPaymentModel struct {
	OrderId                  string    `validate:"required"`
	PaymentTypeId            int       `validate:"required"`
	PaymentMethodId          int       `validate:"required"`
	Status                   int       `validate:"required"`
	PaymentOperationType     int       `validate:"required"`
	PaymentOperationTypeDate time.Time `validate:"required"`
	Amount                   float64   `validate:"gte=0"`
	TransactionId            string
}

type GetOrderPaymentModel struct {
	OrderId              string `validate:"required"`
	PaymentOperationType int    `validate:"required"`
	TransactionId        string
}

type MarkOrderPaymentAsDeliveredModel struct {
	OrderId   string `validate:"required"`
	PaymentId int64  `validate:"required"`
}

type MarkOrderPaymentAsCompletedModel struct {
	TransactionId int64  `validate:"required"`
	OrderId       string `validate:"required"`
}

type GetOrderWithPaymentDetailModel struct {
	TransactionId int64  `validate:"required"`
	OrderId       string `validate:"required"`
}

type GetOrderByIdModel struct {
	OrderId string `validate:"required"`
}

type UpdateOrderPaymentModel struct {
	OrderId   string  `validate:"required"`
	PaymentId int64   `validate:"required"`
	Amount    float64 `validate:"gte=0"`
}

type AddOrderCancellationModel struct {
	OrderId              string `validate:"required"`
	OrderTransactionType int    `validate:"required"`
}

type GetOrderCancellationModel struct {
	OrderId              string `validate:"required"`
	OrderTransactionType int    `validate:"required"`
}

type GetMahalleValeOrderModel struct {
	OrderId string `validate:"required"`
}

type AddMahalleValeOrderModel struct {
	OrderType         string    `validate:"required"`
	OrderId           string    `validate:"required"`
	Timestamp         time.Time `validate:"required"`
	VendorId          string    `validate:"required"`
	VendorType        string    `validate:"required"`
	VendorName        string    `validate:"required"`
	UserId            string    `validate:"required"`
	UserFirstName     string    `validate:"required"`
	UserLastName      string    `validate:"required"`
	PaymentMethodName string    `validate:"required"`
	Amount            float64   `validate:"gte=0"`
}

type MarkMahalleValeOrderAsSentModel struct {
	OrderId string `validate:"required"`
}

type GetMahalleValeOrderCancellationModel struct {
	OrderId string `validate:"required"`
}

type AddMahalleValeOrderCancellationModel struct {
	OrderId   string    `validate:"required"`
	Timestamp time.Time `validate:"required"`
}

type MarkMahalleValeOrderCancellationAsSentModel struct {
	OrderId string `validate:"required"`
}

type GetSoftposOrderWithPaymentDetailModel struct {
	OrderId              string `validate:"required"`
	PaymentOperationType int    `validate:"required"`
}

type GetWarehouseByIdModel struct {
	WarehouseId string `validate:"required"`
}
