package order

import (
	"time"
)

type GetOrderResponse struct {
	OrderDetails string
	Error        error `json:"-"`
}

type AddOrderResponse struct {
	Error     error `json:"-"`
	PaymentId int64
}

type GetOrderIsDeliveredResponse struct {
	OrderId     string
	IsDelivered bool
	Error       error `json:"-"`
}

type MarkOrderAsDeliveredResponse struct {
	Error error `json:"-"`
}

type GetOrderPaymentResponse struct {
	PaymentId                int64
	OrderId                  string
	PaymentTypeId            int
	PaymentMethodId          int
	Status                   int
	PaymentOperationType     int
	PaymentOperationTypeDate time.Time
	Amount                   float64
	TransactionId            string
	Error                    error `json:"-"`
}

type MarkOrderPaymentAsDeliveredResponse struct {
	Error error `json:"-"`
}

type MarkOrderPaymentAsCompletedResponse struct {
	Error error `json:"-"`
}

type GetOrderWithPaymentDetailResponse struct {
	OrderPaymentId       int64
	OrderId              string
	PaymentTypeId        int32
	PaymentMethodId      int32
	Status               int32
	PaymentOperationType int32
	Amount               float64
	PaymentOperationDate time.Time
	StoreId              string
	UserId               string
	UserFriendlyId       string
	UserFirstname        string
	UserLastname         string
	Error                error `json:"-"`
}

type AddOrderPaymentResponse struct {
	Error     error `json:"-"`
	PaymentId int64
}

type UpdateOrderPaymentResponse struct {
	Error error `json:"-"`
}

type AddOrderCancellationResponse struct {
	Error error `json:"-"`
}

type GetOrderCancellationResponse struct {
	Id              int64
	OrderId         string
	TransactionType int
	CreatedAt       time.Time
	Error           error `json:"-"`
}

type GetMahalleValeOrderResponse struct {
	OrderId          string
	VendorType       string
	Amount           float64
	PaymentMethod    string
	StoreName        string
	IsSendToLogistic bool
	Error            error `json:"-"`
}

type AddMahalleValeOrderResponse struct {
	Error error `json:"-"`
}

type MarkMahalleValeOrderAsSentResponse struct {
	Error error `json:"-"`
}

type GetMahalleValeOrderCancellationResponse struct {
	OrderId          string
	IsSendToLogistic bool
	Error            error `json:"-"`
}

type AddMahalleValeOrderCancellationResponse struct {
	Error error `json:"-"`
}

type MarkMahalleValeOrderCancellationAsSentResponse struct {
	Error error `json:"-"`
}

type GetWarehouseByIdResponse struct {
	Warehouse Warehouse
	Error     error `json:"-"`
}

type Warehouse struct {
	Id               int
	SalesforceGridId string
	WarehouseId      string
	WarehouseName    string
	WarehouseType    int
	Address          string
	City             string
}
