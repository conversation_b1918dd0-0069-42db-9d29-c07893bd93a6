// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/order/pandora_billing_db.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIPandoraBillingDb is a mock of IPandoraBillingDb interface.
type MockIPandoraBillingDb struct {
	ctrl     *gomock.Controller
	recorder *MockIPandoraBillingDbMockRecorder
}

// MockIPandoraBillingDbMockRecorder is the mock recorder for MockIPandoraBillingDb.
type MockIPandoraBillingDbMockRecorder struct {
	mock *MockIPandoraBillingDb
}

// NewMockIPandoraBillingDb creates a new mock instance.
func NewMockIPandoraBillingDb(ctrl *gomock.Controller) *MockIPandoraBillingDb {
	mock := &MockIPandoraBillingDb{ctrl: ctrl}
	mock.recorder = &MockIPandoraBillingDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPandoraBillingDb) EXPECT() *MockIPandoraBillingDbMockRecorder {
	return m.recorder
}

// AddBillingMessage mocks base method.
func (m *MockIPandoraBillingDb) AddBillingMessage(ch chan *AddPandoraBillingMessageResponse, model *AddPandoraBillingMessageModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddBillingMessage", ch, model)
}

// AddBillingMessage indicates an expected call of AddBillingMessage.
func (mr *MockIPandoraBillingDbMockRecorder) AddBillingMessage(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBillingMessage", reflect.TypeOf((*MockIPandoraBillingDb)(nil).AddBillingMessage), ch, model)
}

// AddSoftposBillingMessage mocks base method.
func (m *MockIPandoraBillingDb) AddSoftposBillingMessage(ch chan *AddPandoraBillingMessageResponse, model *AddPandoraBillingSoftposMessageModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddSoftposBillingMessage", ch, model)
}

// AddSoftposBillingMessage indicates an expected call of AddSoftposBillingMessage.
func (mr *MockIPandoraBillingDbMockRecorder) AddSoftposBillingMessage(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSoftposBillingMessage", reflect.TypeOf((*MockIPandoraBillingDb)(nil).AddSoftposBillingMessage), ch, model)
}

// GetBillingMessage mocks base method.
func (m *MockIPandoraBillingDb) GetBillingMessage(ch chan *GetPandoraBillingMessageResponse, model *GetPandoraBillingMessage) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetBillingMessage", ch, model)
}

// GetBillingMessage indicates an expected call of GetBillingMessage.
func (mr *MockIPandoraBillingDbMockRecorder) GetBillingMessage(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillingMessage", reflect.TypeOf((*MockIPandoraBillingDb)(nil).GetBillingMessage), ch, model)
}

// GetBillingMessageExists mocks base method.
func (m *MockIPandoraBillingDb) GetBillingMessageExists(ch chan *GetPandoraBillingMessageExistsResponse, model *GetPandoraBillingMessageExists) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetBillingMessageExists", ch, model)
}

// GetBillingMessageExists indicates an expected call of GetBillingMessageExists.
func (mr *MockIPandoraBillingDbMockRecorder) GetBillingMessageExists(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillingMessageExists", reflect.TypeOf((*MockIPandoraBillingDb)(nil).GetBillingMessageExists), ch, model)
}

// GetSoftposBillingMessageExists mocks base method.
func (m *MockIPandoraBillingDb) GetSoftposBillingMessageExists(ch chan *GetPandoraBillingMessageExistsResponse, model *GetPandoraBillingSoftposMessageExists) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetSoftposBillingMessageExists", ch, model)
}

// GetSoftposBillingMessageExists indicates an expected call of GetSoftposBillingMessageExists.
func (mr *MockIPandoraBillingDbMockRecorder) GetSoftposBillingMessageExists(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSoftposBillingMessageExists", reflect.TypeOf((*MockIPandoraBillingDb)(nil).GetSoftposBillingMessageExists), ch, model)
}
