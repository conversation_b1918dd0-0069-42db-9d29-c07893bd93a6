package order

import (
	"context"
	"database/sql"
	"fmt"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"time"
)

type IPandoraBillingDb interface {
	AddBillingMessage(ch chan *AddPandoraBillingMessageResponse, model *AddPandoraBillingMessageModel)
	GetBillingMessageExists(ch chan *GetPandoraBillingMessageExistsResponse, model *GetPandoraBillingMessageExists)
	GetBillingMessage(ch chan *GetPandoraBillingMessageResponse, model *GetPandoraBillingMessage)
	AddSoftposBillingMessage(ch chan *AddPandoraBillingMessageResponse, model *AddPandoraBillingSoftposMessageModel)
	GetSoftposBillingMessageExists(ch chan *GetPandoraBillingMessageExistsResponse, model *GetPandoraBillingSoftposMessageExists)
}

type PandoraBillingDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

// NewPandoraBillingDb
// Returns a new PandoraBillingDb.
func NewPandoraBillingDb(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IPandoraBillingDb {
	db := PandoraBillingDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

func (d *PandoraBillingDb) AddBillingMessage(ch chan *AddPandoraBillingMessageResponse, model *AddPandoraBillingMessageModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := "INSERT INTO pandora_billing_messages as pbm (order_id, order_details, status, version, logo_message_id, logo_order_details, timestamp) VALUES ($1,$2,$3,$4,$5,$6,$7)"

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.OrderDetails, model.Status, model.Version, model.LogoMessageId, model.LogoOrderDetails, model.Timestamp)
	if err != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: err}
		return
	}

	if rows != 1 {
		ch <- &AddPandoraBillingMessageResponse{Error: fmt.Errorf("couldn't add pd billing message : %+v", model)}
		return
	}

	ch <- &AddPandoraBillingMessageResponse{Error: nil}
}

func (d *PandoraBillingDb) GetBillingMessageExists(ch chan *GetPandoraBillingMessageExistsResponse, model *GetPandoraBillingMessageExists) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPandoraBillingMessageExistsResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetPandoraBillingMessageExistsResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := "SELECT order_id FROM pandora_billing_messages WHERE order_id = $1 AND status=$2"

	var orderId string

	dbErr := connection.QueryRowContext(ctx, query, model.OrderId, model.Status).Scan(&orderId)

	if dbErr == sql.ErrNoRows || orderId == "" {
		ch <- &GetPandoraBillingMessageExistsResponse{
			Error:  nil,
			Exists: false,
		}
		return
	}

	if dbErr != nil {
		ch <- &GetPandoraBillingMessageExistsResponse{
			Error: dbErr,
		}
		return
	}

	ch <- &GetPandoraBillingMessageExistsResponse{
		Error:  nil,
		Exists: true,
	}
}

func (d *PandoraBillingDb) GetBillingMessage(ch chan *GetPandoraBillingMessageResponse, model *GetPandoraBillingMessage) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPandoraBillingMessageResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetPandoraBillingMessageResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := "SELECT order_id,order_details,logo_order_details FROM pandora_billing_messages WHERE order_id = $1 AND status=$2"

	var response GetPandoraBillingMessageResponse

	dbErr := connection.QueryRowContext(ctx, query, model.OrderId, model.Status).Scan(&response.OrderId, &response.OrderDetails, &response.LogoOrderDetails)

	if dbErr == sql.ErrNoRows {
		ch <- &GetPandoraBillingMessageResponse{
			Error: fmt.Errorf("Couldn't find billing message for order : %v", model.OrderId),
		}
		return
	}

	if dbErr != nil {
		ch <- &GetPandoraBillingMessageResponse{
			Error: dbErr,
		}
		return
	}

	ch <- &response
}

func (d *PandoraBillingDb) AddSoftposBillingMessage(ch chan *AddPandoraBillingMessageResponse, model *AddPandoraBillingSoftposMessageModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := "INSERT INTO pandora_billing_softpos_messages as pbm (order_id, order_details, logo_message_id, logo_order_details, timestamp) VALUES ($1,$2,$3,$4,$5)"

	result, err := connection.ExecContext(ctx, query, model.OrderId, model.OrderDetails, model.LogoMessageId, model.LogoOrderDetails, model.Timestamp)
	if err != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddPandoraBillingMessageResponse{Error: err}
		return
	}

	if rows != 1 {
		ch <- &AddPandoraBillingMessageResponse{Error: fmt.Errorf("couldn't add pd billing softpos message : %+v", model)}
		return
	}

	ch <- &AddPandoraBillingMessageResponse{Error: nil}
}

func (d *PandoraBillingDb) GetSoftposBillingMessageExists(ch chan *GetPandoraBillingMessageExistsResponse, model *GetPandoraBillingSoftposMessageExists) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPandoraBillingMessageExistsResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetPandoraBillingMessageExistsResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := "SELECT order_id FROM pandora_billing_softpos_messages WHERE order_id = $1"

	var orderId string

	dbErr := connection.QueryRowContext(ctx, query, model.OrderId).Scan(&orderId)

	if dbErr == sql.ErrNoRows || orderId == "" {
		ch <- &GetPandoraBillingMessageExistsResponse{
			Error:  nil,
			Exists: false,
		}
		return
	}

	if dbErr != nil {
		ch <- &GetPandoraBillingMessageExistsResponse{
			Error: dbErr,
		}
		return
	}

	ch <- &GetPandoraBillingMessageExistsResponse{
		Error:  nil,
		Exists: true,
	}
}
