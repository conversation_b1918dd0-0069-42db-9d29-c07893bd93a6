// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/order/order_db.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIOrderDb is a mock of IOrderDb interface.
type MockIOrderDb struct {
	ctrl     *gomock.Controller
	recorder *MockIOrderDbMockRecorder
}

// MockIOrderDbMockRecorder is the mock recorder for MockIOrderDb.
type MockIOrderDbMockRecorder struct {
	mock *MockIOrderDb
}

// NewMockIOrderDb creates a new mock instance.
func NewMockIOrderDb(ctrl *gomock.Controller) *MockIOrderDb {
	mock := &MockIOrderDb{ctrl: ctrl}
	mock.recorder = &MockIOrderDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrderDb) EXPECT() *MockIOrderDbMockRecorder {
	return m.recorder
}

// AddMahalleValeOrder mocks base method.
func (m *MockIOrderDb) AddMahalleValeOrder(ch chan *AddMahalleValeOrderResponse, model *AddMahalleValeOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddMahalleValeOrder", ch, model)
}

// AddMahalleValeOrder indicates an expected call of AddMahalleValeOrder.
func (mr *MockIOrderDbMockRecorder) AddMahalleValeOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMahalleValeOrder", reflect.TypeOf((*MockIOrderDb)(nil).AddMahalleValeOrder), ch, model)
}

// AddMahalleValeOrderCancellation mocks base method.
func (m *MockIOrderDb) AddMahalleValeOrderCancellation(ch chan *AddMahalleValeOrderCancellationResponse, model *AddMahalleValeOrderCancellationModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddMahalleValeOrderCancellation", ch, model)
}

// AddMahalleValeOrderCancellation indicates an expected call of AddMahalleValeOrderCancellation.
func (mr *MockIOrderDbMockRecorder) AddMahalleValeOrderCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMahalleValeOrderCancellation", reflect.TypeOf((*MockIOrderDb)(nil).AddMahalleValeOrderCancellation), ch, model)
}

// AddOrder mocks base method.
func (m *MockIOrderDb) AddOrder(ch chan *AddOrderResponse, model *AddOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddOrder", ch, model)
}

// AddOrder indicates an expected call of AddOrder.
func (mr *MockIOrderDbMockRecorder) AddOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOrder", reflect.TypeOf((*MockIOrderDb)(nil).AddOrder), ch, model)
}

// AddOrderCancellation mocks base method.
func (m *MockIOrderDb) AddOrderCancellation(ch chan *AddOrderCancellationResponse, model *AddOrderCancellationModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddOrderCancellation", ch, model)
}

// AddOrderCancellation indicates an expected call of AddOrderCancellation.
func (mr *MockIOrderDbMockRecorder) AddOrderCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOrderCancellation", reflect.TypeOf((*MockIOrderDb)(nil).AddOrderCancellation), ch, model)
}

// AddOrderPayment mocks base method.
func (m *MockIOrderDb) AddOrderPayment(ch chan *AddOrderPaymentResponse, model *AddOrderPaymentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddOrderPayment", ch, model)
}

// AddOrderPayment indicates an expected call of AddOrderPayment.
func (mr *MockIOrderDbMockRecorder) AddOrderPayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOrderPayment", reflect.TypeOf((*MockIOrderDb)(nil).AddOrderPayment), ch, model)
}

// GetMahalleValeOrder mocks base method.
func (m *MockIOrderDb) GetMahalleValeOrder(ch chan *GetMahalleValeOrderResponse, model *GetMahalleValeOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetMahalleValeOrder", ch, model)
}

// GetMahalleValeOrder indicates an expected call of GetMahalleValeOrder.
func (mr *MockIOrderDbMockRecorder) GetMahalleValeOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMahalleValeOrder", reflect.TypeOf((*MockIOrderDb)(nil).GetMahalleValeOrder), ch, model)
}

// GetMahalleValeOrderCancellation mocks base method.
func (m *MockIOrderDb) GetMahalleValeOrderCancellation(ch chan *GetMahalleValeOrderCancellationResponse, model *GetMahalleValeOrderCancellationModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetMahalleValeOrderCancellation", ch, model)
}

// GetMahalleValeOrderCancellation indicates an expected call of GetMahalleValeOrderCancellation.
func (mr *MockIOrderDbMockRecorder) GetMahalleValeOrderCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMahalleValeOrderCancellation", reflect.TypeOf((*MockIOrderDb)(nil).GetMahalleValeOrderCancellation), ch, model)
}

// GetOrder mocks base method.
func (m *MockIOrderDb) GetOrder(ch chan *GetOrderResponse, model *GetOrderModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetOrder", ch, model)
}

// GetOrder indicates an expected call of GetOrder.
func (mr *MockIOrderDbMockRecorder) GetOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrder", reflect.TypeOf((*MockIOrderDb)(nil).GetOrder), ch, model)
}

// GetOrderCancellation mocks base method.
func (m *MockIOrderDb) GetOrderCancellation(ch chan *GetOrderCancellationResponse, model *GetOrderCancellationModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetOrderCancellation", ch, model)
}

// GetOrderCancellation indicates an expected call of GetOrderCancellation.
func (mr *MockIOrderDbMockRecorder) GetOrderCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderCancellation", reflect.TypeOf((*MockIOrderDb)(nil).GetOrderCancellation), ch, model)
}

// GetOrderIsDelivered mocks base method.
func (m *MockIOrderDb) GetOrderIsDelivered(ch chan *GetOrderIsDeliveredResponse, model *GetOrderIsDeliveredModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetOrderIsDelivered", ch, model)
}

// GetOrderIsDelivered indicates an expected call of GetOrderIsDelivered.
func (mr *MockIOrderDbMockRecorder) GetOrderIsDelivered(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderIsDelivered", reflect.TypeOf((*MockIOrderDb)(nil).GetOrderIsDelivered), ch, model)
}

// GetOrderPayment mocks base method.
func (m *MockIOrderDb) GetOrderPayment(ch chan *GetOrderPaymentResponse, model *GetOrderPaymentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetOrderPayment", ch, model)
}

// GetOrderPayment indicates an expected call of GetOrderPayment.
func (mr *MockIOrderDbMockRecorder) GetOrderPayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderPayment", reflect.TypeOf((*MockIOrderDb)(nil).GetOrderPayment), ch, model)
}

// GetOrderWithPaymentDetail mocks base method.
func (m *MockIOrderDb) GetOrderWithPaymentDetail(ch chan *GetOrderWithPaymentDetailResponse, model *GetOrderWithPaymentDetailModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetOrderWithPaymentDetail", ch, model)
}

// GetOrderWithPaymentDetail indicates an expected call of GetOrderWithPaymentDetail.
func (mr *MockIOrderDbMockRecorder) GetOrderWithPaymentDetail(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderWithPaymentDetail", reflect.TypeOf((*MockIOrderDb)(nil).GetOrderWithPaymentDetail), ch, model)
}

// GetSoftposOrderWithPaymentDetail mocks base method.
func (m *MockIOrderDb) GetSoftposOrderWithPaymentDetail(ch chan *GetOrderWithPaymentDetailResponse, model *GetSoftposOrderWithPaymentDetailModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetSoftposOrderWithPaymentDetail", ch, model)
}

// GetSoftposOrderWithPaymentDetail indicates an expected call of GetSoftposOrderWithPaymentDetail.
func (mr *MockIOrderDbMockRecorder) GetSoftposOrderWithPaymentDetail(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSoftposOrderWithPaymentDetail", reflect.TypeOf((*MockIOrderDb)(nil).GetSoftposOrderWithPaymentDetail), ch, model)
}

// GetWarehouseById mocks base method.
func (m *MockIOrderDb) GetWarehouseById(ch chan *GetWarehouseByIdResponse, model *GetWarehouseByIdModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetWarehouseById", ch, model)
}

// GetWarehouseById indicates an expected call of GetWarehouseById.
func (mr *MockIOrderDbMockRecorder) GetWarehouseById(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarehouseById", reflect.TypeOf((*MockIOrderDb)(nil).GetWarehouseById), ch, model)
}

// MarkMahalleValeOrderAsSent mocks base method.
func (m *MockIOrderDb) MarkMahalleValeOrderAsSent(ch chan *MarkMahalleValeOrderAsSentResponse, model *MarkMahalleValeOrderAsSentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkMahalleValeOrderAsSent", ch, model)
}

// MarkMahalleValeOrderAsSent indicates an expected call of MarkMahalleValeOrderAsSent.
func (mr *MockIOrderDbMockRecorder) MarkMahalleValeOrderAsSent(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkMahalleValeOrderAsSent", reflect.TypeOf((*MockIOrderDb)(nil).MarkMahalleValeOrderAsSent), ch, model)
}

// MarkMahalleValeOrderCancellationAsSent mocks base method.
func (m *MockIOrderDb) MarkMahalleValeOrderCancellationAsSent(ch chan *MarkMahalleValeOrderCancellationAsSentResponse, model *MarkMahalleValeOrderCancellationAsSentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkMahalleValeOrderCancellationAsSent", ch, model)
}

// MarkMahalleValeOrderCancellationAsSent indicates an expected call of MarkMahalleValeOrderCancellationAsSent.
func (mr *MockIOrderDbMockRecorder) MarkMahalleValeOrderCancellationAsSent(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkMahalleValeOrderCancellationAsSent", reflect.TypeOf((*MockIOrderDb)(nil).MarkMahalleValeOrderCancellationAsSent), ch, model)
}

// MarkOrderAsDelivered mocks base method.
func (m *MockIOrderDb) MarkOrderAsDelivered(ch chan *MarkOrderAsDeliveredResponse, model *MarkOrderAsDeliveredModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkOrderAsDelivered", ch, model)
}

// MarkOrderAsDelivered indicates an expected call of MarkOrderAsDelivered.
func (mr *MockIOrderDbMockRecorder) MarkOrderAsDelivered(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOrderAsDelivered", reflect.TypeOf((*MockIOrderDb)(nil).MarkOrderAsDelivered), ch, model)
}

// MarkOrderPaymentAsCompleted mocks base method.
func (m *MockIOrderDb) MarkOrderPaymentAsCompleted(ch chan *MarkOrderPaymentAsCompletedResponse, model *MarkOrderPaymentAsCompletedModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkOrderPaymentAsCompleted", ch, model)
}

// MarkOrderPaymentAsCompleted indicates an expected call of MarkOrderPaymentAsCompleted.
func (mr *MockIOrderDbMockRecorder) MarkOrderPaymentAsCompleted(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOrderPaymentAsCompleted", reflect.TypeOf((*MockIOrderDb)(nil).MarkOrderPaymentAsCompleted), ch, model)
}

// MarkOrderPaymentAsDelivered mocks base method.
func (m *MockIOrderDb) MarkOrderPaymentAsDelivered(ch chan *MarkOrderPaymentAsDeliveredResponse, model *MarkOrderPaymentAsDeliveredModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkOrderPaymentAsDelivered", ch, model)
}

// MarkOrderPaymentAsDelivered indicates an expected call of MarkOrderPaymentAsDelivered.
func (mr *MockIOrderDbMockRecorder) MarkOrderPaymentAsDelivered(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOrderPaymentAsDelivered", reflect.TypeOf((*MockIOrderDb)(nil).MarkOrderPaymentAsDelivered), ch, model)
}

// UpdateOrderPayment mocks base method.
func (m *MockIOrderDb) UpdateOrderPayment(ch chan *UpdateOrderPaymentResponse, model *UpdateOrderPaymentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateOrderPayment", ch, model)
}

// UpdateOrderPayment indicates an expected call of UpdateOrderPayment.
func (mr *MockIOrderDbMockRecorder) UpdateOrderPayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderPayment", reflect.TypeOf((*MockIOrderDb)(nil).UpdateOrderPayment), ch, model)
}
