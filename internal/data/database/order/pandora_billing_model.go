package order

import "time"

type AddPandoraBillingMessageModel struct {
	OrderId          string `validate:"required"`
	OrderDetails     string `validate:"required"`
	Status           string `validate:"required"`
	Version          int    `validate:"gt=0"`
	LogoMessageId    string
	LogoOrderDetails string
	Timestamp        time.Time
}

type GetPandoraBillingMessageExists struct {
	OrderId string `validate:"required"`
	Status  string `validate:"required"`
}

type GetPandoraBillingMessage struct {
	OrderId string `validate:"required"`
	Status  string `validate:"required"`
}

type AddPandoraBillingSoftposMessageModel struct {
	OrderId          string `validate:"required"`
	OrderDetails     string `validate:"required"`
	LogoMessageId    string
	LogoOrderDetails string
	Timestamp        time.Time
}

type GetPandoraBillingSoftposMessageExists struct {
	OrderId string `validate:"required"`
}
