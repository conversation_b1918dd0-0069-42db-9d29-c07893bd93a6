// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/warehouse/warehouse_stock_db.go

// Package warehouse is a generated GoMock package.
package warehouse

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIWarehouseStockDb is a mock of IWarehouseStockDb interface.
type MockIWarehouseStockDb struct {
	ctrl     *gomock.Controller
	recorder *MockIWarehouseStockDbMockRecorder
}

// MockIWarehouseStockDbMockRecorder is the mock recorder for MockIWarehouseStockDb.
type MockIWarehouseStockDbMockRecorder struct {
	mock *MockIWarehouseStockDb
}

// NewMockIWarehouseStockDb creates a new mock instance.
func NewMockIWarehouseStockDb(ctrl *gomock.Controller) *MockIWarehouseStockDb {
	mock := &MockIWarehouseStockDb{ctrl: ctrl}
	mock.recorder = &MockIWarehouseStockDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWarehouseStockDb) EXPECT() *MockIWarehouseStockDbMockRecorder {
	return m.recorder
}

// AddStockCount mocks base method.
func (m *MockIWarehouseStockDb) AddStockCount(ch chan *AddStockCountResponse, model *AddStockCountListModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddStockCount", ch, model)
}

// AddStockCount indicates an expected call of AddStockCount.
func (mr *MockIWarehouseStockDbMockRecorder) AddStockCount(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStockCount", reflect.TypeOf((*MockIWarehouseStockDb)(nil).AddStockCount), ch, model)
}

// GetReasonCode mocks base method.
func (m *MockIWarehouseStockDb) GetReasonCode(ch chan *GetReasonCodesResponse, model *GetReasonCodesModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetReasonCode", ch, model)
}

// GetReasonCode indicates an expected call of GetReasonCode.
func (mr *MockIWarehouseStockDbMockRecorder) GetReasonCode(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReasonCode", reflect.TypeOf((*MockIWarehouseStockDb)(nil).GetReasonCode), ch, model)
}

// GetWarehouseNameById mocks base method.
func (m *MockIWarehouseStockDb) GetWarehouseNameById(ch chan *GetWarehouseNameByIdResponse, model *GetWarehouseByIdModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetWarehouseNameById", ch, model)
}

// GetWarehouseNameById indicates an expected call of GetWarehouseNameById.
func (mr *MockIWarehouseStockDbMockRecorder) GetWarehouseNameById(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarehouseNameById", reflect.TypeOf((*MockIWarehouseStockDb)(nil).GetWarehouseNameById), ch, model)
}

// GetWarehouseStockCountsByNotSentMail mocks base method.
func (m *MockIWarehouseStockDb) GetWarehouseStockCountsByNotSentMail(ch chan *GetWarehouseStockCountsByNotSentMailResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetWarehouseStockCountsByNotSentMail", ch)
}

// GetWarehouseStockCountsByNotSentMail indicates an expected call of GetWarehouseStockCountsByNotSentMail.
func (mr *MockIWarehouseStockDbMockRecorder) GetWarehouseStockCountsByNotSentMail(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarehouseStockCountsByNotSentMail", reflect.TypeOf((*MockIWarehouseStockDb)(nil).GetWarehouseStockCountsByNotSentMail), ch)
}

// GetWarehouseStockCountsByNotSentToLogo mocks base method.
func (m *MockIWarehouseStockDb) GetWarehouseStockCountsByNotSentToLogo(ch chan *GetWarehouseStockCountsResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetWarehouseStockCountsByNotSentToLogo", ch)
}

// GetWarehouseStockCountsByNotSentToLogo indicates an expected call of GetWarehouseStockCountsByNotSentToLogo.
func (mr *MockIWarehouseStockDbMockRecorder) GetWarehouseStockCountsByNotSentToLogo(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarehouseStockCountsByNotSentToLogo", reflect.TypeOf((*MockIWarehouseStockDb)(nil).GetWarehouseStockCountsByNotSentToLogo), ch)
}

// MarkWarehouseStockCountsAsSent mocks base method.
func (m *MockIWarehouseStockDb) MarkWarehouseStockCountsAsSent(ch chan *MarkWareHouseStockCountsAsSentResponse, model *MarkWareHouseStockCountsAsSentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkWarehouseStockCountsAsSent", ch, model)
}

// MarkWarehouseStockCountsAsSent indicates an expected call of MarkWarehouseStockCountsAsSent.
func (mr *MockIWarehouseStockDbMockRecorder) MarkWarehouseStockCountsAsSent(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkWarehouseStockCountsAsSent", reflect.TypeOf((*MockIWarehouseStockDb)(nil).MarkWarehouseStockCountsAsSent), ch, model)
}

// MarkWarehouseStockCountsAsSentMail mocks base method.
func (m *MockIWarehouseStockDb) MarkWarehouseStockCountsAsSentMail(ch chan *MarkWarehouseStockCountsAsSentMailResponse, model *MarkWarehouseStockCountsAsSentMailModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkWarehouseStockCountsAsSentMail", ch, model)
}

// MarkWarehouseStockCountsAsSentMail indicates an expected call of MarkWarehouseStockCountsAsSentMail.
func (mr *MockIWarehouseStockDbMockRecorder) MarkWarehouseStockCountsAsSentMail(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkWarehouseStockCountsAsSentMail", reflect.TypeOf((*MockIWarehouseStockDb)(nil).MarkWarehouseStockCountsAsSentMail), ch, model)
}
