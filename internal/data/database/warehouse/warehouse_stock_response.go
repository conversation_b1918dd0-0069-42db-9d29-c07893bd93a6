package warehouse

import "time"

type GetReasonCodesResponse struct {
	Error                   error  `json:"-"`
	Id                      int    `json:"id"`
	GlobalReasonCode        int    `json:"global_reason_code"`
	GlobalReasonDescription string `json:"global_reason_description"`
	LogoReasonCode          string `json:"logo_reason_code"`
	LogoReasonDescription   string `json:"logo_reason_description"`
	LogoSlipTypeId          int    `json:"logo_slip_type_id"`
}

type AddStockCountResponse struct {
	Error error `json:"-"`
}

type GetWarehouseStockCountsResponse struct {
	Error             error `json:"-"`
	GetAllStockCounts []GetStockCountModel
}

type GetStockCountModel struct {
	StoreCount          float64
	StoreId             string
	CenterStoreQuantity float64
	MessageId           string
	Sku                 string
}

type MarkWareHouseStockCountsAsSentResponse struct {
	Error error `json:"-"`
}

type GetWarehouseStockCountsByNotSentMailResponse struct {
	Error    error `json:"-"`
	StoreIds []StoreIdsModel
}

type StoreIdsModel struct {
	Id             int
	StoreId        string
	SentDateToLogo time.Time
}

type MarkWarehouseStockCountsAsSentMailResponse struct {
	Error error `json:"-"`
}
