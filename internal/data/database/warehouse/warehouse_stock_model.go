package warehouse

type GetReasonCodesModel struct {
	GlobalReasonCode int `validate:"required"`
}

type AddStockCountListModel struct {
	StockCountTypeId int             `validate:"required"`
	StockCountList   []AddStockCount `validate:"required,dive,required"`
}

type AddStockCount struct {
	StoreCount          float64 `json:"store_count" validate:"gte=-9999"`
	StoreId             string  `json:"store_id" validate:"required"`
	CenterStoreQuantity float64 `json:"center_store_quantity" validate:"gte=-9999"`
	MessageId           string  `json:"message_id" validate:"required"`
	Sku                 string  `json:"sku" validate:"required"`
}

type MarkWareHouseStockCountsAsSentModel struct {
	StoreId    string   `validate:"required"`
	MessageIds []string `validate:"required"`
}

type MarkWarehouseStockCountsAsSentMailModel struct {
	Ids []int `validate:"required"`
}
