// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/warehouse/warehouse_db.go

// Package warehouse is a generated GoMock package.
package warehouse

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIWarehouseDb is a mock of IWarehouseDb interface.
type MockIWarehouseDb struct {
	ctrl     *gomock.Controller
	recorder *MockIWarehouseDbMockRecorder
}

// MockIWarehouseDbMockRecorder is the mock recorder for MockIWarehouseDb.
type MockIWarehouseDbMockRecorder struct {
	mock *MockIWarehouseDb
}

// NewMockIWarehouseDb creates a new mock instance.
func NewMockIWarehouseDb(ctrl *gomock.Controller) *MockIWarehouseDb {
	mock := &MockIWarehouseDb{ctrl: ctrl}
	mock.recorder = &MockIWarehouseDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWarehouseDb) EXPECT() *MockIWarehouseDbMockRecorder {
	return m.recorder
}

// AddOrUpdateWarehouse mocks base method.
func (m *MockIWarehouseDb) AddOrUpdateWarehouse(ch chan *AddOrUpdateWarehouseResponse, model *AddOrUpdateWarehouseModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddOrUpdateWarehouse", ch, model)
}

// AddOrUpdateWarehouse indicates an expected call of AddOrUpdateWarehouse.
func (mr *MockIWarehouseDbMockRecorder) AddOrUpdateWarehouse(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOrUpdateWarehouse", reflect.TypeOf((*MockIWarehouseDb)(nil).AddOrUpdateWarehouse), ch, model)
}

// GetUndeliveredWarehouses mocks base method.
func (m *MockIWarehouseDb) GetUndeliveredWarehouses(ch chan *GetUndeliveredWarehousesResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetUndeliveredWarehouses", ch)
}

// GetUndeliveredWarehouses indicates an expected call of GetUndeliveredWarehouses.
func (mr *MockIWarehouseDbMockRecorder) GetUndeliveredWarehouses(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUndeliveredWarehouses", reflect.TypeOf((*MockIWarehouseDb)(nil).GetUndeliveredWarehouses), ch)
}

// GetWarehouseById mocks base method.
func (m *MockIWarehouseDb) GetWarehouseById(ch chan *GetWarehouseByIdResponse, model *GetWarehouseByIdModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetWarehouseById", ch, model)
}

// GetWarehouseById indicates an expected call of GetWarehouseById.
func (mr *MockIWarehouseDbMockRecorder) GetWarehouseById(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarehouseById", reflect.TypeOf((*MockIWarehouseDb)(nil).GetWarehouseById), ch, model)
}

// MarkWarehousesDelivered mocks base method.
func (m *MockIWarehouseDb) MarkWarehousesDelivered(ch chan *MarkWarehousesAsDeliveredResponse, model *MarkWarehousesAsDeliveredModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkWarehousesDelivered", ch, model)
}

// MarkWarehousesDelivered indicates an expected call of MarkWarehousesDelivered.
func (mr *MockIWarehouseDbMockRecorder) MarkWarehousesDelivered(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkWarehousesDelivered", reflect.TypeOf((*MockIWarehouseDb)(nil).MarkWarehousesDelivered), ch, model)
}
