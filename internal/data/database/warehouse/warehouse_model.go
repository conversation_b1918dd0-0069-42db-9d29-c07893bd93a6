package warehouse

import "time"

type AddOrUpdateWarehouseModel struct {
	SalesforceGridId string
	WarehouseId      string    `validate:"required"`
	WarehouseName    string    `validate:"required"`
	Address          string    `validate:"required"`
	City             string    `validate:"required"`
	PayloadTimestamp time.Time `validate:"required"`
	VendorCode       string    `validate:"required"`
}

type MarkWarehousesAsDeliveredModel struct {
	Ids []int `validate:"required"`
}

type GetWarehouseByIdModel struct {
	WarehouseId string `validate:"required"`
}
