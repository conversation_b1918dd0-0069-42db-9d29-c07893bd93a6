package warehouse

type AddOrUpdateWarehouseResponse struct {
	Error error `json:"-"`
}

type GetUndeliveredWarehousesResponse struct {
	UndeliveredWarehouses []Warehouse
	Error                 error `json:"-"`
}

type Warehouse struct {
	Id               int
	SalesforceGridId string
	WarehouseId      string
	WarehouseName    string
	WarehouseType    int
	Address          string
	City             string
}

type MarkWarehousesAsDeliveredResponse struct {
	Error error `json:"-"`
}

type GetWarehouseByIdResponse struct {
	Warehouse Warehouse
	Error     error `json:"-"`
}

type GetWarehouseNameByIdResponse struct {
	WarehouseName string
	Error         error `json:"-"`
}
