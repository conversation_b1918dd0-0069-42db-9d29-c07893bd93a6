package warehouse

import (
	"context"
	"database/sql"
	"errors"
	"strconv"
	"strings"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	_ "github.com/lib/pq"
)

type IWarehouseDb interface {
	AddOrUpdateWarehouse(ch chan *AddOrUpdateWarehouseResponse, model *AddOrUpdateWarehouseModel)
	GetUndeliveredWarehouses(ch chan *GetUndeliveredWarehousesResponse)
	MarkWarehousesDelivered(ch chan *MarkWarehousesAsDeliveredResponse, model *Mark<PERSON>arehousesAsDeliveredModel)
	GetWarehouseById(ch chan *GetWarehouseByIdResponse, model *GetWarehouseByIdModel)
}

type WarehouseDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

// NewWarehouseDb
// Returns a new WarehouseDb.
func NewWarehouseDb(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IWarehouseDb {
	db := WarehouseDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

// AddOrUpdateWarehouse
// Adds or updates warehouse.
func (d *WarehouseDb) AddOrUpdateWarehouse(ch chan *AddOrUpdateWarehouseResponse, model *AddOrUpdateWarehouseModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddOrUpdateWarehouseResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddOrUpdateWarehouseResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `  INSERT INTO warehouses as wh (salesforce_grid_id,
											  warehouse_id,
											  warehouse_name,
											  address,
											  city,
											  created_date,
											  modified_date,
											  payload_timestamp,
											  is_delivered,
											  vendor_code)
				VALUES ($1,
						$2,
						$3,
						$4,
						$5,
						current_timestamp,
						null,
						$6,
						false,
						$7)
				ON CONFLICT (warehouse_id)
					DO UPDATE SET salesforce_grid_id = $1,
								  warehouse_name     = $3,
								  address            = $4,
								  city               = $5,
								  modified_date      = current_timestamp,
								  payload_timestamp  = $6,
								  is_delivered       = false,
								  vendor_code        = $7
				WHERE wh.payload_timestamp < $6`

	result, err := connection.ExecContext(ctx, query, model.SalesforceGridId, model.WarehouseId, model.WarehouseName, model.Address, model.City, model.PayloadTimestamp, model.VendorCode)

	if err != nil {
		ch <- &AddOrUpdateWarehouseResponse{Error: err}
		return
	}

	_, err = result.RowsAffected()
	if err != nil {
		ch <- &AddOrUpdateWarehouseResponse{Error: err}
		return
	}

	ch <- &AddOrUpdateWarehouseResponse{Error: nil}
}

// GetUndeliveredWarehouses
// Adds or updates warehouse salesforce info.
func (d *WarehouseDb) GetUndeliveredWarehouses(ch chan *GetUndeliveredWarehousesResponse) {

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetUndeliveredWarehousesResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `  SELECT w.id,
         			   w.salesforce_grid_id,
					   w.warehouse_id,
					   w.warehouse_name,
         			   wsi.warehouse_type,
					   w.address,
					   w.city
				FROM warehouses w
						 INNER JOIN warehouses_salesforce_info wsi
									ON w.salesforce_grid_id = wsi.salesforce_grid_id
				WHERE w.is_delivered = false`

	var undeliveredWarehouses []Warehouse
	rows, dbErr := connection.QueryContext(ctx, query)

	if dbErr != nil {
		ch <- &GetUndeliveredWarehousesResponse{Error: dbErr}
		return
	}

	defer rows.Close()

	for rows.Next() {
		var warehouse Warehouse

		err := rows.Scan(&warehouse.Id,
			&warehouse.SalesforceGridId,
			&warehouse.WarehouseId,
			&warehouse.WarehouseName,
			&warehouse.WarehouseType,
			&warehouse.Address,
			&warehouse.City)

		if err != nil {
			break
		}
		undeliveredWarehouses = append(undeliveredWarehouses, warehouse)
	}

	if err := rows.Err(); err != nil {
		ch <- &GetUndeliveredWarehousesResponse{Error: err}
		return
	}

	ch <- &GetUndeliveredWarehousesResponse{
		UndeliveredWarehouses: undeliveredWarehouses,
		Error:                 nil}
}

// MarkWarehousesDelivered
// Adds or updates warehouse salesforce info.
func (d *WarehouseDb) MarkWarehousesDelivered(ch chan *MarkWarehousesAsDeliveredResponse, model *MarkWarehousesAsDeliveredModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkWarehousesAsDeliveredResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &MarkWarehousesAsDeliveredResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE warehouses SET is_delivered = true WHERE id = ANY($1::int[])`

	ids := make([]string, len(model.Ids))
	for i, x := range model.Ids {
		ids[i] = strconv.Itoa(x)
	}

	param := "{" + strings.Join(ids, ",") + "}"

	result, err := connection.ExecContext(ctx, query, param)

	if err != nil {
		ch <- &MarkWarehousesAsDeliveredResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkWarehousesAsDeliveredResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &MarkWarehousesAsDeliveredResponse{Error: err}
		return
	}

	ch <- &MarkWarehousesAsDeliveredResponse{Error: nil}
}

// GetWarehouseById
// Gets warehouse info by id.
func (d *WarehouseDb) GetWarehouseById(ch chan *GetWarehouseByIdResponse, model *GetWarehouseByIdModel) {

	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetWarehouseByIdResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetWarehouseByIdResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `  SELECT w.id,
         			   w.salesforce_grid_id,
					   w.warehouse_id,
					   w.warehouse_name,
         			   wsi.warehouse_type,
					   w.address,
					   w.city
				FROM warehouses w
						 INNER JOIN warehouses_salesforce_info wsi
									ON w.salesforce_grid_id = wsi.salesforce_grid_id
				WHERE w.warehouse_id = $1`

	row := connection.QueryRowContext(ctx, query, strings.ToLower(model.WarehouseId))
	if row.Err() != nil {
		ch <- &GetWarehouseByIdResponse{
			Error: row.Err(),
		}
		return
	}

	var warehouse Warehouse
	scanErr := row.Scan(&warehouse.Id, &warehouse.SalesforceGridId, &warehouse.WarehouseId, &warehouse.WarehouseName, &warehouse.WarehouseType, &warehouse.Address, &warehouse.City)

	if scanErr != nil && scanErr == sql.ErrNoRows {
		ch <- &GetWarehouseByIdResponse{Error: errors.New("could not find warehouse data: " + model.WarehouseId)}
		return
	}

	response := GetWarehouseByIdResponse{Warehouse: warehouse}
	if scanErr != nil {
		ch <- &GetWarehouseByIdResponse{
			Error: scanErr,
		}
		return
	}
	ch <- &response
}
