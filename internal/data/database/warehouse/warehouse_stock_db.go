package warehouse

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"
	"strings"
	"time"

	"github.com/lib/pq"
)

type IWarehouseStockDb interface {
	GetReasonCode(ch chan *GetReasonCodesResponse, model *GetReasonCodesModel)
	AddStockCount(ch chan *AddStockCountResponse, model *AddStockCountListModel)
	GetWarehouseStockCountsByNotSentToLogo(ch chan *GetWarehouseStockCountsResponse)
	MarkWarehouseStockCountsAsSent(ch chan *MarkWareHouseStockCountsAsSentResponse, model *MarkWareHouseStockCountsAsSentModel)
	GetWarehouseStockCountsByNotSentMail(ch chan *GetWarehouseStockCountsByNotSentMailResponse)
	MarkWarehouseStockCountsAsSentMail(ch chan *MarkWarehouseStockCountsAsSentMailResponse, model *MarkWarehouseStockCountsAsSentMailModel)
	GetWarehouseNameById(ch chan *GetWarehouseNameByIdResponse, model *GetWarehouseByIdModel)
}

type WarehouseStockDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

func NewWarehouseStockDb(environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher) IWarehouseStockDb {

	db := WarehouseStockDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

func (s *WarehouseStockDb) GetReasonCode(ch chan *GetReasonCodesResponse, model *GetReasonCodesModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetReasonCodesResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(s.driverName, s.connectionString)
	if err != nil {
		ch <- &GetReasonCodesResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	query := `SELECT id,
					 global_reason_code,
					 global_reason_description,
					 logo_reason_code,
					 logo_reason_description,
					 logo_slip_type_id
				FROM stock_adjustment_reason_code 
				WHERE global_reason_code = $1`

	var reason GetReasonCodesResponse
	dbErr := connection.QueryRowContext(ctx, query, model.GlobalReasonCode).Scan(&reason.Id,
		&reason.GlobalReasonCode,
		&reason.GlobalReasonDescription,
		&reason.LogoReasonCode,
		&reason.LogoReasonDescription,
		&reason.LogoSlipTypeId)

	if dbErr != nil {
		if dbErr == sql.ErrNoRows {
			ch <- &GetReasonCodesResponse{Error: errors.New("Global reason code " + strconv.Itoa(model.GlobalReasonCode) + " is not found in config")}
			return
		}
		ch <- &GetReasonCodesResponse{Error: dbErr}
		return
	}

	ch <- &reason
}

func (s *WarehouseStockDb) AddStockCount(ch chan *AddStockCountResponse, model *AddStockCountListModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddStockCountResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(s.driverName, s.connectionString)
	if err != nil {
		ch <- &AddStockCountResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	query := `insert into warehouses_stock_count
				(
					store_id,
					message_id,
					sku,
					store_count,
					center_store_quantity,
					created_date,
					stock_count_type_id
				)
				select 
					store_id,
					message_id,
					sku,
					store_count,
					center_store_quantity,
					current_timestamp,
					$2
				from json_to_recordset($1::json) as counts
				(
					store_id varchar(50),
					message_id varchar(50),
					sku varchar(50),
					store_count numeric(18,2),
					center_store_quantity numeric(18,2)
				);`

	countsJson, err := json.Marshal(model.StockCountList)
	if err != nil {
		ch <- &AddStockCountResponse{Error: err}
		return
	}

	result, err := connection.ExecContext(ctx, query, string(countsJson), model.StockCountTypeId)
	if err != nil {
		ch <- &AddStockCountResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddStockCountResponse{Error: err}
		return
	}
	if rows < 1 {
		ch <- &AddStockCountResponse{Error: errors.New("could_not_insert_any_rows")}
		return
	}

	ch <- &AddStockCountResponse{Error: nil}
}

func (s *WarehouseStockDb) GetWarehouseStockCountsByNotSentToLogo(ch chan *GetWarehouseStockCountsResponse) {

	connection, err := sql.Open(s.driverName, s.connectionString)
	if err != nil {
		ch <- &GetWarehouseStockCountsResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	query := `select store_id, 
	                 store_count, 
	                 center_store_quantity,
	                 message_id,
	                 sku
	           from warehouses_stock_count
	           where is_send = false and stock_count_type_id = 1
			   and created_date < NOW() - INTERVAL '30 MINUTE';`

	rows, dbErr := connection.QueryContext(ctx, query)

	if dbErr != nil && dbErr != sql.ErrNoRows {
		ch <- &GetWarehouseStockCountsResponse{Error: dbErr}
		return
	}
	if dbErr == sql.ErrNoRows {
		ch <- &GetWarehouseStockCountsResponse{GetAllStockCounts: []GetStockCountModel{}}
		return
	}

	var response GetWarehouseStockCountsResponse
	for rows.Next() {
		var getStockCount GetStockCountModel
		if err := rows.Scan(&getStockCount.StoreId, &getStockCount.StoreCount, &getStockCount.CenterStoreQuantity,
			&getStockCount.MessageId, &getStockCount.Sku); err != nil {
			ch <- &GetWarehouseStockCountsResponse{Error: err}
			return
		}
		response.GetAllStockCounts = append(response.GetAllStockCounts, getStockCount)
	}

	ch <- &response
}

func (s *WarehouseStockDb) MarkWarehouseStockCountsAsSent(ch chan *MarkWareHouseStockCountsAsSentResponse, model *MarkWareHouseStockCountsAsSentModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkWareHouseStockCountsAsSentResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(s.driverName, s.connectionString)
	if err != nil {
		ch <- &MarkWareHouseStockCountsAsSentResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	query := `update warehouses_stock_count
			set is_send = true, sent_date = current_timestamp
			where store_id = $1 and message_id = ANY($2::text[])`

	param := "{" + strings.Join(model.MessageIds, ",") + "}"

	result, dbErr := connection.ExecContext(ctx, query, model.StoreId, param)
	if dbErr != nil {
		ch <- &MarkWareHouseStockCountsAsSentResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkWareHouseStockCountsAsSentResponse{Error: err}
		return
	}
	if rows < 1 {
		ch <- &MarkWareHouseStockCountsAsSentResponse{Error: errors.New("could_not_update_any_rows")}
		return
	}

	ch <- &MarkWareHouseStockCountsAsSentResponse{Error: nil}
}

func (s *WarehouseStockDb) GetWarehouseStockCountsByNotSentMail(ch chan *GetWarehouseStockCountsByNotSentMailResponse) {
	connection, err := sql.Open(s.driverName, s.connectionString)
	if err != nil {
		ch <- &GetWarehouseStockCountsByNotSentMailResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	query := `select id, store_id, sent_date
	           from warehouses_stock_count
	           where is_send = true and is_mail_send = false order by sent_date;`

	rows, dbErr := connection.QueryContext(ctx, query)

	if dbErr != nil && dbErr != sql.ErrNoRows {
		ch <- &GetWarehouseStockCountsByNotSentMailResponse{Error: dbErr}
		return
	}
	if dbErr == sql.ErrNoRows {
		ch <- &GetWarehouseStockCountsByNotSentMailResponse{StoreIds: []StoreIdsModel{}}
		return
	}

	var response GetWarehouseStockCountsByNotSentMailResponse
	for rows.Next() {
		var getStoreId StoreIdsModel
		if err := rows.Scan(&getStoreId.Id, &getStoreId.StoreId, &getStoreId.SentDateToLogo); err != nil {
			ch <- &GetWarehouseStockCountsByNotSentMailResponse{Error: err}
			return
		}
		response.StoreIds = append(response.StoreIds, getStoreId)
	}

	ch <- &response
}

func (s *WarehouseStockDb) MarkWarehouseStockCountsAsSentMail(ch chan *MarkWarehouseStockCountsAsSentMailResponse, model *MarkWarehouseStockCountsAsSentMailModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkWarehouseStockCountsAsSentMailResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(s.driverName, s.connectionString)
	if err != nil {
		ch <- &MarkWarehouseStockCountsAsSentMailResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	query := `update warehouses_stock_count
			set is_mail_send = true
			where id = ANY($1::int[])`

	result, dbErr := connection.ExecContext(ctx, query, pq.Array(model.Ids))
	if dbErr != nil {
		ch <- &MarkWarehouseStockCountsAsSentMailResponse{Error: dbErr}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkWarehouseStockCountsAsSentMailResponse{Error: err}
		return
	}
	if rows < 1 {
		ch <- &MarkWarehouseStockCountsAsSentMailResponse{Error: errors.New("could_not_update_any_rows")}
		return
	}

	ch <- &MarkWarehouseStockCountsAsSentMailResponse{Error: nil}
}

func (s *WarehouseStockDb) GetWarehouseNameById(ch chan *GetWarehouseNameByIdResponse, model *GetWarehouseByIdModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetWarehouseNameByIdResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(s.driverName, s.connectionString)
	if err != nil {
		ch <- &GetWarehouseNameByIdResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	query := `select warehouse_name
	           from warehouses
	           where warehouse_id = $1;`

	var response GetWarehouseNameByIdResponse
	dbErr := connection.QueryRowContext(ctx, query, strings.ToLower(model.WarehouseId)).Scan(&response.WarehouseName)

	if dbErr != nil {
		if dbErr == sql.ErrNoRows {
			ch <- &GetWarehouseNameByIdResponse{WarehouseName: model.WarehouseId} //send id if warehouse name is not found
			return
		}
		ch <- &GetWarehouseNameByIdResponse{Error: dbErr}
		return
	}

	ch <- &response
}
