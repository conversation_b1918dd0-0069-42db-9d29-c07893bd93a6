// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/salesforce/salesforce_db.go

// Package salesforce is a generated GoMock package.
package salesforce

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISalesforceDb is a mock of ISalesforceDb interface.
type MockISalesforceDb struct {
	ctrl     *gomock.Controller
	recorder *MockISalesforceDbMockRecorder
}

// MockISalesforceDbMockRecorder is the mock recorder for MockISalesforceDb.
type MockISalesforceDbMockRecorder struct {
	mock *MockISalesforceDb
}

// NewMockISalesforceDb creates a new mock instance.
func NewMockISalesforceDb(ctrl *gomock.Controller) *MockISalesforceDb {
	mock := &MockISalesforceDb{ctrl: ctrl}
	mock.recorder = &MockISalesforceDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISalesforceDb) EXPECT() *MockISalesforceDbMockRecorder {
	return m.recorder
}

// GetUndeliveredSalesforceAccounts mocks base method.
func (m *MockISalesforceDb) GetUndeliveredSalesforceAccounts(ch chan *GetUndeliveredSalesforceAccountsResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetUndeliveredSalesforceAccounts", ch)
}

// GetUndeliveredSalesforceAccounts indicates an expected call of GetUndeliveredSalesforceAccounts.
func (mr *MockISalesforceDbMockRecorder) GetUndeliveredSalesforceAccounts(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUndeliveredSalesforceAccounts", reflect.TypeOf((*MockISalesforceDb)(nil).GetUndeliveredSalesforceAccounts), ch)
}

// MarkSalesforceAccountsDelivered mocks base method.
func (m *MockISalesforceDb) MarkSalesforceAccountsDelivered(ch chan *MarkSalesforceAccountsAsDeliveredResponse, model *MarkSalesforceAccountsAsDeliveredModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkSalesforceAccountsDelivered", ch, model)
}

// MarkSalesforceAccountsDelivered indicates an expected call of MarkSalesforceAccountsDelivered.
func (mr *MockISalesforceDbMockRecorder) MarkSalesforceAccountsDelivered(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkSalesforceAccountsDelivered", reflect.TypeOf((*MockISalesforceDb)(nil).MarkSalesforceAccountsDelivered), ch, model)
}

// UpdateSalesforceAddress mocks base method.
func (m *MockISalesforceDb) UpdateSalesforceAddress(ch chan *UpdateSalesforceAddressDbResponse, model *UpdateSalesforceAddressDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateSalesforceAddress", ch, model)
}

// UpdateSalesforceAddress indicates an expected call of UpdateSalesforceAddress.
func (mr *MockISalesforceDbMockRecorder) UpdateSalesforceAddress(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSalesforceAddress", reflect.TypeOf((*MockISalesforceDb)(nil).UpdateSalesforceAddress), ch, model)
}

// UpdateSalesforceContact mocks base method.
func (m *MockISalesforceDb) UpdateSalesforceContact(ch chan *UpdateSalesforceContactDbResponse, model *UpdateSalesforceContactDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateSalesforceContact", ch, model)
}

// UpdateSalesforceContact indicates an expected call of UpdateSalesforceContact.
func (mr *MockISalesforceDbMockRecorder) UpdateSalesforceContact(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSalesforceContact", reflect.TypeOf((*MockISalesforceDb)(nil).UpdateSalesforceContact), ch, model)
}

// UpsertSalesforceAccount mocks base method.
func (m *MockISalesforceDb) UpsertSalesforceAccount(ch chan *UpsertSalesforceAccountDbResponse, model *UpsertSalesforceAccountDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpsertSalesforceAccount", ch, model)
}

// UpsertSalesforceAccount indicates an expected call of UpsertSalesforceAccount.
func (mr *MockISalesforceDbMockRecorder) UpsertSalesforceAccount(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertSalesforceAccount", reflect.TypeOf((*MockISalesforceDb)(nil).UpsertSalesforceAccount), ch, model)
}
