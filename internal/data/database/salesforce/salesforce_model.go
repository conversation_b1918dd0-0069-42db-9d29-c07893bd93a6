package salesforce

import "time"

type UpsertSalesforceAccountDbModel struct {
	ReplayId                       int
	PayloadTimestamp               time.Time
	SalesforceGridId               string
	WarehouseType                  int
	CommercialName                 string
	CommercialType                 int
	TaxAdministration              string
	TaxNumber                      string
	IBAN                           string
	BillingAddressLine1            string
	BillingAddressLine2            string
	BillingAddressTown             string
	BillingAddressCity             string
	BillingAddressCountry          string
	BillingAddressPostCode         string
	RestaurantAddressLine1         string
	RestaurantAddressLine2         string
	RestaurantAddressTown          string
	RestaurantAddressCity          string
	RestaurantAddressCountry       string
	RestaurantAddressPostCode      string
	EmailAddress                   string
	MobilePhoneNumber              string
	PhoneNumber                    string
	StoreSiteName                  string
	ResponsibleSalesRepresentative string
	IsCreate                       bool
	IsUpdate                       bool
}

type UpdateSalesforceContactDbModel struct {
	MessageId         string
	ReplayId          int
	PayloadTimestamp  time.Time
	SalesforceGridId  string
	EmailAddress      string
	MobilePhoneNumber string
	PhoneNumber       string
}

type UpdateSalesforceAddressDbModel struct {
	MessageId                 string
	ReplayId                  int
	PayloadTimestamp          time.Time
	SalesforceGridId          string
	BillingAddressLine1       string
	BillingAddressLine2       string
	BillingAddressTown        string
	BillingAddressCity        string
	BillingAddressCountry     string
	BillingAddressPostCode    string
	RestaurantAddressLine1    string
	RestaurantAddressLine2    string
	RestaurantAddressTown     string
	RestaurantAddressCity     string
	RestaurantAddressCountry  string
	RestaurantAddressPostCode string
}

type MarkSalesforceAccountsAsDeliveredModel struct {
	Ids []int `validate:"required"`
}
