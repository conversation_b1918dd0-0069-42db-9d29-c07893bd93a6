package salesforce

type UpsertSalesforceAccountDbResponse struct {
	Error error `json:"-"`
}

type UpdateSalesforceContactDbResponse struct {
	Error error `json:"-"`
}

type UpdateSalesforceAddressDbResponse struct {
	Error error `json:"-"`
}

type GetUndeliveredSalesforceAccountsResponse struct {
	UndeliveredSalesforceAccounts []SalesforceAccount
	Error                         error `json:"-"`
}

type SalesforceAccount struct {
	Id                             int
	SalesforceGridId               string
	StoreType                      int
	CommercialName                 string
	CommercialType                 int
	TaxAdministration              string
	TaxNumber                      string
	IBAN                           string
	BillingAddressLine1            string
	BillingAddressLine2            string
	BillingAddressTown             string
	BillingAddressCity             string
	BillingAddressCountry          string
	BillingAddressPostCode         string
	RestaurantAddressLine1         string
	RestaurantAddressLine2         string
	RestaurantAddressTown          string
	RestaurantAddressCity          string
	RestaurantAddressCountry       string
	RestaurantAddressPostCode      string
	EmailAddress                   string
	MobilePhoneNumber              string
	PhoneNumber                    string
	StoreSiteName                  string
	ResponsibleSalesRepresentative string
	PayloadTimestamp               string
	ReplayId                       int
}

type MarkSalesforceAccountsAsDeliveredResponse struct {
	Error error `json:"-"`
}
