package salesforce

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	_ "github.com/lib/pq"
)

type ISalesforceDb interface {
	UpsertSalesforceAccount(ch chan *UpsertSalesforceAccountDbResponse, model *UpsertSalesforceAccountDbModel)
	UpdateSalesforceContact(ch chan *UpdateSalesforceContactDbResponse, model *UpdateSalesforceContactDbModel)
	UpdateSalesforceAddress(ch chan *UpdateSalesforceAddressDbResponse, model *UpdateSalesforceAddressDbModel)
	GetUndeliveredSalesforceAccounts(ch chan *GetUndeliveredSalesforceAccountsResponse)
	MarkSalesforceAccountsDelivered(ch chan *MarkSalesforceAccountsAsDeliveredResponse, model *MarkSalesforceAccountsAsDeliveredModel)
}

type SalesforceDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

// NewSalesForceDb
// Returns a new SalesforceDb.
func NewSalesForceDb(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) ISalesforceDb {
	db := SalesforceDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

// UpsertSalesforceAccount
// Inserts or updates warehouse's salesforce info.
func (d *SalesforceDb) UpsertSalesforceAccount(ch chan *UpsertSalesforceAccountDbResponse, model *UpsertSalesforceAccountDbModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpsertSalesforceAccountDbResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &UpsertSalesforceAccountDbResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `	UPDATE warehouses
				SET
					is_delivered = false, 
					modified_date = current_timestamp
				WHERE
					salesforce_grid_id = $1 
					AND EXISTS (SELECT 1 FROM warehouses_salesforce_info WHERE salesforce_grid_id = $1 AND warehouse_type != $2);`

	result, err := connection.ExecContext(ctx, query, model.SalesforceGridId, model.WarehouseType)

	if err != nil {
		ch <- &UpsertSalesforceAccountDbResponse{Error: err}
		return
	}

	query = `INSERT INTO warehouses_salesforce_info
				(
					salesforce_grid_id,
					warehouse_type,
					created_date,
					modified_date,
					is_delivered,
					commercial_name,
					commercial_type,
					tax_administration,
					tax_number,
					iban,
					billing_address_line1,
					billing_address_line2,
					billing_address_town,
					billing_address_city,
					billing_address_country,
					billing_address_postcode,
					restaurant_address_line1,
					restaurant_address_line2,
					restaurant_address_town,
					restaurant_address_city,
					restaurant_address_country,
					restaurant_address_postcode,
					email_address,
					mobile_phone_number,
					phone_number,
					store_site_name,
					responsible_sales_representative,
					payload_timestamp,
				 	replay_id
				)
				VALUES
			   	(
			    	$1,
					$2,
					current_timestamp, 
					null,
					false,
					$3,
					$4,
					$5,
					$6,
					$7,
					$8,
					$9,
					$10,
					$11,
					$12,
					$13,
					$14,
					$15,
					$16,
					$17,
					$18,
					$19,
					$20,
					$21,
					$22,
					$23,
					$24,
					$25,
					$26
				)
				ON CONFLICT (salesforce_grid_id)
					DO UPDATE SET
						modified_date      = current_timestamp,
						is_delivered 	   = false,
					    payload_timestamp  = $25,
					    replay_id		   = $26,
						warehouse_type 	   = $2,
						commercial_name    = $3,
						commercial_type    = $4,
						tax_administration = $5,
						tax_number         = $6,
						iban               = $7,
						store_site_name    = $23,
						responsible_sales_representative = $24
					WHERE
					    (warehouses_salesforce_info.payload_timestamp IS NULL OR warehouses_salesforce_info.payload_timestamp < $25 OR (warehouses_salesforce_info.payload_timestamp = $25 AND warehouses_salesforce_info.replay_id < $26));`

	result, err = connection.ExecContext(ctx, query, model.SalesforceGridId, model.WarehouseType, model.CommercialName, model.CommercialType, model.TaxAdministration, model.TaxNumber, model.IBAN, model.BillingAddressLine1, model.BillingAddressLine2, model.BillingAddressTown, model.BillingAddressCity, model.BillingAddressCountry, model.BillingAddressPostCode, model.RestaurantAddressLine1, model.RestaurantAddressLine2, model.RestaurantAddressTown, model.RestaurantAddressCity, model.RestaurantAddressCountry, model.RestaurantAddressPostCode, model.EmailAddress, model.MobilePhoneNumber, model.PhoneNumber, model.StoreSiteName, model.ResponsibleSalesRepresentative, model.PayloadTimestamp, model.ReplayId)

	if err != nil {
		ch <- &UpsertSalesforceAccountDbResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &UpsertSalesforceAccountDbResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &UpsertSalesforceAccountDbResponse{Error: err}
		return
	}

	// Update message can reach us before create. In that case we need to update fields that were not included in the update message.
	if model.IsCreate {
		query = `	UPDATE warehouses_salesforce_info
					SET
					    payload_timestamp  			= $2,
					    replay_id		   			= $3,
						billing_address_line1       = $4,
						billing_address_line2       = $5,
						billing_address_town        = $6,
						billing_address_city        = $7,
						billing_address_country     = $8,
						billing_address_postcode    = $9,
						restaurant_address_line1    = $10,
						restaurant_address_line2    = $11,
						restaurant_address_town     = $12,
						restaurant_address_city     = $13,
						restaurant_address_country  = $14,
						restaurant_address_postcode = $15,
						email_address               = $16,
						mobile_phone_number         = $17,
						phone_number 		        = $18
					WHERE
						salesforce_grid_id = $1
						AND (payload_timestamp IS NULL OR payload_timestamp < $2 OR (payload_timestamp = $2 AND replay_id <= $3));`

		result, err = connection.ExecContext(ctx, query, model.SalesforceGridId, model.PayloadTimestamp, model.ReplayId, model.BillingAddressLine1, model.BillingAddressLine2, model.BillingAddressTown, model.BillingAddressCity, model.BillingAddressCountry, model.BillingAddressPostCode, model.RestaurantAddressLine1, model.RestaurantAddressLine2, model.RestaurantAddressTown, model.RestaurantAddressCity, model.RestaurantAddressCountry, model.RestaurantAddressPostCode, model.EmailAddress, model.MobilePhoneNumber, model.PhoneNumber)

		if err != nil {
			ch <- &UpsertSalesforceAccountDbResponse{Error: err}
			return
		}

		rows, err := result.RowsAffected()
		if err != nil {
			ch <- &UpsertSalesforceAccountDbResponse{Error: err}
			return
		}

		if rows != 1 {
			ch <- &UpsertSalesforceAccountDbResponse{Error: err}
			return
		}
	}

	ch <- &UpsertSalesforceAccountDbResponse{Error: nil}
}

// UpdateSalesforceContact
// Updates salesforce account's contact info.
func (d *SalesforceDb) UpdateSalesforceContact(ch chan *UpdateSalesforceContactDbResponse, model *UpdateSalesforceContactDbModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateSalesforceContactDbResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &UpdateSalesforceContactDbResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `  UPDATE warehouses_salesforce_info 
  				SET
					modified_date       = current_timestamp,
  				    is_delivered        = false,
  				    payload_timestamp   = CAST($5 AS timestamp),
  				    replay_id		    = $6,
					email_address       = $2,
  				    mobile_phone_number = $3,
  				    phone_number        = $4
				WHERE
					salesforce_grid_id = $1
					AND (payload_timestamp IS NULL OR payload_timestamp < $5 OR (payload_timestamp = $5 AND replay_id < $6))`

	result, err := connection.ExecContext(ctx, query, model.SalesforceGridId, model.EmailAddress, model.MobilePhoneNumber, model.PhoneNumber, model.PayloadTimestamp, model.ReplayId)

	if err != nil {
		ch <- &UpdateSalesforceContactDbResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &UpdateSalesforceContactDbResponse{Error: err}
		return
	}

	if rows == 0 {
		d.loggr.Info(fmt.Sprintf("UpdateSalesforceContact [%s]: no record to update (salesforce_grid_id='%s') or message is old", model.MessageId, model.SalesforceGridId))
		ch <- &UpdateSalesforceContactDbResponse{Error: nil}
		return
	}

	if rows != 1 {
		ch <- &UpdateSalesforceContactDbResponse{Error: fmt.Errorf("UpdateSalesforceContact [%s]: multiple records found for salesforce_grid_id='%s'", model.MessageId, model.SalesforceGridId)}
		return
	}

	ch <- &UpdateSalesforceContactDbResponse{Error: nil}
}

// UpdateSalesforceAddress
// Updates salesforce account's address info.
func (d *SalesforceDb) UpdateSalesforceAddress(ch chan *UpdateSalesforceAddressDbResponse, model *UpdateSalesforceAddressDbModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateSalesforceAddressDbResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &UpdateSalesforceAddressDbResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `  UPDATE warehouses_salesforce_info 
  				SET
					modified_date      			= current_timestamp,
  				    is_delivered 				= false,
  				    payload_timestamp   		= $14,
  				    replay_id		    		= $15,  				    
					billing_address_line1 		= $2,
  				    billing_address_line2 		= $3,
  				    billing_address_town 		= $4,
  				    billing_address_city 		= $5,
  				    billing_address_country 	= $6,
  				    billing_address_postcode 	= $7,
  				    restaurant_address_line1 	= $8,
  				    restaurant_address_line2 	= $9,
  				    restaurant_address_town 	= $10,
  				    restaurant_address_city     = $11,
  				    restaurant_address_country  = $12,
  				    restaurant_address_postcode = $13
				WHERE
					salesforce_grid_id = $1
					AND (payload_timestamp IS NULL OR payload_timestamp < $14 OR (payload_timestamp = $14 AND replay_id < $15))`

	result, err := connection.ExecContext(ctx, query, model.SalesforceGridId, model.BillingAddressLine1, model.BillingAddressLine2, model.BillingAddressTown, model.BillingAddressCity, model.BillingAddressCountry, model.BillingAddressPostCode, model.RestaurantAddressLine1, model.RestaurantAddressLine2, model.RestaurantAddressTown, model.RestaurantAddressCity, model.RestaurantAddressCountry, model.RestaurantAddressPostCode, model.PayloadTimestamp, model.ReplayId)

	if err != nil {
		ch <- &UpdateSalesforceAddressDbResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &UpdateSalesforceAddressDbResponse{Error: err}
		return
	}

	if rows == 0 {
		d.loggr.Info(fmt.Sprintf("UpdateSalesforceAddress [%s]: no record to update (salesforce_grid_id='%s') or message is old", model.MessageId, model.SalesforceGridId))
		ch <- &UpdateSalesforceAddressDbResponse{Error: nil}
		return
	}

	if rows != 1 {
		ch <- &UpdateSalesforceAddressDbResponse{Error: fmt.Errorf("UpdateSalesforceAddress [%s]: multiple records found for salesforce_grid_id='%s'", model.MessageId, model.SalesforceGridId)}
		return
	}

	ch <- &UpdateSalesforceAddressDbResponse{Error: nil}
}

// GetUndeliveredSalesforceAccounts
// Gets undelivered salesforce accounts.
func (d *SalesforceDb) GetUndeliveredSalesforceAccounts(ch chan *GetUndeliveredSalesforceAccountsResponse) {
	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetUndeliveredSalesforceAccountsResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `  SELECT
         			id,
					salesforce_grid_id,
					COALESCE(warehouse_type, -1),
					COALESCE(commercial_name, ''),
					COALESCE(commercial_type, -1),
					COALESCE(tax_administration, ''),
					COALESCE(tax_number, ''),
					COALESCE(iban, ''),
					COALESCE(billing_address_line1, ''),
					COALESCE(billing_address_line2, ''),
					COALESCE(billing_address_town, ''),
					COALESCE(billing_address_city, ''),
					COALESCE(billing_address_country, ''),
					COALESCE(billing_address_postcode, ''),
					COALESCE(restaurant_address_line1, ''),
					COALESCE(restaurant_address_line2, ''),
					COALESCE(restaurant_address_town, ''),
					COALESCE(restaurant_address_city, ''),
					COALESCE(restaurant_address_country, ''),
					COALESCE(restaurant_address_postcode, ''),
					COALESCE(email_address, ''),
					COALESCE(mobile_phone_number, ''),
					COALESCE(phone_number, ''),
					COALESCE(store_site_name, ''),
					COALESCE(responsible_sales_representative, '')
				FROM warehouses_salesforce_info
				WHERE
					is_delivered = false`

	var undeliveredAccounts []SalesforceAccount
	rows, dbErr := connection.QueryContext(ctx, query)

	if dbErr != nil {
		ch <- &GetUndeliveredSalesforceAccountsResponse{Error: dbErr}
		return
	}

	defer rows.Close()

	for rows.Next() {
		var salesforceAccount SalesforceAccount

		err := rows.Scan(&salesforceAccount.Id,
			&salesforceAccount.SalesforceGridId,
			&salesforceAccount.StoreType,
			&salesforceAccount.CommercialName,
			&salesforceAccount.CommercialType,
			&salesforceAccount.TaxAdministration,
			&salesforceAccount.TaxNumber,
			&salesforceAccount.IBAN,
			&salesforceAccount.BillingAddressLine1,
			&salesforceAccount.BillingAddressLine2,
			&salesforceAccount.BillingAddressTown,
			&salesforceAccount.BillingAddressCity,
			&salesforceAccount.BillingAddressCountry,
			&salesforceAccount.BillingAddressPostCode,
			&salesforceAccount.RestaurantAddressLine1,
			&salesforceAccount.RestaurantAddressLine2,
			&salesforceAccount.RestaurantAddressTown,
			&salesforceAccount.RestaurantAddressCity,
			&salesforceAccount.RestaurantAddressCountry,
			&salesforceAccount.RestaurantAddressPostCode,
			&salesforceAccount.EmailAddress,
			&salesforceAccount.MobilePhoneNumber,
			&salesforceAccount.PhoneNumber,
			&salesforceAccount.StoreSiteName,
			&salesforceAccount.ResponsibleSalesRepresentative)

		if err != nil {
			ch <- &GetUndeliveredSalesforceAccountsResponse{Error: err}
			return
		}
		undeliveredAccounts = append(undeliveredAccounts, salesforceAccount)
	}

	if err := rows.Err(); err != nil {
		ch <- &GetUndeliveredSalesforceAccountsResponse{Error: err}
		return
	}

	ch <- &GetUndeliveredSalesforceAccountsResponse{
		UndeliveredSalesforceAccounts: undeliveredAccounts,
		Error:                         nil}
}

// MarkSalesforceAccountsDelivered
// Marks salesforce account as delivered info.
func (d *SalesforceDb) MarkSalesforceAccountsDelivered(ch chan *MarkSalesforceAccountsAsDeliveredResponse, model *MarkSalesforceAccountsAsDeliveredModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &MarkSalesforceAccountsAsDeliveredResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &MarkSalesforceAccountsAsDeliveredResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `UPDATE warehouses_salesforce_info SET is_delivered = true WHERE id = ANY($1::int[])`

	ids := make([]string, len(model.Ids))
	for i, x := range model.Ids {
		ids[i] = strconv.Itoa(x)
	}

	param := "{" + strings.Join(ids, ",") + "}"

	result, err := connection.ExecContext(ctx, query, param)

	if err != nil {
		ch <- &MarkSalesforceAccountsAsDeliveredResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &MarkSalesforceAccountsAsDeliveredResponse{Error: err}
		return
	}
	if rows != 1 {
		ch <- &MarkSalesforceAccountsAsDeliveredResponse{Error: err}
		return
	}

	ch <- &MarkSalesforceAccountsAsDeliveredResponse{Error: nil}
}
