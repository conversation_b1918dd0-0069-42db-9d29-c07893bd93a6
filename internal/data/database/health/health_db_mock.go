// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/health/health_db.go

// Package health is a generated GoMock package.
package health

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIHealthDb is a mock of IHealthDb interface.
type MockIHealthDb struct {
	ctrl     *gomock.Controller
	recorder *MockIHealthDbMockRecorder
}

// MockIHealthDbMockRecorder is the mock recorder for MockIHealthDb.
type MockIHealthDbMockRecorder struct {
	mock *MockIHealthDb
}

// NewMockIHealthDb creates a new mock instance.
func NewMockIHealthDb(ctrl *gomock.Controller) *MockIHealthDb {
	mock := &MockIHealthDb{ctrl: ctrl}
	mock.recorder = &MockIHealthDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIHealthDb) EXPECT() *MockIHealthDbMockRecorder {
	return m.recorder
}

// Ping mocks base method.
func (m *MockIHealthDb) Ping() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping")
	ret0, _ := ret[0].(error)
	return ret0
}

// Ping indicates an expected call of Ping.
func (mr *MockIHealthDbMockRecorder) Ping() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockIHealthDb)(nil).Ping))
}
