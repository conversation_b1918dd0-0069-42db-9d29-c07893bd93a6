package configuration

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	_ "github.com/lib/pq"
)

type IConfigurationDb interface {
	GetConfig(ch chan *GetConfigurationResponse, model *GetConfigurationDbModel)
	AddConfig(ch chan *AddConfigResponse, model AddConfigDbModel)
	UpdateConfig(ch chan *UpdateConfigResponse, model UpdateConfigDbModel)
	UpsertConfig(ch chan *UpsertConfigResponse, model UpsertConfigDbModel)
}

type ConfigurationDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

// NewConfigurationDb
// Returns a new Server Configuration Db.
func NewConfigurationDb(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IConfigurationDb {
	db := ConfigurationDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

// Get server configuration db
// Gets sample response from postgresql db.
func (d *ConfigurationDb) GetConfig(ch chan *GetConfigurationResponse, model *GetConfigurationDbModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetConfigurationResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetConfigurationResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `SELECT id, data_type_id, data_key, data_value FROM server_configurations WHERE data_key = $1`

	var config GetConfigurationResponse
	dbErr := connection.QueryRowContext(ctx, query, model.DataKey).Scan(&config.Id, &config.DataTypeId, &config.DataKey, &config.DataValue)

	if dbErr != nil {
		ch <- &GetConfigurationResponse{Error: dbErr}
		return
	}

	ch <- &config
}

func (d *ConfigurationDb) AddConfig(ch chan *AddConfigResponse, model AddConfigDbModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddConfigResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &AddConfigResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `INSERT INTO server_configurations (data_type_id, data_key, data_value) VALUES ($1, $2, $3)`

	result, err := connection.ExecContext(ctx, query, model.DataTypeId, model.DataKey, model.DataValue)

	if err != nil {
		ch <- &AddConfigResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &AddConfigResponse{Error: err}
		return
	}
	if rows < 1 {
		ch <- &AddConfigResponse{Error: errors.New("couldn't add configuration")}
		return
	}

	ch <- &AddConfigResponse{
		Error:     nil,
		DataKey:   model.DataKey,
		DataValue: model.DataValue,
	}
}

func (d *ConfigurationDb) UpdateConfig(ch chan *UpdateConfigResponse, model UpdateConfigDbModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateConfigResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &UpdateConfigResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `
		UPDATE server_configurations SET 
			data_value = $2
		WHERE data_key = $1
	`

	result, err := connection.ExecContext(ctx, query, model.DataKey, model.DataValue)

	if err != nil {
		ch <- &UpdateConfigResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &UpdateConfigResponse{Error: err}
		return
	}
	if rows < 1 {
		ch <- &UpdateConfigResponse{Error: errors.New("couldn't update configuration")}
		return
	}

	ch <- &UpdateConfigResponse{
		Error:     nil,
		DataKey:   model.DataKey,
		DataValue: model.DataValue,
	}
}

func (d *ConfigurationDb) UpsertConfig(ch chan *UpsertConfigResponse, model UpsertConfigDbModel) {
	modelErr := d.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpsertConfigResponse{Error: modelErr}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &UpsertConfigResponse{Error: err}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `
		INSERT INTO server_configurations as sc (data_type_id, data_key, data_value) VALUES ($1, $2, $3)
		ON CONFLICT (data_key)
		DO UPDATE SET
			data_value = $3
		WHERE sc.data_key = $2
	`

	result, err := connection.ExecContext(ctx, query, model.DataTypeId, model.DataKey, model.DataValue)

	if err != nil {
		ch <- &UpsertConfigResponse{Error: err}
		return
	}

	rows, err := result.RowsAffected()
	if err != nil {
		ch <- &UpsertConfigResponse{Error: err}
		return
	}
	if rows < 1 {
		ch <- &UpsertConfigResponse{Error: errors.New("couldn't upsert configuration")}
		return
	}

	ch <- &UpsertConfigResponse{
		Error:     nil,
		DataKey:   model.DataKey,
		DataValue: model.DataValue,
	}
}
