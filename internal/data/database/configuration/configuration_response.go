package configuration

type GetConfigurationResponse struct {
	Error      error  `json:"-"`
	Id         int    `validate:"required"`
	DataTypeId int    `validate:"required"`
	DataKey    string `validate:"required"`
	DataValue  string `validate:"required"`
}

type AddConfigResponse struct {
	Error     error `json:"-"`
	DataKey   string
	DataValue string
}
type UpdateConfigResponse struct {
	Error     error `json:"-"`
	DataKey   string
	DataValue string
}

type UpsertConfigResponse struct {
	Error        error `json:"-"`
	DataKey      string
	DataValue    string
	OldDataValue string
}
