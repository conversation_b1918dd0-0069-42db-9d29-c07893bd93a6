// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/configuration/configuration_db.go

// Package configuration is a generated GoMock package.
package configuration

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIConfigurationDb is a mock of IConfigurationDb interface.
type MockIConfigurationDb struct {
	ctrl     *gomock.Controller
	recorder *MockIConfigurationDbMockRecorder
}

// MockIConfigurationDbMockRecorder is the mock recorder for MockIConfigurationDb.
type MockIConfigurationDbMockRecorder struct {
	mock *MockIConfigurationDb
}

// NewMockIConfigurationDb creates a new mock instance.
func NewMockIConfigurationDb(ctrl *gomock.Controller) *MockIConfigurationDb {
	mock := &MockIConfigurationDb{ctrl: ctrl}
	mock.recorder = &MockIConfigurationDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIConfigurationDb) EXPECT() *MockIConfigurationDbMockRecorder {
	return m.recorder
}

// AddConfig mocks base method.
func (m *MockIConfigurationDb) AddConfig(ch chan *AddConfigResponse, model AddConfigDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddConfig", ch, model)
}

// AddConfig indicates an expected call of AddConfig.
func (mr *MockIConfigurationDbMockRecorder) AddConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddConfig", reflect.TypeOf((*MockIConfigurationDb)(nil).AddConfig), ch, model)
}

// GetConfig mocks base method.
func (m *MockIConfigurationDb) GetConfig(ch chan *GetConfigurationResponse, model *GetConfigurationDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetConfig", ch, model)
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockIConfigurationDbMockRecorder) GetConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockIConfigurationDb)(nil).GetConfig), ch, model)
}

// UpdateConfig mocks base method.
func (m *MockIConfigurationDb) UpdateConfig(ch chan *UpdateConfigResponse, model UpdateConfigDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateConfig", ch, model)
}

// UpdateConfig indicates an expected call of UpdateConfig.
func (mr *MockIConfigurationDbMockRecorder) UpdateConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfig", reflect.TypeOf((*MockIConfigurationDb)(nil).UpdateConfig), ch, model)
}

// UpsertConfig mocks base method.
func (m *MockIConfigurationDb) UpsertConfig(ch chan *UpsertConfigResponse, model UpsertConfigDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpsertConfig", ch, model)
}

// UpsertConfig indicates an expected call of UpsertConfig.
func (mr *MockIConfigurationDbMockRecorder) UpsertConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertConfig", reflect.TypeOf((*MockIConfigurationDb)(nil).UpsertConfig), ch, model)
}
