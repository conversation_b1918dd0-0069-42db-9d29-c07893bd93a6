package configuration

type GetConfigurationDbModel struct {
	DataKey string `validate:"required"`
}

type AddConfigDbModel struct {
	DataKey    string `validate:"required"`
	DataValue  string `validate:"required"`
	DataTypeId int    `validate:"required"`
}
type UpdateConfigDbModel struct {
	DataKey   string `validate:"required"`
	DataValue string `validate:"required"`
}

type UpsertConfigDbModel struct {
	DataTypeId int    `validate:"required"`
	DataKey    string `validate:"required"`
	DataValue  string `validate:"required"`
}
