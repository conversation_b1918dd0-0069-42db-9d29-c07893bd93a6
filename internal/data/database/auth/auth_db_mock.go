// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/auth/auth_db.go

// Package auth is a generated GoMock package.
package auth

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIAuthDb is a mock of IAuthDb interface.
type MockIAuthDb struct {
	ctrl     *gomock.Controller
	recorder *MockIAuthDbMockRecorder
}

// MockIAuthDbMockRecorder is the mock recorder for MockIAuthDb.
type MockIAuthDbMockRecorder struct {
	mock *MockIAuthDb
}

// NewMockIAuthDb creates a new mock instance.
func NewMockIAuthDb(ctrl *gomock.Controller) *MockIAuthDb {
	mock := &MockIAuthDb{ctrl: ctrl}
	mock.recorder = &MockIAuthDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAuthDb) EXPECT() *MockIAuthDbMockRecorder {
	return m.recorder
}

// AddRefreshToken mocks base method.
func (m *MockIAuthDb) AddRefreshToken(ch chan *AddRefreshTokenResponse, model *AddRefreshTokenModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddRefreshToken", ch, model)
}

// AddRefreshToken indicates an expected call of AddRefreshToken.
func (mr *MockIAuthDbMockRecorder) AddRefreshToken(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRefreshToken", reflect.TypeOf((*MockIAuthDb)(nil).AddRefreshToken), ch, model)
}

// GetUserByPassword mocks base method.
func (m *MockIAuthDb) GetUserByPassword(ch chan *GetUserByPasswordResponse, model *GetUserByPasswordModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetUserByPassword", ch, model)
}

// GetUserByPassword indicates an expected call of GetUserByPassword.
func (mr *MockIAuthDbMockRecorder) GetUserByPassword(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByPassword", reflect.TypeOf((*MockIAuthDb)(nil).GetUserByPassword), ch, model)
}

// GetUserByRefreshToken mocks base method.
func (m *MockIAuthDb) GetUserByRefreshToken(ch chan *GetUserByRefreshTokenResponse, model *GetUserByRefreshTokenModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetUserByRefreshToken", ch, model)
}

// GetUserByRefreshToken indicates an expected call of GetUserByRefreshToken.
func (mr *MockIAuthDbMockRecorder) GetUserByRefreshToken(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByRefreshToken", reflect.TypeOf((*MockIAuthDb)(nil).GetUserByRefreshToken), ch, model)
}
