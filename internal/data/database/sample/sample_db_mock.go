// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/sample/sample_db.go

// Package sample is a generated GoMock package.
package sample

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISampleDb is a mock of ISampleDb interface.
type MockISampleDb struct {
	ctrl     *gomock.Controller
	recorder *MockISampleDbMockRecorder
}

// MockISampleDbMockRecorder is the mock recorder for MockISampleDb.
type MockISampleDbMockRecorder struct {
	mock *MockISampleDb
}

// NewMockISampleDb creates a new mock instance.
func NewMockISampleDb(ctrl *gomock.Controller) *MockISampleDb {
	mock := &MockISampleDb{ctrl: ctrl}
	mock.recorder = &MockISampleDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISampleDb) EXPECT() *MockISampleDbMockRecorder {
	return m.recorder
}

// GetSample mocks base method.
func (m *MockISampleDb) GetSample(ch chan *GetSampleDbResponse, model *GetSampleDbModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetSample", ch, model)
}

// GetSample indicates an expected call of GetSample.
func (mr *MockISampleDbMockRecorder) GetSample(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSample", reflect.TypeOf((*MockISampleDb)(nil).GetSample), ch, model)
}
