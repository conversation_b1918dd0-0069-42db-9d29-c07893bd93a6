// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/database/product/product_db.go

// Package product is a generated GoMock package.
package product

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIProductDb is a mock of IProductDb interface.
type MockIProductDb struct {
	ctrl     *gomock.Controller
	recorder *MockIProductDbMockRecorder
}

// MockIProductDbMockRecorder is the mock recorder for MockIProductDb.
type MockIProductDbMockRecorder struct {
	mock *MockIProductDb
}

// NewMockIProductDb creates a new mock instance.
func NewMockIProductDb(ctrl *gomock.Controller) *MockIProductDb {
	mock := &MockIProductDb{ctrl: ctrl}
	mock.recorder = &MockIProductDbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIProductDb) EXPECT() *MockIProductDbMockRecorder {
	return m.recorder
}

// GetAllProductsStream mocks base method.
func (m *MockIProductDb) GetAllProductsStream(ch chan *GetAllProductStreamResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetAllProductsStream", ch)
}

// GetAllProductsStream indicates an expected call of GetAllProductsStream.
func (mr *MockIProductDbMockRecorder) GetAllProductsStream(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllProductsStream", reflect.TypeOf((*MockIProductDb)(nil).GetAllProductsStream), ch)
}

// GetProductStream mocks base method.
func (m *MockIProductDb) GetProductStream(ch chan *GetProductStreamResponse, model GetProductStreamModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProductStream", ch, model)
}

// GetProductStream indicates an expected call of GetProductStream.
func (mr *MockIProductDbMockRecorder) GetProductStream(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductStream", reflect.TypeOf((*MockIProductDb)(nil).GetProductStream), ch, model)
}

// GetProductStreamBySku mocks base method.
func (m *MockIProductDb) GetProductStreamBySku(ch chan *GetProductStreamResponse, sku string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProductStreamBySku", ch, sku)
}

// GetProductStreamBySku indicates an expected call of GetProductStreamBySku.
func (mr *MockIProductDbMockRecorder) GetProductStreamBySku(ch, sku interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductStreamBySku", reflect.TypeOf((*MockIProductDb)(nil).GetProductStreamBySku), ch, sku)
}

// GetProductStreamsBySku mocks base method.
func (m *MockIProductDb) GetProductStreamsBySku(ch chan *GetProductStreamsBySkuResponse, skus []string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProductStreamsBySku", ch, skus)
}

// GetProductStreamsBySku indicates an expected call of GetProductStreamsBySku.
func (mr *MockIProductDbMockRecorder) GetProductStreamsBySku(ch, skus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductStreamsBySku", reflect.TypeOf((*MockIProductDb)(nil).GetProductStreamsBySku), ch, skus)
}

// UpsertProductStream mocks base method.
func (m *MockIProductDb) UpsertProductStream(ch chan error, model UpsertProductStreamModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpsertProductStream", ch, model)
}

// UpsertProductStream indicates an expected call of UpsertProductStream.
func (mr *MockIProductDbMockRecorder) UpsertProductStream(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertProductStream", reflect.TypeOf((*MockIProductDb)(nil).UpsertProductStream), ch, model)
}
