package product

import "time"

type GetProductStreamResponse struct {
	Error     error `json:"-"`
	ProductId string
	Sku       string
	Message   string
	Model     ProductMessageModel
	Timestamp time.Time
}

type GetProductStreamsBySkuResponse struct {
	Error    error `json:"-"`
	Messages []GetProductStreamResponse
}

type ProductMessageModel struct {
	Content        Content `json:"content"`
	GlobalEntityID string  `json:"global_entity_id"`
	Version        string  `json:"version"`
}

type Content struct {
	ProductId  string      `json:"product_id"`
	Name       string      `json:"name"`
	Names      []Name      `json:"names"`
	Attributes []Attribute `json:"attributes"`
	Timestamp  time.Time   `json:"timestamp"`
}

type Attribute struct {
	AttributeId string `json:"attribute_id"`
	Name        string `json:"name"`
	Names       []Name `json:"names"`
}

type Name struct {
	Locale string `json:"locale"`
	Value  string `json:"value"`
}

type GetAllProductStreamResponse struct {
	Error             error `json:"-"`
	AllProductsStream []Product
}

type Product struct {
	Sku         string
	Name        string
	Price       float64
	ListPrice   float64
	StockAmount int
}
