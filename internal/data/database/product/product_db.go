package product

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/lib/pq"
	_ "github.com/lib/pq"
)

type IProductDb interface {
	UpsertProductStream(ch chan error, model UpsertProductStreamModel)
	GetProductStream(ch chan *GetProductStreamResponse, model GetProductStreamModel)
	GetProductStreamBySku(ch chan *GetProductStreamResponse, sku string)
	GetProductStreamsBySku(ch chan *GetProductStreamsBySkuResponse, skus []string)
	GetAllProductsStream(ch chan *GetAllProductStreamResponse)
}

type ProductDb struct {
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	environment      env.IEnvironment
	connectionString string
	driverName       string
	timeout          time.Duration
}

// NewProductDb
// Returns a new ProductDb.
func NewProductDb(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IProductDb {
	db := ProductDb{
		environment:      environment,
		loggr:            loggr,
		validatr:         validatr,
		cachr:            cachr,
		driverName:       "postgres",
		connectionString: environment.Get(env.PostgresqlConnectionString),
		timeout:          time.Second * 5,
	}

	return &db
}

// UpsertProduct
// Upsert product that comes from datafridge product-stream.
func (d *ProductDb) UpsertProductStream(ch chan error, model UpsertProductStreamModel) {
	validationErr := d.validatr.ValidateStruct(model)
	if validationErr != nil {
		ch <- validationErr
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- err
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `
		INSERT INTO product_stream_messages as psm (product_id,
			sku,
			message,
			timestamp,
			message_date,
			name,
			price,
			list_price,
			stock_amount
		)
		VALUES ($1,
		$2,
		$3,
		$4,
		$5,
		$6,
		$7,
		$8,
		$9)
		ON CONFLICT (product_id)
		DO UPDATE SET 
			sku 	     = $2,
			message      = $3,
			timestamp    = $4,
			message_date = $5,
			name         = $6,
			price        = $7,
			list_price   = $8,
			stock_amount = $9
		WHERE psm.timestamp < $4`

	result, err := connection.ExecContext(ctx, query, model.ProductId, model.Sku, model.Message, model.Timestamp, time.Now(), model.Name, model.Price, model.ListPrice, model.StockAmount)

	if err != nil {
		ch <- err
		return
	}

	_, err = result.RowsAffected()
	if err != nil {
		ch <- err
		return
	}

	ch <- nil
}

//GetProduct
//Gets product with given productId from logoadapterdb
func (d *ProductDb) GetProductStream(ch chan *GetProductStreamResponse, model GetProductStreamModel) {
	validationErr := d.validatr.ValidateStruct(model)
	if validationErr != nil {
		ch <- &GetProductStreamResponse{
			Error: validationErr,
		}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetProductStreamResponse{
			Error: err,
		}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select product_id, sku, message, timestamp from product_stream_messages where product_id = $1`

	row := connection.QueryRowContext(ctx, query, model.ProductId)
	if row.Err() != nil {
		ch <- &GetProductStreamResponse{
			Error: row.Err(),
		}
		return
	}

	response := GetProductStreamResponse{}

	scanErr := row.Scan(&response.ProductId, &response.Sku, &response.Message, &response.Timestamp)
	if scanErr != nil {
		ch <- &GetProductStreamResponse{
			Error: scanErr,
		}
		return
	}

	json.Unmarshal([]byte(response.Message), &response.Model)

	ch <- &response
}

func (d *ProductDb) GetProductStreamBySku(ch chan *GetProductStreamResponse, sku string) {
	if sku == "" {
		ch <- &GetProductStreamResponse{
			Error: errors.New("sku shouldn't be empty !"),
		}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetProductStreamResponse{
			Error: err,
		}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select product_id, sku, message, timestamp from product_stream_messages where sku = $1`

	row := connection.QueryRowContext(ctx, query, sku)
	if row.Err() != nil {
		ch <- &GetProductStreamResponse{
			Error: row.Err(),
		}
		return
	}

	response := GetProductStreamResponse{}

	scanErr := row.Scan(&response.ProductId, &response.Sku, &response.Message, &response.Timestamp)
	if scanErr != nil {
		ch <- &GetProductStreamResponse{
			Error: scanErr,
		}
		return
	}

	json.Unmarshal([]byte(response.Message), &response.Model)

	ch <- &response
}

func (d *ProductDb) GetProductStreamsBySku(ch chan *GetProductStreamsBySkuResponse, skus []string) {
	if len(skus) == 0 {
		ch <- &GetProductStreamsBySkuResponse{
			Error: errors.New("Skus array shouldn't be empty !"),
		}
		return
	}

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetProductStreamsBySkuResponse{
			Error: err,
		}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select product_id, sku, message, timestamp from product_stream_messages where sku = any($1)`

	rows, err := connection.QueryContext(ctx, query, pq.Array(skus))
	if err != nil {
		ch <- &GetProductStreamsBySkuResponse{
			Error: err,
		}
		return
	}

	response := GetProductStreamsBySkuResponse{}

	for rows.Next() {
		productMessage := GetProductStreamResponse{}
		scanErr := rows.Scan(&productMessage.ProductId, &productMessage.Sku, &productMessage.Message, &productMessage.Timestamp)
		if scanErr != nil {
			ch <- &GetProductStreamsBySkuResponse{
				Error: scanErr,
			}
			return
		}

		response.Messages = append(response.Messages, productMessage)
	}

	ch <- &response
}

func (d *ProductDb) GetAllProductsStream(ch chan *GetAllProductStreamResponse) {

	connection, err := sql.Open(d.driverName, d.connectionString)
	if err != nil {
		ch <- &GetAllProductStreamResponse{
			Error: err,
		}
		return
	}
	defer connection.Close()

	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	query := `select sku, name, price, list_price, stock_amount from product_stream_messages`

	rows, dbErr := connection.QueryContext(ctx, query)

	if dbErr != nil && dbErr != sql.ErrNoRows {
		ch <- &GetAllProductStreamResponse{Error: dbErr}
		return
	}
	if dbErr == sql.ErrNoRows {
		ch <- &GetAllProductStreamResponse{AllProductsStream: []Product{}}
		return
	}

	var response GetAllProductStreamResponse
	for rows.Next() {
		var product Product
		if err := rows.Scan(&product.Sku, &product.Name, &product.Price, &product.ListPrice,
			&product.StockAmount); err != nil {
			ch <- &GetAllProductStreamResponse{Error: dbErr}
			return
		}
		response.AllProductsStream = append(response.AllProductsStream, product)
	}

	ch <- &response
}
