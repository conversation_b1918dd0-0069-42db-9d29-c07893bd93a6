package product

import "time"

type UpsertProductStreamModel struct {
	ProductId   string    `validate:"required"`
	Sku         string    `validate:"required"`
	Timestamp   time.Time `validate:"required"`
	Message     string    `validate:"required"`
	Name        string    `validate:"required"`
	Price       float64   `validate:"gte=0"`
	ListPrice   float64   `validate:"gte=0"`
	StockAmount int       `validate:"gte=0"`
}

type GetProductStreamModel struct {
	ProductId string `validate:"required"`
	Timestamp time.Time
}
