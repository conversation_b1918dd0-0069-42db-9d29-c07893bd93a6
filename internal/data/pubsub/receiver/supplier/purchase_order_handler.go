package supplier

import (
	"errors"
	"logo-adapter/internal/data/pubsub/receiver"
	supplierSer "logo-adapter/internal/service/supplier"
	"strconv"
	"strings"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"

	"github.com/deliveryhero/dh-nv-proto-golang/packages/purchase_tool/v2/purchase_order"
)

type PurchaseOrderReceiverHandler struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	purchaseOrderService supplierSer.IPurchaseOrderService
	supplierService      supplierSer.ISupplierService
}

// NewPurchaseOrderReceiverHandler
// Returns a new PurchaseOrderReceiverHandler
func NewPurchaseOrderReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	purchaseOrderService supplierSer.IPurchaseOrderService,
	supplierService supplierSer.ISupplierService,
) receiver.IReceiverHandler {
	handler := PurchaseOrderReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if purchaseOrderService != nil {
		handler.purchaseOrderService = purchaseOrderService
	} else {
		handler.purchaseOrderService = supplierSer.NewPurchaseOrderService(environment, loggr, validatr, cachr, nil, nil)
	}

	if supplierService != nil {
		handler.supplierService = supplierService
	} else {
		handler.supplierService = supplierSer.NewSupplierService(environment, loggr, validatr, cachr, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *PurchaseOrderReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	// Check event type exists in attributes
	eventType, exists := model.Attributes["event_type"]
	if exists == false {
		eventErr := errors.New("event_type attribute not exists in message")
		ch <- eventErr
		return
	}

	purchaseOrderServiceCh := make(chan *supplierSer.PurchaseOrderServiceBaseResponse)
	defer close(purchaseOrderServiceCh)

	// Send Logo service for creating new purchase order
	if eventType == enum.PurchaseOrderCreatedEvent {
		var handlerModel purchase_order.PurchaseOrderCreation
		err := proto.Unmarshal(model.Data, &handlerModel)
		if err != nil {
			ch <- err
			return
		}

		err = h.validatr.ValidateStruct(handlerModel)
		if err != nil {
			ch <- err
			return
		}

		orderType := convertToWarehouseType(handlerModel.Data.WarehouseId)

		getPurchaseOrderCh := make(chan *supplierSer.GetPurchaseOrderFromDbServiceResponse)
		defer close(getPurchaseOrderCh)

		go h.purchaseOrderService.GetPurchaseOrderFromDb(getPurchaseOrderCh, &supplierSer.GetPurchaseOrderServiceModel{
			OrderId:   handlerModel.Data.OrderId,
			EventType: enum.PurchaseOrderCreatedEvent,
		})

		// If there is no error, this means we delivered before.
		getPurchaseOrderResponse := <-getPurchaseOrderCh
		if getPurchaseOrderResponse.Error == nil {
			ch <- nil
			return
		}

		supplierServiceCh := make(chan *supplierSer.GetSupplierDetailResponse)
		defer close(supplierServiceCh)
		supplierId, _ := strconv.ParseInt(handlerModel.Data.SupplierId, 10, 64)
		go h.supplierService.GetSuppliersWithDetails(supplierServiceCh, []int64{supplierId})

		supplierResponse := <-supplierServiceCh
		if supplierResponse.Error != nil {
			ch <- supplierResponse.Error
			return
		}
		if len(supplierResponse.Supplier) == 0 {
			ch <- errors.New("supplier detail could not be found")
			return
		}

		supplierDetail := supplierResponse.Supplier[0]
		go h.purchaseOrderService.SendGlobalPurchaseOrder(purchaseOrderServiceCh, convertToPurchaseOrderServiceModel(&handlerModel, &supplierDetail))

		response := <-purchaseOrderServiceCh
		if response.Error != nil {
			ch <- response.Error
			return
		}

		go h.purchaseOrderService.AddPurchaseOrderToDb(purchaseOrderServiceCh, &supplierSer.AddPurchaseOrderToDbServiceModel{
			OrderId:        handlerModel.Data.OrderId,
			OrderReference: handlerModel.Data.Reference,
			OrderType:      orderType,
			EventType:      enum.PurchaseOrderCreatedEvent,
		})

		purchaseOrderDbResponse := <-purchaseOrderServiceCh
		if purchaseOrderDbResponse.Error != nil {
			ch <- purchaseOrderDbResponse.Error
			return
		}
	}

	// Send Logo service for cancel purchase order
	if eventType == enum.PurchaseOrderCanceledEvent {
		var handlerModel purchase_order.PurchaseOrderCancellation
		err := proto.Unmarshal(model.Data, &handlerModel)
		if err != nil {
			ch <- err
			return
		}

		err = h.validatr.ValidateStruct(handlerModel)
		if err != nil {
			ch <- err
			return
		}

		//check if PO has already canceled
		getPurchaseOrderCanceledCh := make(chan *supplierSer.GetPurchaseOrderFromDbServiceResponse)
		defer close(getPurchaseOrderCanceledCh)

		go h.purchaseOrderService.GetPurchaseOrderFromDb(getPurchaseOrderCanceledCh, &supplierSer.GetPurchaseOrderServiceModel{
			OrderId:   handlerModel.Data.OrderId,
			EventType: enum.PurchaseOrderCanceledEvent,
		})

		// If there is no error, this means we delivered before.
		getPurchaseOrderCancelResponse := <-getPurchaseOrderCanceledCh
		if getPurchaseOrderCancelResponse.Error == nil {
			ch <- nil
			return
		}

		go h.purchaseOrderService.GlobalPurchaseOrderCancellation(purchaseOrderServiceCh, convertToCancelPurchaseOrderServiceModel(&handlerModel))

		response := <-purchaseOrderServiceCh
		if response.Error != nil {
			ch <- response.Error
			return
		}

		//get PO for warehouse type
		getPurchaseOrderCh := make(chan *supplierSer.GetPurchaseOrderFromDbServiceResponse)
		defer close(getPurchaseOrderCh)

		go h.purchaseOrderService.GetPurchaseOrderFromDb(getPurchaseOrderCh, &supplierSer.GetPurchaseOrderServiceModel{
			OrderId:   handlerModel.Data.OrderId,
			EventType: enum.PurchaseOrderCreatedEvent,
		})

		getPurchaseOrderResponse := <-getPurchaseOrderCh
		if getPurchaseOrderResponse.Error != nil {
			ch <- getPurchaseOrderResponse.Error
			return
		}

		go h.purchaseOrderService.AddPurchaseOrderToDb(purchaseOrderServiceCh, &supplierSer.AddPurchaseOrderToDbServiceModel{
			OrderId:        handlerModel.Data.OrderId,
			OrderReference: handlerModel.Data.Reference,
			OrderType:      getPurchaseOrderResponse.OrderType,
			EventType:      enum.PurchaseOrderCanceledEvent,
		})

		purchaseOrderDbResponse := <-purchaseOrderServiceCh
		if purchaseOrderDbResponse.Error != nil {
			ch <- purchaseOrderDbResponse.Error
			return
		}
	}

	ch <- nil
	return
}

// convertToPurchaseOrderServiceModel
// Convert handler model to service request model
func convertToPurchaseOrderServiceModel(model *purchase_order.PurchaseOrderCreation, supplier *supplierSer.SupplierDetail) *supplierSer.SendGlobalPurchaseOrderServiceModel {
	var poSlips []supplierSer.SendGlobalPurchaseOrderPoSlipServiceModel
	var productList []supplierSer.SendGlobalPurchaseOrderPoSlipProductServiceModel
	messageId := uuid.New()
	for _, productItem := range model.Data.Products {
		productList = append(productList, supplierSer.SendGlobalPurchaseOrderPoSlipProductServiceModel{
			SKU:      productItem.Sku,
			Quantity: int(productItem.RequestedQty),
			Price:    productItem.UnitGrossCostWithoutVat,
			VatRate:  float32(productItem.VatRate),
		})
	}

	poSlips = append(poSlips, supplierSer.SendGlobalPurchaseOrderPoSlipServiceModel{
		PODate:       helper.ConvertTimeByZone(model.Data.CreatedAt.AsTime(), "Europe/Istanbul"),
		POGuidId:     model.Data.OrderId,
		SupplierCode: supplier.SupplierCode,
		PayPlanCode:  supplier.SupplierPayPlanId,
		StoreId:      model.Data.WarehouseId,
		ProjectCode:  "MRK",
		ProductList:  productList,
		POReference:  model.Data.Reference,
		DeliveryDate: helper.ConvertTimeByZone(model.Data.ExpectedDeliveryAt.AsTime(), "Europe/Istanbul"),
	})

	result := &supplierSer.SendGlobalPurchaseOrderServiceModel{
		MessageId:       messageId.String(),
		TransactionDate: helper.ConvertTimeByZone(model.Data.CreatedAt.AsTime(), "Europe/Istanbul"),
		POSlips:         poSlips,
	}
	return result
}

// convertToCancelPurchaseOrderServiceModel
// Convert handler model to service request model
func convertToCancelPurchaseOrderServiceModel(model *purchase_order.PurchaseOrderCancellation) *supplierSer.GlobalPurchaseOrderCancellationServiceModel {
	messageId := uuid.New()
	return &supplierSer.GlobalPurchaseOrderCancellationServiceModel{
		MessageId:        messageId.String(),
		POGuidId:         model.Data.OrderId,
		POReference:      model.Data.Reference,
		CancellationDate: helper.ConvertTimeByZone(model.Timestamp.AsTime(), "Europe/Istanbul"),
	}
}

func convertToWarehouseType(warehouseId string) string {
	dcWarehouseIds := []string{
		"055ECF5A-B20E-EB11-80CA-0050569C1F78",
		"B588466C-E10E-EB11-80CA-0050569C1F78",
		"E13F7B05-B60E-EB11-80CA-0050569C1F78",
		"94B7238A-B30E-EB11-80CA-0050569C1F78",
	}

	if helper.Contains(dcWarehouseIds, strings.ToUpper(warehouseId)) {
		return enum.PurchaseOrderTypeDc
	}

	return enum.PurchaseOrderTypeDmart
}
