package supplier

import (
	"errors"
	"logo-adapter/internal/data/pubsub/receiver"
	supplierSer "logo-adapter/internal/service/supplier"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	productReturn "github.com/deliveryhero/dh-nv-proto-golang/packages/purchase_tool/product_return/v1"
	"google.golang.org/protobuf/proto"
)

type ProductReturnReceiverHandler struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	productReturnService supplierSer.IProductReturnService
}

// NewProductReturnReceiverHandler
// Returns a new ProductReturnReceiverHandler
func NewProductReturnReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.I<PERSON>acher,
	productReturnService supplierSer.IProductReturnService,
) receiver.IReceiverHandler {
	handler := ProductReturnReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if productReturnService != nil {
		handler.productReturnService = productReturnService
	} else {
		handler.productReturnService = supplierSer.NewProductReturnService(environment, loggr, validatr, cachr, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *ProductReturnReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	// Check content type exists in attributes
	contentType, exists := model.Attributes["content_type"]
	if !exists {
		eventErr := errors.New("content_type attribute not exists in message")
		ch <- eventErr
		return
	}

	productReturnServiceCh := make(chan *supplierSer.SendProductReturnServiceResponse)
	defer close(productReturnServiceCh)

	// Send product return service for product return (wastage)
	if contentType == enum.ProductReturnEvent {
		var handlerModel productReturn.ProductReturnCompleted
		err := proto.Unmarshal(model.Data, &handlerModel)
		if err != nil {
			ch <- err
			return
		}

		err = h.validatr.ValidateStruct(handlerModel)
		if err != nil {
			ch <- err
			return
		}

		var products []supplierSer.ProductReturnProductItem
		for _, p := range handlerModel.Data.Products {
			if int(p.ActualQuantity) < 1 {
				continue
			}

			products = append(products, supplierSer.ProductReturnProductItem{
				Sku:      p.Sku,
				Quantity: int(p.ActualQuantity),
			})
		}

		if len(products) > 0 {
			go h.productReturnService.SendProductReturn(productReturnServiceCh, &supplierSer.SendProductReturnServiceModel{
				ContentType:     enum.ProductReturnEvent,
				ReturnId:        handlerModel.Data.Id,
				ReturnReference: handlerModel.Data.Reference,
				WarehouseId:     handlerModel.Data.WarehouseId,
				ReturnReason:    handlerModel.Data.Reason.String(),
				CompletedAt:     handlerModel.Data.CompletedAt.AsTime(),
				ReturnProducts:  products,
			})

			response := <-productReturnServiceCh
			if response.Error != nil {
				ch <- response.Error
				return
			}
		}
	}

	// Send product return service for supplier return
	if contentType == enum.SupplierReturnEvent {
		var handlerModel productReturn.SupplierReturnCompleted
		err := proto.Unmarshal(model.Data, &handlerModel)
		if err != nil {
			ch <- err
			return
		}

		err = h.validatr.ValidateStruct(handlerModel)
		if err != nil {
			ch <- err
			return
		}

		var products []supplierSer.SupplierReturnProductItem
		for _, p := range handlerModel.Data.Products {
			if int(p.ActualQuantity) < 1 {
				continue
			}

			products = append(products, supplierSer.SupplierReturnProductItem{
				Sku:      p.Sku,
				Quantity: int(p.ActualQuantity),
			})
		}

		if len(products) > 0 {
			go h.productReturnService.SendSupplierReturn(productReturnServiceCh, &supplierSer.SendSupplierReturnServiceModel{
				ContentType:     enum.SupplierReturnEvent,
				CarrierType:     handlerModel.Carrier.String(),
				ReturnId:        handlerModel.Data.Id,
				ReturnReference: handlerModel.Data.Reference,
				SupplierId:      int(handlerModel.Data.SupplierId),
				ReturnReason:    handlerModel.Data.Reason.String(),
				WarehouseId:     handlerModel.Data.WarehouseId,
				ReturnProducts:  products,
				CompletedAt:     handlerModel.Timestamp.AsTime(),
			})

			response := <-productReturnServiceCh
			if response.Error != nil {
				ch <- response.Error
				return
			}
		}
	}

	ch <- nil
}
