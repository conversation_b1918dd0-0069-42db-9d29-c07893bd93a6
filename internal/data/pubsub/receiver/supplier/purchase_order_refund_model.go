package supplier

import "time"

type PurchaseOrderRefundReceiverHandlerModel struct {
	Version        int                                            `json:"version"`
	Metadata       PurchaseOrderRefundReceiverHandlerContentModel `json:"metadata"`
	Content        PurchaseOrderRefundReceiverHandlerContentModel `json:"content"`
	Timestamp      string                                         `json:"timestamp"`
	GlobalEntityId string                                         `json:"global_entity_id"`
}

type PurchaseOrderRefundReceiverHandlerReturnLineModel struct {
	Sku             string `json:"sku"`
	LineId          string `json:"line_id"`
	GlobalCatalogId string `json:"global_catalog_id"`
	Quantity        int    `json:"quantity"`
}

type PurchaseOrderRefundReceiverHandlerContentModel struct {
	GlobalEntityId          string                                              `json:"global_entity_id"`
	SupplierReturnId        string                                              `json:"supplier_return_id"`
	SupplierReferenceNumber string                                              `json:"supplier_reference_number"`
	PurchaseOrderId         string                                              `json:"purchase_order_id"`
	ReturnStatus            string                                              `json:"return_status"`
	ReturnReason            string                                              `json:"return_reason"`
	ReturnLineDetails       []PurchaseOrderRefundReceiverHandlerReturnLineModel `json:"return_line_details"`
	Carrier                 string                                              `json:"carrier"`
	SupplierId              string                                              `json:"supplier_id"`
	WarehouseId             string                                              `json:"warehouse_id"`
	CompletedAt             time.Time                                           `json:"completed_at"`
}

type PurchaseOrderRefundReceiverHandlerMetadataModel struct {
	IngestionTimestamp string        `json:"ingestion_timestamp"`
	Type               string        `json:"type"`
	Version            int           `json:"version"`
	Source             string        `json:"source"`
	Tracking           []interface{} `json:"tracking"`
	ImportId           string        `json:"import_id"`
	PiiRemovalDate     string        `json:"pii_removal_date"`
	ChangedFieldNames  []interface{} `json:"changed_field_names"`
}
