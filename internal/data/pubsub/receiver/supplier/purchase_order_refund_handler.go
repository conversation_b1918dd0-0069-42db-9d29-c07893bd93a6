package supplier

import (
	"encoding/json"
	"errors"
	"fmt"
	"logo-adapter/internal/data/pubsub/receiver"
	supplierSer "logo-adapter/internal/service/supplier"
	"strconv"

	uuid2 "github.com/google/uuid"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type PurchaseOrderRefundReceiverHandler struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	purchaseOrderService supplierSer.IPurchaseOrderService
	supplierService      supplierSer.ISupplierService
}

// NewPurchaseOrderRefundReceiverHandler
// Returns a new PurchaseOrderRefundReceiverHandler
func NewPurchaseOrderRefundReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	purchaseOrderService supplierSer.IPurchaseOrderService,
	supplierService supplierSer.ISupplierService,
) receiver.IReceiverHandler {
	handler := PurchaseOrderRefundReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if purchaseOrderService != nil {
		handler.purchaseOrderService = purchaseOrderService
	} else {
		handler.purchaseOrderService = supplierSer.NewPurchaseOrderService(environment, loggr, validatr, cachr, nil, nil)
	}

	if supplierService != nil {
		handler.supplierService = supplierService
	} else {
		handler.supplierService = supplierSer.NewSupplierService(environment, loggr, validatr, cachr, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *PurchaseOrderRefundReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel PurchaseOrderRefundReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	if handlerModel.Content.ReturnStatus != enum.PurchaseOrderReturnStatusDone {
		ch <- nil
		return
	}

	getPurchaseOrderRefundCh := make(chan *supplierSer.GetPurchaseOrderRefundServiceResponse)
	defer close(getPurchaseOrderRefundCh)
	go h.purchaseOrderService.GetPurchaseOrderRefundFromDb(getPurchaseOrderRefundCh, &supplierSer.GetPurchaseOrderRefundServiceModel{
		OrderReference: handlerModel.Content.SupplierReferenceNumber,
	})

	// If there is no error, this means it has been added before.
	purchaseOrderRefundFromDbResponse := <-getPurchaseOrderRefundCh
	if purchaseOrderRefundFromDbResponse.Error == nil {
		ch <- nil
		return
	}

	supplierServiceCh := make(chan *supplierSer.GetSupplierDetailResponse)
	defer close(supplierServiceCh)
	supplierId, _ := strconv.ParseInt(handlerModel.Content.SupplierId, 10, 64)
	go h.supplierService.GetSuppliersWithDetails(supplierServiceCh, []int64{supplierId})
	supplierServiceResponse := <-supplierServiceCh

	if supplierServiceResponse.Error != nil {
		ch <- supplierServiceResponse.Error
		return
	}
	if len(supplierServiceResponse.Supplier) == 0 {
		ch <- errors.New(fmt.Sprintf("supplierId: %v not found", supplierId))
		return
	}

	supplier := supplierServiceResponse.Supplier[0]

	returnReasonCh := make(chan *supplierSer.GetReturnReasonServiceResponse)
	defer close(returnReasonCh)
	go h.purchaseOrderService.GetReturnReason(returnReasonCh, &supplierSer.GetReturnReasonServiceModel{
		Reason: handlerModel.Content.ReturnReason,
	})

	returnReasonResponse := <-returnReasonCh
	if returnReasonResponse.Error != nil {
		ch <- returnReasonResponse.Error
		return
	}

	purchaseOrderServiceCh := make(chan *supplierSer.PurchaseOrderServiceBaseResponse)
	defer close(purchaseOrderServiceCh)
	go h.purchaseOrderService.SendStoreRefund(purchaseOrderServiceCh, convertToStoreRefundServiceModel(&handlerModel, &supplier, returnReasonResponse))

	purchaseOrderRefundResponse := <-purchaseOrderServiceCh
	if purchaseOrderRefundResponse.Error != nil {
		ch <- purchaseOrderRefundResponse.Error
		return
	}

	go h.purchaseOrderService.AddPurchaseOrderRefundToDb(purchaseOrderServiceCh, &supplierSer.AddPurchaseOrderRefundServiceModel{
		OrderId:        handlerModel.Content.PurchaseOrderId,
		OrderReference: handlerModel.Content.SupplierReferenceNumber,
	})

	addPurchaseOrderRefundToDbResponse := <-purchaseOrderServiceCh
	if addPurchaseOrderRefundToDbResponse.Error != nil {
		ch <- addPurchaseOrderRefundToDbResponse.Error
		return
	}

	ch <- nil
	return
}

// convertToStoreRefundServiceModel
// Convert store refund handler model to service model
func convertToStoreRefundServiceModel(
	model *PurchaseOrderRefundReceiverHandlerModel,
	supplier *supplierSer.SupplierDetail,
	returnReason *supplierSer.GetReturnReasonServiceResponse,
) *supplierSer.SendStoreRefundServiceModel {
	uuid := uuid2.New()
	var products []supplierSer.ProductServiceModel
	for _, p := range model.Content.ReturnLineDetails {
		products = append(products, supplierSer.ProductServiceModel{
			SKU:        p.Sku,
			Quantity:   p.Quantity,
			ReasonCode: returnReason.Code,
		})
	}

	response := &supplierSer.SendStoreRefundServiceModel{
		MessageId:       uuid.String(),
		TransactionDate: helper.ConvertTimeByZone(model.Content.CompletedAt, "Europe/Istanbul"),
		StoreId:         model.Content.WarehouseId,
		SlipType:        6,
		CarrierType:     model.Content.Carrier,
		SupplierCode:    supplier.SupplierCode,
		DispatchId:      model.Content.SupplierReferenceNumber,
		DispatchDate:    helper.ConvertTimeByZone(model.Content.CompletedAt, "Europe/Istanbul"),
		ProductList:     products,
	}
	return response
}
