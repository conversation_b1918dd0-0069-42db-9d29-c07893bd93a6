package sample

import (
	"logo-adapter/internal/data/pubsub/protobuf/samplepb"
	"logo-adapter/internal/data/pubsub/receiver"
	"logo-adapter/internal/service/sample"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"google.golang.org/protobuf/encoding/protojson"
)

type SampleProtoReceiverHandler struct {
	environment   env.IEnvironment
	loggr         logger.ILogger
	validatr      validator.IValidator
	cachr         cacher.ICacher
	sampleService sample.ISampleService
}

// NewSampleProtoReceiverHandler
// Returns a new SampleProtoReceiverHandler
func NewSampleProtoReceiverHandler(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, sampleService sample.ISampleService) receiver.IReceiverHandler {
	handler := SampleProtoReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if sampleService != nil {
		handler.sampleService = sampleService
	} else {
		handler.sampleService = sample.NewSampleService(environment, loggr, validatr, cachr, nil, nil, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *SampleProtoReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	handlerModel, err := h.mapProto(model.Data)
	if err != nil {
		ch <- err
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	sampleCh := make(chan *sample.UpdateSampleServiceResponse)
	defer close(sampleCh)
	go h.sampleService.UpdateSample(sampleCh, &sample.UpdateSampleServiceModel{
		SampleId:     1,
		SampleStatus: 2,
		ModifiedBy:   "lorem_ipsum",
	})

	response := <-sampleCh

	if response.Error != nil {
		ch <- response.Error
		return
	}

	ch <- nil
	return
}

func (h *SampleProtoReceiverHandler) mapProto(data []byte) (*SampleProtoReceiverHandlerModel, error) {
	var sample samplepb.Sample
	err := protojson.Unmarshal(data, &sample)
	if err != nil {
		return nil, err
	}

	return &SampleProtoReceiverHandlerModel{
		Id:        sample.Id,
		FirstName: sample.FirstName,
		LastName:  sample.LastName,
	}, nil
}
