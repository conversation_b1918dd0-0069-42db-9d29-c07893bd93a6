package dc

import "time"

// Dc Adapter Purchase Order Inbound Models

type DcAdapterPurchaseOrderInboundReceiverHandlerModel struct {
	OrderId         string                              `validate:"required"`
	Reference       string                              `validate:"required"`
	StoreId         string                              `validate:"required"`
	BillingId       string                              `validate:"required"`
	BillingDate     time.Time                           `validate:"required"`
	TransactionDate time.Time                           `validate:"required"`
	OrderDetail     []DcAdapterPurchaseOrderDetailModel `validate:"required"`
}

type DcAdapterPurchaseOrderDetailModel struct {
	Sku      string `validate:"required"`
	Quantity int    `validate:"required,gte=0"`
}
