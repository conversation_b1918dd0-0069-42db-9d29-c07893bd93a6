package dc

import (
	"encoding/json"
	"fmt"
	"logo-adapter/internal/data/pubsub/receiver"
	"logo-adapter/internal/service/supplier"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/google/uuid"
)

type DcPoInboundDcReceiverHandler struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	purchaseOrderService supplier.IPurchaseOrderService
}

// NewDcPoInboundDcReceiverHandler
// Returns a new DcPoInboundDcReceiverHandler
func NewDcPoInboundDcReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	purchaseOrderService supplier.IPurchaseOrderService,
) receiver.IReceiverHandler {
	handler := DcPoInboundDcReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if purchaseOrderService != nil {
		handler.purchaseOrderService = purchaseOrderService
	} else {
		handler.purchaseOrderService = supplier.NewPurchaseOrderService(environment, loggr, validatr, cachr, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *DcPoInboundDcReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)

	if err != nil {
		ch <- err
		return
	}

	var handlerModel DcAdapterPurchaseOrderInboundReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)

	if err != nil {
		ch <- err
		return
	}

	eventId := getEventId(model)
	event_type := model.Attributes["event_type"]

	if event_type == "" || event_type == "INBOUNDING_COMPLETED" {
		serviceChannel := make(chan *supplier.SendPurchaseOrderInboundServiceResponse)
		defer close(serviceChannel)

		serviceModel := supplier.SendPurchaseOrderInboundServiceModel{
			TransactionDate: handlerModel.TransactionDate,
			OrderId:         handlerModel.OrderId,
			OrderReference:  handlerModel.Reference,
			StoreId:         handlerModel.StoreId,
			BillingId:       handlerModel.BillingId,
			BillingDate:     handlerModel.BillingDate,
			MessageId:       eventId,
		}

		serviceModel.OrderDetail = make([]supplier.OrderDetail, len(handlerModel.OrderDetail))

		for i := 0; i < len(handlerModel.OrderDetail); i++ {
			serviceModel.OrderDetail[i].Sku = handlerModel.OrderDetail[i].Sku
			serviceModel.OrderDetail[i].Quantity = handlerModel.OrderDetail[i].Quantity
		}

		go h.purchaseOrderService.SendPurchaseOrderInbound(serviceChannel, &serviceModel, enum.PurchaseOrderTypeDc)

		serviceResponse := <-serviceChannel

		if serviceResponse.Error != nil {
			ch <- serviceResponse.Error
			return
		}

		ch <- nil

		return
	}

	if event_type == "PO_COMPLETED" {
		purchaseOrderServiceCh := make(chan *supplier.PurchaseOrderServiceBaseResponse)
		defer close(purchaseOrderServiceCh)

		serviceModel := supplier.GlobalPurchaseOrderCancellationServiceModel{
			MessageId:        eventId,
			POGuidId:         handlerModel.OrderId,
			POReference:      handlerModel.Reference,
			CancellationDate: handlerModel.TransactionDate,
		}

		go h.purchaseOrderService.GlobalPurchaseOrderCancellation(purchaseOrderServiceCh, &serviceModel)

		serviceResponse := <-purchaseOrderServiceCh

		if serviceResponse.Error != nil {
			ch <- serviceResponse.Error
			return
		}

		ch <- nil

		return
	}

	ch <- fmt.Errorf("unknown event_type: %s", event_type)

	return
}

func getEventId(model *receiver.ReceiverHandlerModel) string {
	eventId := model.Attributes["id"]

	if eventId == "" {
		eventId = uuid.NewString()
	}

	return eventId
}
