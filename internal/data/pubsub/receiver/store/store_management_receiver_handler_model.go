package store

import "time"

type StoreTransferReceiverHandlerModel struct {
	MessageId       string                                `json:"message_id" validate:"required"`
	TransactionDate time.Time                             `json:"transaction_date"`
	Data            StoreTransferReceiverHandlerDataModel `json:"data" validate:"required"`
}

type StoreTransferReceiverHandlerDataModel struct {
	TransferId             string         `json:"transfer_id" validate:"required"`
	TransferReference      string         `json:"reference"`
	SourceWarehouseId      string         `json:"src_warehouse_id" validate:"required"`
	DestinationWarehouseId string         `json:"dst_warehouse_id" validate:"required"`
	TransferDate           time.Time      `json:"created_at" validate:"required"`
	Products               []ProductModel `json:"products" validate:"required"`
	Shipping               ShippingModel  `json:"shipping" validate:"required"`
}

type ProductModel struct {
	Sku      string `json:"sku"`
	Quantity int    `json:"quantity" validate:"required,gte=0"`
}

type ShippingModel struct {
	DispatchId               string `json:"dispatch_id" validate:"required"`
	DriverName               string `json:"driver_name" validate:"required"`
	DriverIdentityCardNumber string `json:"driver_national_id" validate:"required"`
	DriverPlate              string `json:"vehicle_plate" validate:"required"`
}

// Purchase Order Inbound Models

type PurchaseOrderInboundReceiverHandlerModel struct {
	MessageId       string                                       `json:"message_id" validate:"required"`
	TransactionDate time.Time                                    `json:"transaction_date"`
	Data            PurchaseOrderInboundReceiverHandlerDataModel `json:"data" validate:"required"`
}

type PurchaseOrderInboundReceiverHandlerDataModel struct {
	OrderId                string                            `json:"order_id" validate:"required"`
	Reference              string                            `json:"reference"`
	DestinationWarehouseId string                            `json:"dst_warehouse_id" validate:"required"`
	CreatedAt              time.Time                         `json:"created_at" validate:"required"`
	Products               []ProductModel                    `json:"products" validate:"required"`
	Shipping               PurchaseOrderInboundShippingModel `json:"shipping" validate:"required"`
}

type PurchaseOrderInboundShippingModel struct {
	DispatchId   string    `json:"dispatch_id" validate:"required"`
	DispatchDate time.Time `json:"dispatch_date" validate:"required"`
}
