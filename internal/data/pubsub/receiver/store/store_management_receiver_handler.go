package store

import (
	"fmt"
	"logo-adapter/internal/data/pubsub/receiver"
	"logo-adapter/internal/service/supplier"
	"logo-adapter/internal/service/warehouse"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/deliveryhero/dh-nv-proto-golang/packages/store_transfer/logo_adapter/receiving/v2/receiving"
	"github.com/deliveryhero/dh-nv-proto-golang/packages/store_transfer/logo_adapter/transfer/v2/transfer"
)

type StoreManagementReceiverHandler struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	storeTransferService warehouse.IWarehouseTransferService
	purchaseOrderService supplier.IPurchaseOrderService
}

// NewStoreManagementReceiverHandler
// Returns a new StoreManagementReceiverHandler
func NewStoreManagementReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	storeTransferService warehouse.IWarehouseTransferService,
	purchaseOrderService supplier.IPurchaseOrderService,
) receiver.IReceiverHandler {
	handler := StoreManagementReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if storeTransferService != nil {
		handler.storeTransferService = storeTransferService
	} else {
		handler.storeTransferService = warehouse.NewWarehouseTransferService(environment, loggr, validatr, cachr, nil)
	}

	if purchaseOrderService != nil {
		handler.purchaseOrderService = purchaseOrderService
	} else {
		handler.purchaseOrderService = supplier.NewPurchaseOrderService(environment, loggr, validatr, cachr, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *StoreManagementReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {
	err := h.validatr.ValidateStruct(model)

	if err != nil {
		ch <- err
		return
	}

	producer := model.Attributes["producer"]
	if producer != "store-management" {
		ch <- fmt.Errorf("unknown producer: %s", producer)
		return
	}

	service := model.Attributes["service"]

	switch service {
	case "store-transfer":
		{
			h.handleStoreTransfer(ch, model)
		}
	case "receiving":
		{
			h.handlePurchaseOrderInbound(ch, model)
		}
	default:
		{
			ch <- fmt.Errorf("unknown service: %s", service)
			return
		}
	}
}

func (h *StoreManagementReceiverHandler) handlePurchaseOrderInbound(ch chan error, model *receiver.ReceiverHandlerModel) {
	eventType := model.Attributes["event_type"]

	if eventType == "INBOUNDING_COMPLETED" {
		handlerModel, err := h.mapToPurchaseOrderInboundHandlerModel(model.Data)

		if err != nil {
			ch <- err
			return
		}

		serviceChannel := make(chan *supplier.SendPurchaseOrderInboundServiceResponse)
		defer close(serviceChannel)

		serviceModel := supplier.SendPurchaseOrderInboundServiceModel{
			TransactionDate: helper.ConvertTimeByZone(handlerModel.TransactionDate, "Europe/Istanbul"),
			OrderId:         handlerModel.Data.OrderId,
			OrderReference:  handlerModel.Data.Reference,
			StoreId:         handlerModel.Data.DestinationWarehouseId,
			BillingId:       handlerModel.Data.Shipping.DispatchId,
			BillingDate:     helper.ConvertTimeByZone(handlerModel.Data.Shipping.DispatchDate, "Europe/Istanbul"),
			MessageId:       handlerModel.MessageId,
		}

		serviceModel.OrderDetail = make([]supplier.OrderDetail, len(handlerModel.Data.Products))

		for i := 0; i < len(handlerModel.Data.Products); i++ {
			serviceModel.OrderDetail[i].Sku = handlerModel.Data.Products[i].Sku
			serviceModel.OrderDetail[i].Quantity = handlerModel.Data.Products[i].Quantity
		}

		go h.purchaseOrderService.SendPurchaseOrderInbound(serviceChannel, &serviceModel, "")

		serviceResponse := <-serviceChannel

		if serviceResponse.Error != nil {
			ch <- serviceResponse.Error
			return
		}

		ch <- nil

		return
	}

	if eventType == "RECEIVING_COMPLETED" {
		//if requests come one after another, wait for the incoming ones to complete
		time.Sleep(10 * time.Second)

		var handlerModel receiving.ShippingNotification
		err := proto.Unmarshal(model.Data, &handlerModel)

		if err != nil {
			ch <- err
			return
		}

		purchaseOrderServiceCh := make(chan *supplier.PurchaseOrderServiceBaseResponse)
		defer close(purchaseOrderServiceCh)

		serviceModel := supplier.GlobalPurchaseOrderCancellationServiceModel{
			MessageId:        handlerModel.MessageId,
			POGuidId:         handlerModel.Data.OrderId,
			POReference:      handlerModel.Data.Reference,
			CancellationDate: helper.ConvertTimeByZone(handlerModel.TransactionDate.AsTime(), "Europe/Istanbul"),
		}

		go h.purchaseOrderService.GlobalPurchaseOrderCancellation(purchaseOrderServiceCh, &serviceModel)

		serviceResponse := <-purchaseOrderServiceCh

		if serviceResponse.Error != nil {
			ch <- serviceResponse.Error
			return
		}

		ch <- nil

		return
	}

	ch <- fmt.Errorf("unknown event_type: %s", eventType)

	return
}

func (h *StoreManagementReceiverHandler) handleStoreTransfer(ch chan error, model *receiver.ReceiverHandlerModel) {
	handlerModel, err := h.mapToStoreTransferHandlerModel(model.Data)

	if err != nil {
		ch <- err
		return
	}

	storeTransferCh := make(chan *warehouse.SendWarehouseTransferCollectionResponse)
	defer close(storeTransferCh)

	var storeTransferCollectionModel = warehouse.SendWarehouseTransferCollectionModel{
		MessageId:                handlerModel.MessageId,
		TransactionDate:          helper.ConvertTimeByZone(handlerModel.TransactionDate, "Europe/Istanbul"),
		TransferId:               handlerModel.Data.TransferReference,
		TransferReference:        handlerModel.Data.TransferId,
		TransferDate:             helper.ConvertTimeByZone(handlerModel.Data.TransferDate, "Europe/Istanbul"),
		DispatchId:               handlerModel.Data.Shipping.DispatchId,
		DriverName:               handlerModel.Data.Shipping.DriverName,
		DriverIdentityCardNumber: handlerModel.Data.Shipping.DriverIdentityCardNumber,
		DriverPlate:              handlerModel.Data.Shipping.DriverPlate,
	}

	storeTransferCollectionModel.OrderDetail = make([]warehouse.OrderDetailModel, len(handlerModel.Data.Products))
	for i := 0; i < len(handlerModel.Data.Products); i++ {
		storeTransferCollectionModel.OrderDetail[i].Quantity = handlerModel.Data.Products[i].Quantity
		storeTransferCollectionModel.OrderDetail[i].SKU = handlerModel.Data.Products[i].Sku
	}

	eventType := model.Attributes["event_type"]

	switch eventType {
	case "INBOUNDING_COMPLETED":
		{
			storeTransferCollectionModel.SourceStoreId = handlerModel.Data.DestinationWarehouseId
			storeTransferCollectionModel.SourceWarehouseType = enum.WarehouseTypeOnTheWay
			storeTransferCollectionModel.TargetStoreId = handlerModel.Data.DestinationWarehouseId
			storeTransferCollectionModel.TargetWarehouseType = enum.WarehouseTypePhysical

			go h.storeTransferService.SendStoreTransferInboundCollection(storeTransferCh, storeTransferCollectionModel)
		}
	case "OUTBOUNDING_COMPLETED":
		{
			storeTransferCollectionModel.SourceStoreId = handlerModel.Data.SourceWarehouseId
			storeTransferCollectionModel.SourceWarehouseType = enum.WarehouseTypePhysical
			storeTransferCollectionModel.TargetStoreId = handlerModel.Data.DestinationWarehouseId
			storeTransferCollectionModel.TargetWarehouseType = enum.WarehouseTypeOnTheWay

			go h.storeTransferService.SendStoreTransferOutboundCollection(storeTransferCh, storeTransferCollectionModel)
		}
	default:
		{
			ch <- fmt.Errorf("unknown event: %s", eventType)
			return
		}
	}

	response := <-storeTransferCh

	if response.Error != nil {
		ch <- response.Error
		return
	}

	ch <- nil
}

func (h *StoreManagementReceiverHandler) mapToPurchaseOrderInboundHandlerModel(data []byte) (*PurchaseOrderInboundReceiverHandlerModel, error) {
	var model receiving.ShippingNotification
	err := proto.Unmarshal(data, &model)

	if err != nil {
		return nil, err
	}

	products := make([]ProductModel, len(model.Data.Products))

	for i := 0; i < len(model.Data.Products); i++ {
		products[i].Sku = model.Data.Products[i].Sku
		products[i].Quantity = int(model.Data.Products[i].Quantity)
	}

	handlerModel := &PurchaseOrderInboundReceiverHandlerModel{
		MessageId:       model.MessageId,
		TransactionDate: model.TransactionDate.AsTime(),
		Data: PurchaseOrderInboundReceiverHandlerDataModel{
			OrderId:                model.Data.OrderId,
			Reference:              model.Data.Reference,
			DestinationWarehouseId: model.Data.DstWarehouseId,
			CreatedAt:              model.Data.CreatedAt.AsTime(),
			Shipping: PurchaseOrderInboundShippingModel{
				DispatchId:   model.Data.Shipping.DispatchId,
				DispatchDate: model.Data.Shipping.DispatchDate.AsTime(),
			},
			Products: products,
		},
	}

	h.loggr.Info("StoreManagementReceiverHandler - POI - "+model.Data.Reference,
		zap.Any("ProtoModel", model),
		zap.Any("HandlerModel", handlerModel),
		zap.String("ProtoTransactionDate", model.TransactionDate.AsTime().String()),
		zap.String("HandlerModelTransactionDate", handlerModel.TransactionDate.String()),
		zap.Any("IsProtoDateValid", model.TransactionDate.IsValid()),
		zap.Any("IsProtoDateZero", model.TransactionDate.AsTime().IsZero()),
		zap.Any("IsHandlerModelDateZero", handlerModel.TransactionDate.IsZero()),
	)

	return handlerModel, nil
}

func (h *StoreManagementReceiverHandler) mapToStoreTransferHandlerModel(data []byte) (*StoreTransferReceiverHandlerModel, error) {
	var model transfer.ShippingNotification
	err := proto.Unmarshal(data, &model)
	if err != nil {
		return nil, err
	}

	products := make([]ProductModel, len(model.Data.Products))

	for i := 0; i < len(model.Data.Products); i++ {
		products[i].Sku = model.Data.Products[i].Sku
		products[i].Quantity = int(model.Data.Products[i].Quantity)
	}

	handlerModel := &StoreTransferReceiverHandlerModel{
		MessageId:       model.MessageId,
		TransactionDate: model.TransactionDate.AsTime(),
		Data: StoreTransferReceiverHandlerDataModel{
			TransferId:             model.Data.TransferId,
			TransferReference:      model.Data.Reference,
			SourceWarehouseId:      model.Data.SrcWarehouseId,
			DestinationWarehouseId: model.Data.DstWarehouseId,
			TransferDate:           model.Data.CreatedAt.AsTime(),
			Shipping: ShippingModel{
				DispatchId:               model.Data.Shipping.DispatchId,
				DriverIdentityCardNumber: model.Data.Shipping.DriverNationalId,
				DriverName:               model.Data.Shipping.DriverName,
				DriverPlate:              model.Data.Shipping.VehiclePlate,
			},
			Products: products,
		},
	}

	h.loggr.Info("StoreManagementReceiverHandler - StoreTransfer - "+model.Data.Reference,
		zap.Any("ProtoModel", model),
		zap.Any("HandlerModel", handlerModel),
		zap.String("ProtoTransactionDate", model.TransactionDate.AsTime().String()),
		zap.String("HandlerModelTransactionDate", handlerModel.TransactionDate.String()),
		zap.Any("IsProtoDateValid", model.TransactionDate.IsValid()),
		zap.Any("IsProtoDateZero", model.TransactionDate.AsTime().IsZero()),
		zap.Any("IsHandlerModelDateZero", handlerModel.TransactionDate.IsZero()),
	)

	return handlerModel, nil
}
