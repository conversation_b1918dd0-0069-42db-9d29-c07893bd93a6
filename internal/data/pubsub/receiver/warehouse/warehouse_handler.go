package warehouse

import (
	"encoding/json"
	"logo-adapter/internal/data/pubsub/receiver"
	"logo-adapter/internal/service/warehouse"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
)

type WarehouseReceiverHandler struct {
	environment      env.IEnvironment
	loggr            logger.ILogger
	validatr         validator.IValidator
	cachr            cacher.ICacher
	warehouseService warehouse.IWarehouseService
}

// NewWarehouseReceiverHandler
// Returns a new WarehouseReceiverHandler
func NewWarehouseReceiverHandler(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	warehouseService warehouse.IWarehouseService,
) receiver.IReceiverHandler {
	handler := WarehouseReceiverHandler{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if warehouseService != nil {
		handler.warehouseService = warehouseService
	} else {
		handler.warehouseService = warehouse.NewWarehouseService(environment, loggr, validatr, cachr, nil, nil, nil, nil)
	}

	return &handler
}

// Handle
// Process events & messages and handle necessary logic/business actions.
func (h *WarehouseReceiverHandler) Handle(ch chan error, model *receiver.ReceiverHandlerModel) {

	err := h.validatr.ValidateStruct(model)
	if err != nil {
		ch <- err
		return
	}

	var handlerModel WarehouseReceiverHandlerModel
	err = json.Unmarshal(model.Data, &handlerModel)
	if err != nil {
		ch <- err
		return
	}

	if handlerModel.Message.Address.CountryCode != "TR" {
		ch <- nil
		return
	}

	err = h.validatr.ValidateStruct(handlerModel)
	if err != nil {
		ch <- err
		return
	}

	whServiceCh := make(chan *warehouse.AddOrUpdateWarehouseServiceResponse)
	defer close(whServiceCh)

	if len(handlerModel.Message.PlatformVendorIds) < 1 {
		h.loggr.Error("platform datafridge id does not exist!", zap.String("WarehouseId", handlerModel.MessageAttributes.GlobalWarehouseId.Value))
		ch <- nil
		return
	}

	go h.warehouseService.AddOrUpdateWarehouse(
		whServiceCh, &warehouse.AddOrUpdateWarehouseServiceModel{
			GlobalEntityId:   handlerModel.Message.PlatformVendorIds[0].GlobalEntityId,
			PlatformVendorId: handlerModel.Message.PlatformVendorIds[0].PlatformVendorId,
			WarehouseId:      handlerModel.MessageAttributes.GlobalWarehouseId.Value,
			WarehouseName:    handlerModel.Message.Name,
			Address:          handlerModel.Message.Address.AddressLineOne + " " + handlerModel.Message.Address.AddressLineTwo,
			City:             handlerModel.Message.Address.City,
			PayloadTimestamp: handlerModel.Message.Timestamp,
			IsActive:         handlerModel.Message.IsActive,
		},
	)

	whServiceResponse := <-whServiceCh

	if whServiceResponse.Error != nil {
		ch <- whServiceResponse.Error
		return
	}

	ch <- nil
	return
}
