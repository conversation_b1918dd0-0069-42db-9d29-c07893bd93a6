package warehouse

import "time"

type WarehouseReceiverHandlerModel struct {
	Message           WarehouseReceiverHandlerMessageModel           `validate:"required" json:"message"`
	MessageAttributes WarehouseReceiverHandlerMessageAttributesModel `validate:"required" json:"message_attributes"`
}

type WarehouseReceiverHandlerMessageModel struct {
	Address           WarehouseReceiverHandlerAddressModel             `validate:"required" json:"address"`
	IsActive          bool                                             `json:"is_active"`
	Name              string                                           `validate:"required" json:"name"`
	PlatformVendorIds []WarehouseReceiverHandlerPlatformVendorIdsModel `validate:"required" json:"platform_vendor_ids"`
	Timestamp         time.Time                                        `validate:"required" json:"timestamp"`
}

type WarehouseReceiverHandlerAddressModel struct {
	AddressLineOne string `validate:"required" json:"address_line_one"`
	AddressLineTwo string `json:"address_line_two"`
	City           string `validate:"required" json:"city"`
	CountryCode    string `validate:"required" json:"country_code"`
}

type WarehouseReceiverHandlerPlatformVendorIdsModel struct {
	GlobalEntityId   string `validate:"required" json:"global_entity_id"`
	PlatformVendorId string `validate:"required" json:"platform_vendor_id"`
}

type WarehouseReceiverHandlerMessageAttributesModel struct {
	EventId           WarehouseReceiverHandlerTypeModel `validate:"required" json:"event_id"`
	EventType         WarehouseReceiverHandlerTypeModel `validate:"required" json:"event_type"`
	GlobalWarehouseId WarehouseReceiverHandlerTypeModel `validate:"required" json:"global_warehouse_id"`
}

type WarehouseReceiverHandlerTypeModel struct {
	Type  string
	Value string
}
