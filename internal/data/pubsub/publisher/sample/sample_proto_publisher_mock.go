// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/pubsub/publisher/sample/sample_proto_publisher.go

// Package sample is a generated GoMock package.
package sample

import (
	publisher "logo-adapter/internal/data/pubsub/publisher"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISampleProtoPublisher is a mock of ISampleProtoPublisher interface.
type MockISampleProtoPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockISampleProtoPublisherMockRecorder
}

// MockISampleProtoPublisherMockRecorder is the mock recorder for MockISampleProtoPublisher.
type MockISampleProtoPublisherMockRecorder struct {
	mock *MockISampleProtoPublisher
}

// NewMockISampleProtoPublisher creates a new mock instance.
func NewMockISampleProtoPublisher(ctrl *gomock.Controller) *MockISampleProtoPublisher {
	mock := &MockISampleProtoPublisher{ctrl: ctrl}
	mock.recorder = &MockISampleProtoPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISampleProtoPublisher) EXPECT() *MockISampleProtoPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockISampleProtoPublisher) Publish(ch chan publisher.PublisherResponse, message *SampleProtoPublisherModel, attributeOverrides map[string]string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Publish", ch, message, attributeOverrides)
}

// Publish indicates an expected call of Publish.
func (mr *MockISampleProtoPublisherMockRecorder) Publish(ch, message, attributeOverrides interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockISampleProtoPublisher)(nil).Publish), ch, message, attributeOverrides)
}
