package sample

import (
	"context"
	"encoding/base64"
	"logo-adapter/internal/data/pubsub/publisher"
	"time"

	"cloud.google.com/go/pubsub"
	"go.uber.org/zap"
	"google.golang.org/api/option"
	"google.golang.org/protobuf/encoding/protojson"
	"logo-adapter/internal/data/pubsub/protobuf/samplepb"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type ISampleProtoPublisher interface {
	Publish(ch chan publisher.PublisherResponse, message *SampleProtoPublisherModel, attributeOverrides map[string]string)
}

type SampleProtoPublisher struct {
	environment       env.IEnvironment
	loggr             logger.ILogger
	validatr          validator.IValidator
	cachr             cacher.ICacher
	projectId         string
	topicId           string
	timeout           time.Duration
	defaultAttributes map[string]string
}

// NewSampleProtoPublisher
// Returns a new SampleProtoPublisher.
func NewSampleProtoPublisher(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) ISampleProtoPublisher {
	projectId := environment.Get(env.SampleProtoPublisherProjectId)
	topicId := environment.Get(env.SampleProtoPublisherTopicId)

	publisher := SampleProtoPublisher{
		environment: environment,
		loggr: loggr.With(
			zap.String("projectId", projectId),
			zap.String("topicId", topicId),
		),
		validatr:          validatr,
		cachr:             cachr,
		projectId:         projectId,
		topicId:           topicId,
		timeout:           time.Second * 5,
		defaultAttributes: map[string]string{publisher.DummyAttribute: "dummy_attribute_value"},
	}

	return &publisher
}

func (p *SampleProtoPublisher) Publish(ch chan publisher.PublisherResponse, model *SampleProtoPublisherModel, attributeOverrides map[string]string) {
	err := p.validatr.ValidateStruct(model)
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	saJson, err := base64.StdEncoding.DecodeString(p.environment.Get(env.SampleProtoPublisherSaJson))
	if err != nil {
		p.loggr.Panic("Panicked while decoding SampleProtoPublisherSaJson base64.")
	}

	ctx, cancel := context.WithTimeout(context.Background(), p.timeout)
	defer cancel()

	client, err := pubsub.NewClient(ctx, p.projectId, option.WithCredentialsJSON(saJson))
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}
	defer client.Close()

	attributes := p.overrideAttributes(attributeOverrides)

	bytes, err := p.mapProto(model)
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	topic := client.Topic(p.topicId)
	result := topic.Publish(ctx, &pubsub.Message{
		Data:       bytes,
		Attributes: attributes,
	})

	messageId, err := result.Get(ctx)
	if err != nil {
		p.loggr.Error(err.Error())
		ch <- publisher.PublisherResponse{Error: err}
		return
	}

	p.loggr.Info(messageId+" ID message is published successfully.", zap.String("messageId", messageId))
	ch <- publisher.PublisherResponse{MessageId: &messageId}
	return
}

func (p *SampleProtoPublisher) overrideAttributes(overrides map[string]string) map[string]string {
	attr := make(map[string]string)

	for key, value := range p.defaultAttributes {
		attr[key] = value
	}

	if overrides != nil {
		for key, value := range overrides {
			attr[key] = value
		}
	}

	return attr
}

func (p *SampleProtoPublisher) mapProto(model *SampleProtoPublisherModel) ([]byte, error) {
	bytes, err := protojson.Marshal(&samplepb.Sample{
		Id:        model.Id,
		FirstName: model.FirstName,
		LastName:  model.LastName,
	})
	if err != nil {
		return nil, err
	}

	return bytes, nil
}
