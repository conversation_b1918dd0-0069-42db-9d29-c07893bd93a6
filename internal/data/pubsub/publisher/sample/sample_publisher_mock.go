// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/pubsub/publisher/sample/sample_publisher.go

// Package sample is a generated GoMock package.
package sample

import (
	publisher "logo-adapter/internal/data/pubsub/publisher"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISamplePublisher is a mock of ISamplePublisher interface.
type MockISamplePublisher struct {
	ctrl     *gomock.Controller
	recorder *MockISamplePublisherMockRecorder
}

// MockISamplePublisherMockRecorder is the mock recorder for MockISamplePublisher.
type MockISamplePublisherMockRecorder struct {
	mock *MockISamplePublisher
}

// NewMockISamplePublisher creates a new mock instance.
func NewMockISamplePublisher(ctrl *gomock.Controller) *MockISamplePublisher {
	mock := &MockISamplePublisher{ctrl: ctrl}
	mock.recorder = &MockISamplePublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISamplePublisher) EXPECT() *MockISamplePublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockISamplePublisher) Publish(ch chan publisher.PublisherResponse, message *SamplePublisherModel, attributeOverrides map[string]string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Publish", ch, message, attributeOverrides)
}

// Publish indicates an expected call of Publish.
func (mr *MockISamplePublisherMockRecorder) Publish(ch, message, attributeOverrides interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockISamplePublisher)(nil).Publish), ch, message, attributeOverrides)
}
