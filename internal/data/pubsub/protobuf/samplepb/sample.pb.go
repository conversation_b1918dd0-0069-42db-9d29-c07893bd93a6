// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.19.1
// source: internal/data/pubsub/schema/sample.proto

package samplepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Sample struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FirstName string `protobuf:"bytes,2,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName  string `protobuf:"bytes,3,opt,name=lastName,proto3" json:"lastName,omitempty"`
}

func (x *Sample) Reset() {
	*x = Sample{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_data_pubsub_schema_sample_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Sample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sample) ProtoMessage() {}

func (x *Sample) ProtoReflect() protoreflect.Message {
	mi := &file_internal_data_pubsub_schema_sample_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sample.ProtoReflect.Descriptor instead.
func (*Sample) Descriptor() ([]byte, []int) {
	return file_internal_data_pubsub_schema_sample_proto_rawDescGZIP(), []int{0}
}

func (x *Sample) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Sample) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *Sample) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

var File_internal_data_pubsub_schema_sample_proto protoreflect.FileDescriptor

var file_internal_data_pubsub_schema_sample_proto_rawDesc = []byte{
	0x0a, 0x28, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x2f, 0x73, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x73, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x22, 0x52, 0x0a, 0x06, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x28, 0x5a, 0x26, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_data_pubsub_schema_sample_proto_rawDescOnce sync.Once
	file_internal_data_pubsub_schema_sample_proto_rawDescData = file_internal_data_pubsub_schema_sample_proto_rawDesc
)

func file_internal_data_pubsub_schema_sample_proto_rawDescGZIP() []byte {
	file_internal_data_pubsub_schema_sample_proto_rawDescOnce.Do(func() {
		file_internal_data_pubsub_schema_sample_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_data_pubsub_schema_sample_proto_rawDescData)
	})
	return file_internal_data_pubsub_schema_sample_proto_rawDescData
}

var file_internal_data_pubsub_schema_sample_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_internal_data_pubsub_schema_sample_proto_goTypes = []interface{}{
	(*Sample)(nil), // 0: sample.Sample
}
var file_internal_data_pubsub_schema_sample_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_internal_data_pubsub_schema_sample_proto_init() }
func file_internal_data_pubsub_schema_sample_proto_init() {
	if File_internal_data_pubsub_schema_sample_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_data_pubsub_schema_sample_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Sample); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_data_pubsub_schema_sample_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_data_pubsub_schema_sample_proto_goTypes,
		DependencyIndexes: file_internal_data_pubsub_schema_sample_proto_depIdxs,
		MessageInfos:      file_internal_data_pubsub_schema_sample_proto_msgTypes,
	}.Build()
	File_internal_data_pubsub_schema_sample_proto = out.File
	file_internal_data_pubsub_schema_sample_proto_rawDesc = nil
	file_internal_data_pubsub_schema_sample_proto_goTypes = nil
	file_internal_data_pubsub_schema_sample_proto_depIdxs = nil
}
