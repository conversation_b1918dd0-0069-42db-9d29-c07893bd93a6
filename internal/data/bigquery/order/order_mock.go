// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/data/bigquery/order/order.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIOrderBq is a mock of IOrderBq interface.
type MockIOrderBq struct {
	ctrl     *gomock.Controller
	recorder *MockIOrderBqMockRecorder
}

// MockIOrderBqMockRecorder is the mock recorder for MockIOrderBq.
type MockIOrderBqMockRecorder struct {
	mock *MockIOrderBq
}

// NewMockIOrderBq creates a new mock instance.
func NewMockIOrderBq(ctrl *gomock.Controller) *MockIOrderBq {
	mock := &MockIOrderBq{ctrl: ctrl}
	mock.recorder = &MockIOrderBqMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrderBq) EXPECT() *MockIOrderBqMockRecorder {
	return m.recorder
}

// GetPartialRefundOrders mocks base method.
func (m *MockIOrderBq) GetPartialRefundOrders(ch chan *GetPartialRefundOrdersResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPartialRefundOrders", ch)
}

// GetPartialRefundOrders indicates an expected call of GetPartialRefundOrders.
func (mr *MockIOrderBqMockRecorder) GetPartialRefundOrders(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartialRefundOrders", reflect.TypeOf((*MockIOrderBq)(nil).GetPartialRefundOrders), ch)
}
