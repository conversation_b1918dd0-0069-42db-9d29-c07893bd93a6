package order

import (
	"cloud.google.com/go/civil"
)

type GetPartialRefundOrdersResponse struct {
	Error               error `json:"-"`
	PartialRefundOrders []PartialRefundOrder
}

type PartialRefundOrder struct {
	OrderId          string         `bigquery:"order_id"`
	Sku              string         `bigquery:"sku"`
	ReturnedQuantity int            `bigquery:"qty_returned"`
	ReturnedAt       civil.DateTime `bigquery:"returned_at"`
}
