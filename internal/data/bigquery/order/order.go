package order

import (
	"context"
	"encoding/base64"
	"time"

	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"cloud.google.com/go/bigquery"
	_ "github.com/lib/pq"
	"go.uber.org/zap"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
)

type IOrderBq interface {
	GetPartialRefundOrders(ch chan *GetPartialRefundOrdersResponse)
}

type OrderBq struct {
	loggr       logger.ILogger
	validatr    validator.IValidator
	cachr       cacher.ICacher
	environment env.IEnvironment
	projectId   string
	saJson      []byte
	timeout     time.Duration
}

// NewOrderBq
// Returns a new OrderBq.
func NewOrderBq(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher) IOrderBq {
	saJson, err := base64.StdEncoding.DecodeString(environment.Get(env.DefaultSaJson))
	if err != nil {
		loggr.Panic("BigQuery client panicked while decoding DefaultSaJson base64.", zap.Error(err))
	}

	bq := OrderBq{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
		projectId:   environment.Get(env.BigQueryProjectId),
		saJson:      saJson,
		timeout:     time.Second * 10,
	}

	return &bq
}

func (d *OrderBq) GetPartialRefundOrders(ch chan *GetPartialRefundOrdersResponse) {
	ctx, cancel := context.WithTimeout(context.Background(), d.timeout)
	defer cancel()

	client, err := bigquery.NewClient(ctx, d.projectId, option.WithCredentialsJSON(d.saJson))
	if err != nil {
		d.loggr.Error("Error while creating bigquery client.", zap.Error(err))
		ch <- &GetPartialRefundOrdersResponse{Error: err}
		return
	}
	defer client.Close()

	q := client.Query(`
		with CustomerOrders
		AS(
		SELECT 
		DISTINCT
		CO.order_id
		,i.sku
		
		FROM fulfillment-dwh-production.curated_data_shared_dmart.customer_orders_v2 as CO
		,unnest(items)as i
		
		where warehouse.warehouse_name like 'Yemeksepeti Market%'
		and not(CO.is_cancelled)
		and CO.order_placed_localtime_at>=current_date('Turkey') - 22
		),
		ReturnItems 
		AS(
		SELECT 
		DISTINCT
		SD.Reference_id
		,movement_localtime_at
		,event_type
		,store_transfer_owner
		,SM.sku
		,SD.on_hand_qty_delta
		
		FROM fulfillment-dwh-production.curated_data_shared_dmart.stock_movement as SM,unnest(SM.stock_movement_data) as SD
		
		WHERE
		SM.warehouse.warehouse_name like 'Yemeksepeti Market%'
		and SD.stock_move_reason = 'returned'
		and SD.stock_move_source = 'customer order'
		and date(SM.date_localtime_at) = current_date('Turkey') - 1
		)
		SELECT 
		CO.*
		,RI.movement_localtime_at as returned_at
		,RI.on_hand_qty_delta as qty_returned
		
		FROM ReturnItems AS RI
		INNER JOIN CustomerOrders as CO 
		
		ON CO.order_id = RI.reference_id AND RI.sku = CO.sku
		ORDER BY RI.movement_localtime_at
	`)
	it, err := q.Read(ctx)
	if err != nil {
		ch <- &GetPartialRefundOrdersResponse{Error: err}
		return
	}

	var response GetPartialRefundOrdersResponse
	for {
		var row PartialRefundOrder
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			ch <- &GetPartialRefundOrdersResponse{Error: err}
			return
		}
		response.PartialRefundOrders = append(response.PartialRefundOrders, row)
	}

	ch <- &response
}
