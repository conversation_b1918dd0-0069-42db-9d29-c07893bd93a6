package configuration

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"logo-adapter/internal/data/database/configuration"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

type IServerConfigurationService interface {
	Get(key string) *string
	GetConfig(ch chan GetConfigServiceResponse, key string)
	IsReceiverOpen(receiverName string, configName string) bool
	AddConfig(ch chan GetConfigServiceResponse, model AddConfigServiceModel)
	UpdateConfig(ch chan GetConfigServiceResponse, model UpdateConfigServiceModel)
	UpsertConfig(ch chan GetConfigServiceResponse, model UpsertConfigServiceModel)
	GetLogoStockCountProcessTime(configName string) int
	GetCoffeeProductSKUs(configName string) []string
	GetCoffeeMaterialSKUs(configName string) []string
}

type ServerConfigurationService struct {
	loggr          logger.ILogger
	validatr       validator.IValidator
	cachr          cacher.ICacher
	environment    env.IEnvironment
	serverConfigDb configuration.IConfigurationDb
}

func NewServerConfigurationService(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, serverConfigDb configuration.IConfigurationDb) IServerConfigurationService {
	serverConfigurationService := &ServerConfigurationService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if serverConfigDb != nil {
		serverConfigurationService.serverConfigDb = serverConfigDb
	} else {
		serverConfigurationService.serverConfigDb = configuration.NewConfigurationDb(environment, loggr, validatr, cachr)
	}

	return serverConfigurationService
}

func (s ServerConfigurationService) GetConfig(ch chan GetConfigServiceResponse, key string) {
	if key == "" {
		ch <- GetConfigServiceResponse{
			Error: fmt.Errorf("key parameter shoudln't be empty"),
		}
		return
	}

	// if value in cache with given key
	configFromCache := s.cachr.Get(key)
	if configFromCache != nil {
		var response GetConfigServiceResponse

		unmarshalErr := json.Unmarshal([]byte(*configFromCache), &response)
		if unmarshalErr == nil {
			ch <- GetConfigServiceResponse{
				DataTypeId: response.DataTypeId,
				DataKey:    response.DataKey,
				DataValue:  response.DataValue,
				Id:         response.Id,
				Error:      nil,
			}
			return
		}
	}

	dbCh := make(chan *configuration.GetConfigurationResponse)
	defer close(dbCh)

	go s.serverConfigDb.GetConfig(dbCh, &configuration.GetConfigurationDbModel{
		DataKey: key,
	})

	serverConfigDbResponse := <-dbCh
	if serverConfigDbResponse.Error == sql.ErrNoRows {
		ch <- GetConfigServiceResponse{}
		return
	}
	if serverConfigDbResponse.Error != nil {
		ch <- GetConfigServiceResponse{
			Error: serverConfigDbResponse.Error,
		}
		return
	}

	configFromDb := GetConfigServiceResponse{
		DataTypeId: serverConfigDbResponse.DataTypeId,
		DataKey:    serverConfigDbResponse.DataKey,
		DataValue:  serverConfigDbResponse.DataValue,
		Id:         serverConfigDbResponse.Id,
		Error:      nil,
	}

	ch <- configFromDb
	if configFromCache == nil {
		s.cachr.Set(key, configFromDb, time.Minute*5)
	}
}

func (s ServerConfigurationService) IsReceiverOpen(receiverName string, configName string) bool {
	configCh := make(chan GetConfigServiceResponse)
	defer close(configCh)

	go s.GetConfig(configCh, configName)

	configResponse := <-configCh
	if configResponse.Error != nil {
		s.loggr.Error(receiverName+" Configuration service get config error",
			zap.Error(configResponse.Error), zap.Any("Config value:", configResponse))
		return false
	}

	isOpen, _ := strconv.ParseBool(configResponse.DataValue)
	return isOpen
}

func (s ServerConfigurationService) AddConfig(ch chan GetConfigServiceResponse, model AddConfigServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- GetConfigServiceResponse{
			Error: err,
		}
		return
	}

	configDbCh := make(chan *configuration.AddConfigResponse)
	defer close(configDbCh)

	go s.serverConfigDb.AddConfig(configDbCh, configuration.AddConfigDbModel{
		DataTypeId: model.DataTypeId,
		DataKey:    model.DataKey,
		DataValue:  model.DataValue,
	})

	dbResponse := <-configDbCh
	if dbResponse.Error != nil {
		ch <- GetConfigServiceResponse{
			Error: dbResponse.Error,
		}
		return
	}

	ch <- GetConfigServiceResponse{
		DataKey:   dbResponse.DataKey,
		DataValue: dbResponse.DataValue,
	}
}

func (s ServerConfigurationService) UpdateConfig(ch chan GetConfigServiceResponse, model UpdateConfigServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- GetConfigServiceResponse{
			Error: err,
		}
		return
	}

	configDbCh := make(chan *configuration.UpdateConfigResponse)
	defer close(configDbCh)

	go s.serverConfigDb.UpdateConfig(configDbCh, configuration.UpdateConfigDbModel{
		DataKey:   model.DataKey,
		DataValue: model.DataValue,
	})

	dbResponse := <-configDbCh
	if dbResponse.Error != nil {
		ch <- GetConfigServiceResponse{
			Error: dbResponse.Error,
		}
		return
	}

	ch <- GetConfigServiceResponse{
		DataKey:   dbResponse.DataKey,
		DataValue: dbResponse.DataValue,
	}
}

func (s ServerConfigurationService) UpsertConfig(ch chan GetConfigServiceResponse, model UpsertConfigServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- GetConfigServiceResponse{
			Error: err,
		}
		return
	}

	configDbCh := make(chan *configuration.UpsertConfigResponse)
	defer close(configDbCh)

	go s.serverConfigDb.UpsertConfig(configDbCh, configuration.UpsertConfigDbModel{
		DataKey:    model.DataKey,
		DataValue:  model.DataValue,
		DataTypeId: model.DataTypeId,
	})

	dbResponse := <-configDbCh
	if dbResponse.Error != nil {
		ch <- GetConfigServiceResponse{
			Error: dbResponse.Error,
		}
		return
	}

	ch <- GetConfigServiceResponse{
		DataKey:   dbResponse.DataKey,
		DataValue: dbResponse.DataValue,
	}
}

func (s ServerConfigurationService) Get(key string) *string {
	configCh := make(chan GetConfigServiceResponse)
	defer close(configCh)
	go s.GetConfig(configCh, key)
	configResponse := <-configCh

	if configResponse.Error != nil {
		return nil
	}

	if configResponse.DataValue == "" {
		return nil
	}

	return &configResponse.DataValue
}

func (s ServerConfigurationService) GetLogoStockCountProcessTime(configName string) int {
	configCh := make(chan GetConfigServiceResponse)
	defer close(configCh)

	go s.GetConfig(configCh, configName)

	configResponse := <-configCh
	if configResponse.Error != nil {
		s.loggr.Error("Configuration service get config error",
			zap.Error(configResponse.Error), zap.Any("Config value:", configResponse))
		return 0
	}

	value, _ := strconv.Atoi(configResponse.DataValue)
	return value
}

func (s ServerConfigurationService) GetCoffeeProductSKUs(configName string) []string {
	configCh := make(chan GetConfigServiceResponse)
	defer close(configCh)

	go s.GetConfig(configCh, configName)

	configResponse := <-configCh
	if configResponse.Error != nil {
		s.loggr.Error("Configuration service get config error",
			zap.Error(configResponse.Error), zap.Any("Config value:", configResponse))
		return []string{}
	}

	value := strings.Split(configResponse.DataValue, ",")
	return value
}

func (s ServerConfigurationService) GetCoffeeMaterialSKUs(configName string) []string {
	configCh := make(chan GetConfigServiceResponse)
	defer close(configCh)

	go s.GetConfig(configCh, configName)

	configResponse := <-configCh
	if configResponse.Error != nil {
		s.loggr.Error("Configuration service get config error",
			zap.Error(configResponse.Error), zap.Any("Config value:", configResponse))
		return []string{}
	}

	value := strings.Split(configResponse.DataValue, ",")
	return value
}
