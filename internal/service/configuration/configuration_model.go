package configuration

type AddConfigServiceModel struct {
	DataKey    string `validate:"required"`
	DataValue  string `validate:"required"`
	DataTypeId int
}

type UpdateConfigServiceModel struct {
	DataKey   string `validate:"required"`
	DataValue string `validate:"required"`
}

type UpsertConfigServiceModel struct {
	DataKey    string `validate:"required"`
	DataValue  string `validate:"required"`
	DataTypeId int
}
