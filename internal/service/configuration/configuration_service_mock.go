// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/configuration/configuration_service.go

// Package configuration is a generated GoMock package.
package configuration

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIServerConfigurationService is a mock of IServerConfigurationService interface.
type MockIServerConfigurationService struct {
	ctrl     *gomock.Controller
	recorder *MockIServerConfigurationServiceMockRecorder
}

// MockIServerConfigurationServiceMockRecorder is the mock recorder for MockIServerConfigurationService.
type MockIServerConfigurationServiceMockRecorder struct {
	mock *MockIServerConfigurationService
}

// NewMockIServerConfigurationService creates a new mock instance.
func NewMockIServerConfigurationService(ctrl *gomock.Controller) *MockIServerConfigurationService {
	mock := &MockIServerConfigurationService{ctrl: ctrl}
	mock.recorder = &MockIServerConfigurationServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIServerConfigurationService) EXPECT() *MockIServerConfigurationServiceMockRecorder {
	return m.recorder
}

// AddConfig mocks base method.
func (m *MockIServerConfigurationService) AddConfig(ch chan GetConfigServiceResponse, model AddConfigServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddConfig", ch, model)
}

// AddConfig indicates an expected call of AddConfig.
func (mr *MockIServerConfigurationServiceMockRecorder) AddConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddConfig", reflect.TypeOf((*MockIServerConfigurationService)(nil).AddConfig), ch, model)
}

// Get mocks base method.
func (m *MockIServerConfigurationService) Get(key string) *string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", key)
	ret0, _ := ret[0].(*string)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockIServerConfigurationServiceMockRecorder) Get(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockIServerConfigurationService)(nil).Get), key)
}

// GetCoffeeMaterialSKUs mocks base method.
func (m *MockIServerConfigurationService) GetCoffeeMaterialSKUs(configName string) []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoffeeMaterialSKUs", configName)
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetCoffeeMaterialSKUs indicates an expected call of GetCoffeeMaterialSKUs.
func (mr *MockIServerConfigurationServiceMockRecorder) GetCoffeeMaterialSKUs(configName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoffeeMaterialSKUs", reflect.TypeOf((*MockIServerConfigurationService)(nil).GetCoffeeMaterialSKUs), configName)
}

// GetCoffeeProductSKUs mocks base method.
func (m *MockIServerConfigurationService) GetCoffeeProductSKUs(configName string) []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoffeeProductSKUs", configName)
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetCoffeeProductSKUs indicates an expected call of GetCoffeeProductSKUs.
func (mr *MockIServerConfigurationServiceMockRecorder) GetCoffeeProductSKUs(configName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoffeeProductSKUs", reflect.TypeOf((*MockIServerConfigurationService)(nil).GetCoffeeProductSKUs), configName)
}

// GetConfig mocks base method.
func (m *MockIServerConfigurationService) GetConfig(ch chan GetConfigServiceResponse, key string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetConfig", ch, key)
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockIServerConfigurationServiceMockRecorder) GetConfig(ch, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockIServerConfigurationService)(nil).GetConfig), ch, key)
}

// GetLogoStockCountProcessTime mocks base method.
func (m *MockIServerConfigurationService) GetLogoStockCountProcessTime(configName string) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogoStockCountProcessTime", configName)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetLogoStockCountProcessTime indicates an expected call of GetLogoStockCountProcessTime.
func (mr *MockIServerConfigurationServiceMockRecorder) GetLogoStockCountProcessTime(configName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogoStockCountProcessTime", reflect.TypeOf((*MockIServerConfigurationService)(nil).GetLogoStockCountProcessTime), configName)
}

// IsReceiverOpen mocks base method.
func (m *MockIServerConfigurationService) IsReceiverOpen(receiverName, configName string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsReceiverOpen", receiverName, configName)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsReceiverOpen indicates an expected call of IsReceiverOpen.
func (mr *MockIServerConfigurationServiceMockRecorder) IsReceiverOpen(receiverName, configName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReceiverOpen", reflect.TypeOf((*MockIServerConfigurationService)(nil).IsReceiverOpen), receiverName, configName)
}

// UpdateConfig mocks base method.
func (m *MockIServerConfigurationService) UpdateConfig(ch chan GetConfigServiceResponse, model UpdateConfigServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateConfig", ch, model)
}

// UpdateConfig indicates an expected call of UpdateConfig.
func (mr *MockIServerConfigurationServiceMockRecorder) UpdateConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfig", reflect.TypeOf((*MockIServerConfigurationService)(nil).UpdateConfig), ch, model)
}

// UpsertConfig mocks base method.
func (m *MockIServerConfigurationService) UpsertConfig(ch chan GetConfigServiceResponse, model UpsertConfigServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpsertConfig", ch, model)
}

// UpsertConfig indicates an expected call of UpsertConfig.
func (mr *MockIServerConfigurationServiceMockRecorder) UpsertConfig(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertConfig", reflect.TypeOf((*MockIServerConfigurationService)(nil).UpsertConfig), ch, model)
}
