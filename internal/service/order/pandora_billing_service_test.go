package order

import (
	"errors"
	"testing"

	"logo-adapter/internal/data/database/order"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type PandoraBillingServiceTestSuit struct {
	suite.Suite
	pandoraBillingService   IPandoraBillingService
	mockEnvironment         *env.MockIEnvironment
	mockLogger              *logger.MockILogger
	mockValidator           *validator.MockIValidator
	mockCacher              *cacher.MockICacher
	mockLogoProxy           *logo.MockILogoProxy
	mockPandoraBillingDb    *order.MockIPandoraBillingDb
	mockServerConfigService *configuration.MockIServerConfigurationService
}

// Run suite.
func TestPandoraBillingService(t *testing.T) {
	suite.Run(t, new(PandoraBillingServiceTestSuit))
}

// Runs before each test in the suite.
func (p *PandoraBillingServiceTestSuit) SetupTest() {
	p.T().Log("Setup")

	ctrl := gomock.NewController(p.T())
	defer ctrl.Finish()

	p.mockEnvironment = env.NewMockIEnvironment(ctrl)
	p.mockLogger = logger.NewMockILogger(ctrl)
	p.mockValidator = validator.NewMockIValidator(ctrl)
	p.mockCacher = cacher.NewMockICacher(ctrl)
	p.mockPandoraBillingDb = order.NewMockIPandoraBillingDb(ctrl)
	p.mockLogoProxy = logo.NewMockILogoProxy(ctrl)

	p.pandoraBillingService = NewPandoraBillingService(p.mockEnvironment, p.mockLogger, p.mockValidator, p.mockCacher, p.mockPandoraBillingDb, p.mockServerConfigService)
}

// Runs after each test in the suite.
func (p *PandoraBillingServiceTestSuit) TearDownTest() {
	p.T().Log("Teardown")
}

func (p *PandoraBillingServiceTestSuit) TestSavePandoraBillingMessage_ServiceModelValidation_ReturnsError() {
	model := SavePandoraBillingMessageServiceModel{
		Message: PandoraBillingMessage{
			OrderId: "",
		},
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(errors.New("OrderId cannot be empty"))

	ch := make(chan *SavePandoraBillingMessageServiceResponse)
	defer close(ch)

	go p.pandoraBillingService.SavePandoraBillingMessage(ch, &model)
	response := <-ch

	// Then
	p.EqualError(response.Error, "OrderId cannot be empty")
}

func (p *PandoraBillingServiceTestSuit) TestSavePandoraBillingMessage_PandoraBillingDb_ReturnsError() {
	model := SavePandoraBillingMessageServiceModel{}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(nil)

	p.mockPandoraBillingDb.
		EXPECT().
		AddBillingMessage(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *order.AddPandoraBillingMessageResponse, model *order.AddPandoraBillingMessageModel) {
			ch <- &order.AddPandoraBillingMessageResponse{
				Error: errors.New("PandoraBillingDb error"),
			}
		})

	ch := make(chan *SavePandoraBillingMessageServiceResponse)
	defer close(ch)

	go p.pandoraBillingService.SavePandoraBillingMessage(ch, &model)
	response := <-ch

	// Then
	p.EqualError(response.Error, "PandoraBillingDb error")
}

func (p *PandoraBillingServiceTestSuit) TestGetPandoraBillingMessageExists_ServiceModelValidation_ReturnsError() {
	model := GetPandoraBillingMessageExistsServiceModel{
		OrderId: "",
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(errors.New("OrderId cannot be empty"))

	ch := make(chan *GetPandoraBillingMessageExistsServiceResponse)
	defer close(ch)

	go p.pandoraBillingService.GetPandoraBillingMessageExists(ch, &model)
	response := <-ch

	// Then
	p.EqualError(response.Error, "OrderId cannot be empty")
}

func (p *PandoraBillingServiceTestSuit) TestGetPandoraBillingMessageExists_PandoraBillingDb_ReturnsError() {
	model := GetPandoraBillingMessageExistsServiceModel{}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(nil)

	p.mockPandoraBillingDb.
		EXPECT().
		GetBillingMessageExists(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *order.GetPandoraBillingMessageExistsResponse, model *order.GetPandoraBillingMessageExists) {
			ch <- &order.GetPandoraBillingMessageExistsResponse{
				Error: errors.New("PandoraBillingDb error"),
			}
		})

	ch := make(chan *GetPandoraBillingMessageExistsServiceResponse)
	defer close(ch)

	go p.pandoraBillingService.GetPandoraBillingMessageExists(ch, &model)
	response := <-ch

	// Then
	p.EqualError(response.Error, "PandoraBillingDb error")
}

func (p *PandoraBillingServiceTestSuit) TestGetPandoraBillingMessage_ServiceModelValidation_ReturnsError() {
	model := GetPandoraBillingMessageServiceModel{
		OrderId: "",
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(errors.New("OrderId cannot be empty"))

	ch := make(chan *GetPandoraBillingMessageServiceResponse)
	defer close(ch)

	go p.pandoraBillingService.GetPandoraBillingMessage(ch, &model)
	response := <-ch

	// Then
	p.EqualError(response.Error, "OrderId cannot be empty")
}

func (p *PandoraBillingServiceTestSuit) TestGetPandoraBillingMessage_PandoraBillingDb_ReturnsError() {
	model := GetPandoraBillingMessageServiceModel{}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(nil)

	p.mockPandoraBillingDb.
		EXPECT().
		GetBillingMessage(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *order.GetPandoraBillingMessageResponse, model *order.GetPandoraBillingMessage) {
			ch <- &order.GetPandoraBillingMessageResponse{
				Error: errors.New("PandoraBillingDb error"),
			}
		})

	ch := make(chan *GetPandoraBillingMessageServiceResponse)
	defer close(ch)

	go p.pandoraBillingService.GetPandoraBillingMessage(ch, &model)
	response := <-ch

	// Then
	p.EqualError(response.Error, "PandoraBillingDb error")
}
