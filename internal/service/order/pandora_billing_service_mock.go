// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/order/pandora_billing_service.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIPandoraBillingService is a mock of IPandoraBillingService interface.
type MockIPandoraBillingService struct {
	ctrl     *gomock.Controller
	recorder *MockIPandoraBillingServiceMockRecorder
}

// MockIPandoraBillingServiceMockRecorder is the mock recorder for MockIPandoraBillingService.
type MockIPandoraBillingServiceMockRecorder struct {
	mock *MockIPandoraBillingService
}

// NewMockIPandoraBillingService creates a new mock instance.
func NewMockIPandoraBillingService(ctrl *gomock.Controller) *MockIPandoraBillingService {
	mock := &MockIPandoraBillingService{ctrl: ctrl}
	mock.recorder = &MockIPandoraBillingServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPandoraBillingService) EXPECT() *MockIPandoraBillingServiceMockRecorder {
	return m.recorder
}

// GetPandoraBillingMessage mocks base method.
func (m *MockIPandoraBillingService) GetPandoraBillingMessage(ch chan *GetPandoraBillingMessageServiceResponse, model *GetPandoraBillingMessageServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPandoraBillingMessage", ch, model)
}

// GetPandoraBillingMessage indicates an expected call of GetPandoraBillingMessage.
func (mr *MockIPandoraBillingServiceMockRecorder) GetPandoraBillingMessage(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPandoraBillingMessage", reflect.TypeOf((*MockIPandoraBillingService)(nil).GetPandoraBillingMessage), ch, model)
}

// GetPandoraBillingMessageExists mocks base method.
func (m *MockIPandoraBillingService) GetPandoraBillingMessageExists(ch chan *GetPandoraBillingMessageExistsServiceResponse, model *GetPandoraBillingMessageExistsServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPandoraBillingMessageExists", ch, model)
}

// GetPandoraBillingMessageExists indicates an expected call of GetPandoraBillingMessageExists.
func (mr *MockIPandoraBillingServiceMockRecorder) GetPandoraBillingMessageExists(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPandoraBillingMessageExists", reflect.TypeOf((*MockIPandoraBillingService)(nil).GetPandoraBillingMessageExists), ch, model)
}

// GetPandoraBillingSoftposMessageExists mocks base method.
func (m *MockIPandoraBillingService) GetPandoraBillingSoftposMessageExists(ch chan *GetPandoraBillingMessageExistsServiceResponse, model *GetPandoraBillingSoftposMessageExistsServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPandoraBillingSoftposMessageExists", ch, model)
}

// GetPandoraBillingSoftposMessageExists indicates an expected call of GetPandoraBillingSoftposMessageExists.
func (mr *MockIPandoraBillingServiceMockRecorder) GetPandoraBillingSoftposMessageExists(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPandoraBillingSoftposMessageExists", reflect.TypeOf((*MockIPandoraBillingService)(nil).GetPandoraBillingSoftposMessageExists), ch, model)
}

// SavePandoraBillingMessage mocks base method.
func (m *MockIPandoraBillingService) SavePandoraBillingMessage(ch chan *SavePandoraBillingMessageServiceResponse, model *SavePandoraBillingMessageServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SavePandoraBillingMessage", ch, model)
}

// SavePandoraBillingMessage indicates an expected call of SavePandoraBillingMessage.
func (mr *MockIPandoraBillingServiceMockRecorder) SavePandoraBillingMessage(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePandoraBillingMessage", reflect.TypeOf((*MockIPandoraBillingService)(nil).SavePandoraBillingMessage), ch, model)
}

// SavePandoraBillingSoftposMessage mocks base method.
func (m *MockIPandoraBillingService) SavePandoraBillingSoftposMessage(ch chan *SavePandoraBillingMessageServiceResponse, model *SavePandoraBillingSoftposMessageServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SavePandoraBillingSoftposMessage", ch, model)
}

// SavePandoraBillingSoftposMessage indicates an expected call of SavePandoraBillingSoftposMessage.
func (mr *MockIPandoraBillingServiceMockRecorder) SavePandoraBillingSoftposMessage(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePandoraBillingSoftposMessage", reflect.TypeOf((*MockIPandoraBillingService)(nil).SavePandoraBillingSoftposMessage), ch, model)
}
