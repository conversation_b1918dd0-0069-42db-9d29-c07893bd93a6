// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/order/order_service.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIOrderService is a mock of IOrderService interface.
type MockIOrderService struct {
	ctrl     *gomock.Controller
	recorder *MockIOrderServiceMockRecorder
}

// MockIOrderServiceMockRecorder is the mock recorder for MockIOrderService.
type MockIOrderServiceMockRecorder struct {
	mock *MockIOrderService
}

// NewMockIOrderService creates a new mock instance.
func NewMockIOrderService(ctrl *gomock.Controller) *MockIOrderService {
	mock := &MockIOrderService{ctrl: ctrl}
	mock.recorder = &MockIOrderServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrderService) EXPECT() *MockIOrderServiceMockRecorder {
	return m.recorder
}

// GetOrderFulfillment mocks base method.
func (m *MockIOrderService) GetOrderFulfillment(ch chan *GetOrderFulfillmentServiceResponse, model *GetOrderFulfillmentServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetOrderFulfillment", ch, model)
}

// GetOrderFulfillment indicates an expected call of GetOrderFulfillment.
func (mr *MockIOrderServiceMockRecorder) GetOrderFulfillment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderFulfillment", reflect.TypeOf((*MockIOrderService)(nil).GetOrderFulfillment), ch, model)
}

// GetPaymentMethodIds mocks base method.
func (m *MockIOrderService) GetPaymentMethodIds(method string) (int, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentMethodIds", method)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPaymentMethodIds indicates an expected call of GetPaymentMethodIds.
func (mr *MockIOrderServiceMockRecorder) GetPaymentMethodIds(method interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentMethodIds", reflect.TypeOf((*MockIOrderService)(nil).GetPaymentMethodIds), method)
}

// GetPaymentMethodMap mocks base method.
func (m *MockIOrderService) GetPaymentMethodMap(method string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentMethodMap", method)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentMethodMap indicates an expected call of GetPaymentMethodMap.
func (mr *MockIOrderServiceMockRecorder) GetPaymentMethodMap(method interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentMethodMap", reflect.TypeOf((*MockIOrderService)(nil).GetPaymentMethodMap), method)
}

// SendEttnToPandora mocks base method.
func (m *MockIOrderService) SendEttnToPandora(ch chan *SendEttnToPandoraResponse, model *SendEttnToPandoraModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendEttnToPandora", ch, model)
}

// SendEttnToPandora indicates an expected call of SendEttnToPandora.
func (mr *MockIOrderServiceMockRecorder) SendEttnToPandora(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEttnToPandora", reflect.TypeOf((*MockIOrderService)(nil).SendEttnToPandora), ch, model)
}

// SendOfflineYsOrderCancellationToLogistic mocks base method.
func (m *MockIOrderService) SendOfflineYsOrderCancellationToLogistic(ch chan *SendOfflineYsOrderCancellationToLogisticServiceResponse, model *SendOfflineYsOrderCancellationToLogisticServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOfflineYsOrderCancellationToLogistic", ch, model)
}

// SendOfflineYsOrderCancellationToLogistic indicates an expected call of SendOfflineYsOrderCancellationToLogistic.
func (mr *MockIOrderServiceMockRecorder) SendOfflineYsOrderCancellationToLogistic(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOfflineYsOrderCancellationToLogistic", reflect.TypeOf((*MockIOrderService)(nil).SendOfflineYsOrderCancellationToLogistic), ch, model)
}

// SendOfflineYsOrderToLogistic mocks base method.
func (m *MockIOrderService) SendOfflineYsOrderToLogistic(ch chan *SendOfflineYsOrderToLogisticServiceResponse, model *SendOfflineYsOrderToLogisticServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOfflineYsOrderToLogistic", ch, model)
}

// SendOfflineYsOrderToLogistic indicates an expected call of SendOfflineYsOrderToLogistic.
func (mr *MockIOrderServiceMockRecorder) SendOfflineYsOrderToLogistic(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOfflineYsOrderToLogistic", reflect.TypeOf((*MockIOrderService)(nil).SendOfflineYsOrderToLogistic), ch, model)
}

// SendOrder mocks base method.
func (m *MockIOrderService) SendOrder(ch chan *SendOrderServiceResponse, model *SendOrderServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrder", ch, model)
}

// SendOrder indicates an expected call of SendOrder.
func (mr *MockIOrderServiceMockRecorder) SendOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrder", reflect.TypeOf((*MockIOrderService)(nil).SendOrder), ch, model)
}

// SendOrderCancellation mocks base method.
func (m *MockIOrderService) SendOrderCancellation(ch chan *SendOrderCancellationServiceResponse, model *SendOrderCancellationServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderCancellation", ch, model)
}

// SendOrderCancellation indicates an expected call of SendOrderCancellation.
func (mr *MockIOrderServiceMockRecorder) SendOrderCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderCancellation", reflect.TypeOf((*MockIOrderService)(nil).SendOrderCancellation), ch, model)
}

// SendOrderFulfillment mocks base method.
func (m *MockIOrderService) SendOrderFulfillment(ch chan *SendOrderFulfillmentServiceResponse, model *SendOrderFulfillmentServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderFulfillment", ch, model)
}

// SendOrderFulfillment indicates an expected call of SendOrderFulfillment.
func (mr *MockIOrderServiceMockRecorder) SendOrderFulfillment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderFulfillment", reflect.TypeOf((*MockIOrderService)(nil).SendOrderFulfillment), ch, model)
}

// SendOrderFulfillmentCancellation mocks base method.
func (m *MockIOrderService) SendOrderFulfillmentCancellation(ch chan *SendOrderFulfillmentCancellationServiceResponse, model *SendOrderFulfillmentCancellationServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderFulfillmentCancellation", ch, model)
}

// SendOrderFulfillmentCancellation indicates an expected call of SendOrderFulfillmentCancellation.
func (mr *MockIOrderServiceMockRecorder) SendOrderFulfillmentCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderFulfillmentCancellation", reflect.TypeOf((*MockIOrderService)(nil).SendOrderFulfillmentCancellation), ch, model)
}

// SendOrderFulfillmentReconciliation mocks base method.
func (m *MockIOrderService) SendOrderFulfillmentReconciliation(ch chan *SendOrderFulfillmentReconciliationServiceResponse, model *SendOrderFulfillmentReconciliationServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderFulfillmentReconciliation", ch, model)
}

// SendOrderFulfillmentReconciliation indicates an expected call of SendOrderFulfillmentReconciliation.
func (mr *MockIOrderServiceMockRecorder) SendOrderFulfillmentReconciliation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderFulfillmentReconciliation", reflect.TypeOf((*MockIOrderService)(nil).SendOrderFulfillmentReconciliation), ch, model)
}

// SendPartialRefundOrders mocks base method.
func (m *MockIOrderService) SendPartialRefundOrders(ch chan *SendPartialRefundOrdersServiceResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendPartialRefundOrders", ch)
}

// SendPartialRefundOrders indicates an expected call of SendPartialRefundOrders.
func (mr *MockIOrderServiceMockRecorder) SendPartialRefundOrders(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPartialRefundOrders", reflect.TypeOf((*MockIOrderService)(nil).SendPartialRefundOrders), ch)
}
