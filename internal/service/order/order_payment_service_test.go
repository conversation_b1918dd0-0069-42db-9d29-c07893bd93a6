package order

import (
	"errors"
	"testing"
	"time"

	"logo-adapter/internal/data/database/order"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type OrderPaymentServiceTestSuite struct {
	suite.Suite
	orderPaymentService IOrderPaymentService
	mockEnvironment     *env.MockIEnvironment
	mockLogger          *logger.MockILogger
	mockValidator       *validator.MockIValidator
	mockCacher          *cacher.MockICacher
	mockLogoProxy       *logo.MockILogoProxy
	mockOrderDb         *order.MockIOrderDb
}

// Run suite.
func TestOrderPaymentService(t *testing.T) {
	suite.Run(t, new(OrderPaymentServiceTestSuite))
}

// Runs before each test in the suite.
func (o *OrderPaymentServiceTestSuite) SetupTest() {
	o.T().Log("Setup")

	ctrl := gomock.NewController(o.T())
	defer ctrl.Finish()

	o.mockEnvironment = env.NewMockIEnvironment(ctrl)
	o.mockLogger = logger.NewMockILogger(ctrl)
	o.mockValidator = validator.NewMockIValidator(ctrl)
	o.mockCacher = cacher.NewMockICacher(ctrl)
	o.mockOrderDb = order.NewMockIOrderDb(ctrl)
	o.mockLogoProxy = logo.NewMockILogoProxy(ctrl)

	o.orderPaymentService = NewOrderPaymentService(o.mockEnvironment, o.mockLogger, o.mockValidator, o.mockCacher, o.mockOrderDb, o.mockLogoProxy)
}

// Runs after each test in the suite.
func (o *OrderPaymentServiceTestSuite) TearDownTest() {
	o.T().Log("Teardown")
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderOfflinePayment_ModelHasValidationError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList:    []OfflinePaymentListServiceModel{},
		TrackingNumber: "ftrh-zxb3",
		TransactionId:  1,
		TargetAmount:   12.99,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(errors.New("validation error"))

	// When
	ch := make(chan *SendOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderOfflinePayment(ch, &SendOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderOfflinePayment_ProxyReturnedError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "1",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  1,
		TargetAmount:   12.99,
	}

	orderWithPaymentDetailResponse := &order.GetOrderWithPaymentDetailResponse{
		OrderPaymentId:       1,
		OrderId:              "1",
		PaymentTypeId:        2,
		PaymentMethodId:      2,
		Status:               2,
		PaymentOperationType: 1,
		Amount:               12.99,
		PaymentOperationDate: time.Now(),
		StoreId:              "test-warehouse",
		UserId:               "123",
		UserFriendlyId:       "123",
		UserFirstname:        "test-user",
		UserLastname:         "test-user",
		Error:                nil,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- orderWithPaymentDetailResponse
			return
		})

	o.mockLogoProxy.
		EXPECT().
		SendOrderOfflinePayment(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendOrderOfflinePaymentProxyModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: errors.New("proxy has returned an error")}
			return
		})

	// When
	ch := make(chan *SendOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderOfflinePayment(ch, &SendOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderOfflinePayment_OrderDbReturnedError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "1",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  1,
		TargetAmount:   12.99,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- &order.GetOrderWithPaymentDetailResponse{Error: errors.New("supplier db has returned an error")}
			return
		})

	// When
	ch := make(chan *SendOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderOfflinePayment(ch, &SendOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderOfflinePayment_OrderDbPaymentCompleteReturnedError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "12",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  100,
		TargetAmount:   12.99,
	}

	orderWithPaymentDetailResponse := &order.GetOrderWithPaymentDetailResponse{
		OrderPaymentId:       1,
		OrderId:              "1",
		PaymentTypeId:        2,
		PaymentMethodId:      2,
		Status:               2,
		PaymentOperationType: 1,
		Amount:               12.99,
		PaymentOperationDate: time.Now(),
		StoreId:              "test-warehouse",
		UserId:               "123",
		UserFriendlyId:       "123",
		UserFirstname:        "test-user",
		UserLastname:         "test-user",
		Error:                nil,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- orderWithPaymentDetailResponse
			return
		})

	o.mockLogoProxy.EXPECT().
		SendOrderOfflinePayment(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendOrderOfflinePaymentProxyModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil}
			return
		})

	orderDbPaymentCompleteModel := order.MarkOrderPaymentAsCompletedModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().MarkOrderPaymentAsCompleted(gomock.Any(), gomock.Eq(&orderDbPaymentCompleteModel)).
		DoAndReturn(func(ch chan *order.MarkOrderPaymentAsCompletedResponse, model *order.MarkOrderPaymentAsCompletedModel) {
			ch <- &order.MarkOrderPaymentAsCompletedResponse{Error: errors.New("supplier payment couldn't marked as completed")}
			return
		})

	// When
	ch := make(chan *SendOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderOfflinePayment(ch, &SendOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderOfflinePayment_HappyPath_Success() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "12",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  100,
		TargetAmount:   12.99,
	}

	orderWithPaymentDetailResponse := &order.GetOrderWithPaymentDetailResponse{
		OrderPaymentId:       1,
		OrderId:              "1",
		PaymentTypeId:        2,
		PaymentMethodId:      2,
		Status:               2,
		PaymentOperationType: 1,
		Amount:               12.99,
		PaymentOperationDate: time.Now(),
		StoreId:              "test-warehouse",
		UserId:               "123",
		UserFriendlyId:       "123",
		UserFirstname:        "test-user",
		UserLastname:         "test-user",
		Error:                nil,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- orderWithPaymentDetailResponse
			return
		})

	o.mockLogoProxy.EXPECT().
		SendOrderOfflinePayment(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendOrderOfflinePaymentProxyModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil}
			return
		})

	orderDbPaymentCompleteModel := order.MarkOrderPaymentAsCompletedModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().MarkOrderPaymentAsCompleted(gomock.Any(), gomock.Eq(&orderDbPaymentCompleteModel)).
		DoAndReturn(func(ch chan *order.MarkOrderPaymentAsCompletedResponse, model *order.MarkOrderPaymentAsCompletedModel) {
			ch <- &order.MarkOrderPaymentAsCompletedResponse{Error: nil}
			return
		})

	// When
	ch := make(chan *SendOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderOfflinePayment(ch, &SendOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Equal(response.Error, nil)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderRefundOfflinePayment_ModelHasValidationError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList:    []OfflinePaymentListServiceModel{},
		TrackingNumber: "ftrh-zxb3",
		TransactionId:  1,
		TargetAmount:   12.99,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendRefundOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(errors.New("validation error"))

	// When
	ch := make(chan *SendRefundOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderRefundOfflinePayment(ch, &SendRefundOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderRefundOfflinePayment_ProxyReturnedError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "1",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  1,
		TargetAmount:   12.99,
	}

	orderWithPaymentDetailResponse := &order.GetOrderWithPaymentDetailResponse{
		OrderPaymentId:       1,
		OrderId:              "1",
		PaymentTypeId:        2,
		PaymentMethodId:      2,
		Status:               2,
		PaymentOperationType: 1,
		Amount:               12.99,
		PaymentOperationDate: time.Now(),
		StoreId:              "test-warehouse",
		UserId:               "123",
		UserFriendlyId:       "123",
		UserFirstname:        "test-user",
		UserLastname:         "test-user",
		Error:                nil,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendRefundOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- orderWithPaymentDetailResponse
			return
		})

	o.mockLogoProxy.
		EXPECT().
		SendRefundOrderOfflinePayment(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendRefundOrderOfflinePaymentProxyModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: errors.New("proxy has returned an error")}
			return
		})

	// When
	ch := make(chan *SendRefundOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderRefundOfflinePayment(ch, &SendRefundOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderRefundOfflinePayment_OrderDbReturnedError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "1",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  1,
		TargetAmount:   12.99,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendRefundOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- &order.GetOrderWithPaymentDetailResponse{Error: errors.New("supplier db has returned an error")}
			return
		})

	// When
	ch := make(chan *SendRefundOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderRefundOfflinePayment(ch, &SendRefundOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderRefundOfflinePayment_OrderDbPaymentCompleteReturnedError_ReturnsError() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "12",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  100,
		TargetAmount:   12.99,
	}

	orderWithPaymentDetailResponse := &order.GetOrderWithPaymentDetailResponse{
		OrderPaymentId:       1,
		OrderId:              "1",
		PaymentTypeId:        2,
		PaymentMethodId:      2,
		Status:               2,
		PaymentOperationType: 1,
		Amount:               12.99,
		PaymentOperationDate: time.Now(),
		StoreId:              "test-warehouse",
		UserId:               "123",
		UserFriendlyId:       "123",
		UserFirstname:        "test-user",
		UserLastname:         "test-user",
		Error:                nil,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendRefundOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- orderWithPaymentDetailResponse
			return
		})

	o.mockLogoProxy.EXPECT().
		SendRefundOrderOfflinePayment(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendRefundOrderOfflinePaymentProxyModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil}
			return
		})

	orderDbPaymentCompleteModel := order.MarkOrderPaymentAsCompletedModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().MarkOrderPaymentAsCompleted(gomock.Any(), gomock.Eq(&orderDbPaymentCompleteModel)).
		DoAndReturn(func(ch chan *order.MarkOrderPaymentAsCompletedResponse, model *order.MarkOrderPaymentAsCompletedModel) {
			ch <- &order.MarkOrderPaymentAsCompletedResponse{Error: errors.New("supplier payment couldn't marked as completed")}
			return
		})

	// When
	ch := make(chan *SendRefundOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderRefundOfflinePayment(ch, &SendRefundOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Error(response.Error)
}

func (o *OrderPaymentServiceTestSuite) TestSendOrderRefundOfflinePayment_HappyPath_Success() {
	// Given
	offlinePayment := OrderOfflinePaymentServiceModel{
		PaymentList: []OfflinePaymentListServiceModel{
			{
				SlipNo:               "1",
				RelatedTransactionId: 1,
				Price:                12.99,
				TransactionDate:      time.Now(),
				PaymentMethod:        "Cash",
				CollectedBy:          "test",
				CollectedUserName:    "test",
				BankCode:             "12",
				BankAuthCode:         "1",
				BankStan:             "1",
				SlipTerminalNumber:   "1",
			},
		},
		TrackingNumber: "1",
		TransactionId:  100,
		TargetAmount:   12.99,
	}

	orderWithPaymentDetailResponse := &order.GetOrderWithPaymentDetailResponse{
		OrderPaymentId:       1,
		OrderId:              "1",
		PaymentTypeId:        2,
		PaymentMethodId:      2,
		Status:               2,
		PaymentOperationType: 1,
		Amount:               12.99,
		PaymentOperationDate: time.Now(),
		StoreId:              "test-warehouse",
		UserId:               "123",
		UserFriendlyId:       "123",
		UserFirstname:        "test-user",
		UserLastname:         "test-user",
		Error:                nil,
	}

	o.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&SendRefundOrderOfflinePaymentServiceModel{offlinePayment})).
		Return(nil)

	orderDbModel := order.GetOrderWithPaymentDetailModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().GetOrderWithPaymentDetail(gomock.Any(), gomock.Eq(&orderDbModel)).
		DoAndReturn(func(ch chan *order.GetOrderWithPaymentDetailResponse, model *order.GetOrderWithPaymentDetailModel) {
			ch <- orderWithPaymentDetailResponse
			return
		})

	o.mockLogoProxy.EXPECT().
		SendRefundOrderOfflinePayment(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendRefundOrderOfflinePaymentProxyModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil}
			return
		})

	orderDbPaymentCompleteModel := order.MarkOrderPaymentAsCompletedModel{
		TransactionId: offlinePayment.TransactionId,
		OrderId:       offlinePayment.TrackingNumber,
	}

	o.mockOrderDb.EXPECT().MarkOrderPaymentAsCompleted(gomock.Any(), gomock.Eq(&orderDbPaymentCompleteModel)).
		DoAndReturn(func(ch chan *order.MarkOrderPaymentAsCompletedResponse, model *order.MarkOrderPaymentAsCompletedModel) {
			ch <- &order.MarkOrderPaymentAsCompletedResponse{Error: nil}
			return
		})

	// When
	ch := make(chan *SendRefundOrderOfflinePaymentServiceResponse)
	defer close(ch)
	go o.orderPaymentService.SendOrderRefundOfflinePayment(ch, &SendRefundOrderOfflinePaymentServiceModel{
		offlinePayment,
	})
	response := <-ch

	// Then
	o.Equal(response.Error, nil)
}
