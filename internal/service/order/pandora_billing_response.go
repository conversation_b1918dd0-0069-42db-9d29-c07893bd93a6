package order

import (
	"logo-adapter/internal/data/proxy/logo"
)

type GetPandoraBillingMessageExistsServiceResponse struct {
	Error  error `json:"-"`
	Exists bool
}

type SavePandoraBillingMessageServiceResponse struct {
	Error error `json:"-"`
}

type GetPandoraBillingMessageServiceResponse struct {
	Error                 error `json:"-"`
	PandoraBillingMessage PandoraBillingMessage
	LogoOrderDetails      logo.SendCompletedOrderModel
}
