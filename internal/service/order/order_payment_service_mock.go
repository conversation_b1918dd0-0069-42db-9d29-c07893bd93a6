// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/order/order_payment_service.go

// Package order is a generated GoMock package.
package order

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIOrderPaymentService is a mock of IOrderPaymentService interface.
type MockIOrderPaymentService struct {
	ctrl     *gomock.Controller
	recorder *MockIOrderPaymentServiceMockRecorder
}

// MockIOrderPaymentServiceMockRecorder is the mock recorder for MockIOrderPaymentService.
type MockIOrderPaymentServiceMockRecorder struct {
	mock *MockIOrderPaymentService
}

// NewMockIOrderPaymentService creates a new mock instance.
func NewMockIOrderPaymentService(ctrl *gomock.Controller) *MockIOrderPaymentService {
	mock := &MockIOrderPaymentService{ctrl: ctrl}
	mock.recorder = &MockIOrderPaymentServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrderPaymentService) EXPECT() *MockIOrderPaymentServiceMockRecorder {
	return m.recorder
}

// SendOrderOfflinePayment mocks base method.
func (m *MockIOrderPaymentService) SendOrderOfflinePayment(ch chan *SendOrderOfflinePaymentServiceResponse, model *SendOrderOfflinePaymentServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderOfflinePayment", ch, model)
}

// SendOrderOfflinePayment indicates an expected call of SendOrderOfflinePayment.
func (mr *MockIOrderPaymentServiceMockRecorder) SendOrderOfflinePayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderOfflinePayment", reflect.TypeOf((*MockIOrderPaymentService)(nil).SendOrderOfflinePayment), ch, model)
}

// SendOrderRefundOfflinePayment mocks base method.
func (m *MockIOrderPaymentService) SendOrderRefundOfflinePayment(ch chan *SendRefundOrderOfflinePaymentServiceResponse, model *SendRefundOrderOfflinePaymentServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendOrderRefundOfflinePayment", ch, model)
}

// SendOrderRefundOfflinePayment indicates an expected call of SendOrderRefundOfflinePayment.
func (mr *MockIOrderPaymentServiceMockRecorder) SendOrderRefundOfflinePayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderRefundOfflinePayment", reflect.TypeOf((*MockIOrderPaymentService)(nil).SendOrderRefundOfflinePayment), ch, model)
}

// SendSoftposOrderOfflinePayment mocks base method.
func (m *MockIOrderPaymentService) SendSoftposOrderOfflinePayment(ch chan *SendSoftposOrderOfflinePaymentServiceResponse, model *SendOrderOfflinePaymentServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSoftposOrderOfflinePayment", ch, model)
}

// SendSoftposOrderOfflinePayment indicates an expected call of SendSoftposOrderOfflinePayment.
func (mr *MockIOrderPaymentServiceMockRecorder) SendSoftposOrderOfflinePayment(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSoftposOrderOfflinePayment", reflect.TypeOf((*MockIOrderPaymentService)(nil).SendSoftposOrderOfflinePayment), ch, model)
}
