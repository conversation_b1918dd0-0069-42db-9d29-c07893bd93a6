package order

type GetPandoraBillingMessageExistsServiceModel struct {
	OrderId string `validate:"required"`
	Status  string `validate:"required"`
}

type SavePandoraBillingMessageServiceModel struct {
	Message          PandoraBillingMessage `validate:"required"`
	LogoMessageId    string
	LogoOrderDetails string
}

type GetPandoraBillingMessageServiceModel struct {
	OrderId string `validate:"required"`
	Status  string `validate:"required"`
}

type GetPandoraBillingSoftposMessageExistsServiceModel struct {
	OrderId string `validate:"required"`
}

type SavePandoraBillingSoftposMessageServiceModel struct {
	Message          PandoraBillingSoftposMessage `validate:"required"`
	LogoMessageId    string
	LogoOrderDetails string
}
