package order

import (
	"errors"
	"logo-adapter/internal/data/database/order"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

type IOrderPaymentService interface {
	SendOrderOfflinePayment(ch chan *SendOrderOfflinePaymentServiceResponse, model *SendOrderOfflinePaymentServiceModel)
	SendOrderRefundOfflinePayment(ch chan *SendRefundOrderOfflinePaymentServiceResponse, model *SendRefundOrderOfflinePaymentServiceModel)
	SendSoftposOrderOfflinePayment(ch chan *SendSoftposOrderOfflinePaymentServiceResponse, model *SendOrderOfflinePaymentServiceModel)
}

type OrderPaymentService struct {
	environment env.IEnvironment
	loggr       logger.ILogger
	validatr    validator.IValidator
	cachr       cacher.ICacher
	OrderDb     order.IOrderDb
	LogoProxy   logo.ILogoProxy
}

// NewOrderPaymentService
// Returns a new OrderPaymentService.
func NewOrderPaymentService(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, OrderDb order.IOrderDb, LogoProxy logo.ILogoProxy) IOrderPaymentService {
	service := OrderPaymentService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
		OrderDb:     OrderDb,
		LogoProxy:   LogoProxy,
	}

	if OrderDb != nil {
		service.OrderDb = OrderDb
	} else {
		service.OrderDb = order.NewOrderDb(environment, loggr, validatr, cachr)
	}

	if LogoProxy != nil {
		service.LogoProxy = LogoProxy
	} else {
		service.LogoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	return &service
}

// SendOrderOfflinePayment
// Sends order to Logo.
func (s *OrderPaymentService) SendOrderOfflinePayment(ch chan *SendOrderOfflinePaymentServiceResponse, model *SendOrderOfflinePaymentServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOrderOfflinePaymentServiceResponse{Error: modelErr}
		return
	}

	orderWithPaymentDetailCh := make(chan *order.GetOrderWithPaymentDetailResponse)
	defer close(orderWithPaymentDetailCh)
	go s.OrderDb.GetOrderWithPaymentDetail(orderWithPaymentDetailCh, &order.GetOrderWithPaymentDetailModel{
		TransactionId: model.TransactionId,
		OrderId:       model.TrackingNumber,
	})

	orderWithPaymentDetailResponse := <-orderWithPaymentDetailCh
	if orderWithPaymentDetailResponse.Error != nil {
		ch <- &SendOrderOfflinePaymentServiceResponse{Error: orderWithPaymentDetailResponse.Error}
		return
	}

	if orderWithPaymentDetailResponse.OrderId == "" {
		ch <- &SendOrderOfflinePaymentServiceResponse{Error: errors.New("could not find orders payment detail. TrackingNumber: " + model.TrackingNumber)}
		return
	}

	if orderWithPaymentDetailResponse != nil && orderWithPaymentDetailResponse.Status == 3 {
		s.loggr.Error("could not mark order payment as completed", zap.String("TrackingNumber", model.TrackingNumber))
		ch <- &SendOrderOfflinePaymentServiceResponse{
			Error: nil,
		}
		return
	}

	offlinePaymentProxyModel, err := s.PrepareOfflinePaymentProxyModel(&model.OrderOfflinePaymentServiceModel, orderWithPaymentDetailResponse)

	if err != nil {
		ch <- &SendOrderOfflinePaymentServiceResponse{Error: err}
		return
	}

	sendOrderPaymentCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(sendOrderPaymentCh)
	go s.LogoProxy.SendOrderOfflinePayment(sendOrderPaymentCh, &logo.SendOrderOfflinePaymentProxyModel{
		OrderOfflinePaymentProxyModel: *offlinePaymentProxyModel,
		OrderDate:                     helper.ConvertTimeByZone(orderWithPaymentDetailResponse.PaymentOperationDate, "Europe/Istanbul"),
		OrderAmount:                   model.TargetAmount,
		CollectedAmount:               model.TargetAmount,
		OrderPaymentMethodName:        model.PaymentList[0].PaymentMethod,
	})

	sendOrderPaymentResponse := <-sendOrderPaymentCh
	if sendOrderPaymentResponse.Error != nil {
		ch <- &SendOrderOfflinePaymentServiceResponse{Error: sendOrderPaymentResponse.Error}
		return
	}

	markCh := make(chan *order.MarkOrderPaymentAsCompletedResponse)
	defer close(markCh)
	go s.OrderDb.MarkOrderPaymentAsCompleted(markCh, &order.MarkOrderPaymentAsCompletedModel{
		TransactionId: model.TransactionId,
		OrderId:       model.TrackingNumber,
	})

	markResponse := <-markCh
	if markResponse.Error != nil {
		ch <- &SendOrderOfflinePaymentServiceResponse{Error: markResponse.Error}
		return
	}

	ch <- &SendOrderOfflinePaymentServiceResponse{Error: nil}
	return
}

// SendOrderRefundOfflinePayment
// Sends order refund to Logo.
func (s *OrderPaymentService) SendOrderRefundOfflinePayment(ch chan *SendRefundOrderOfflinePaymentServiceResponse, model *SendRefundOrderOfflinePaymentServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendRefundOrderOfflinePaymentServiceResponse{Error: modelErr}
		return
	}

	orderWithPaymentDetailCh := make(chan *order.GetOrderWithPaymentDetailResponse)
	defer close(orderWithPaymentDetailCh)
	go s.OrderDb.GetOrderWithPaymentDetail(orderWithPaymentDetailCh, &order.GetOrderWithPaymentDetailModel{
		TransactionId: model.TransactionId,
		OrderId:       model.TrackingNumber,
	})

	orderWithPaymentDetailResponse := <-orderWithPaymentDetailCh
	if orderWithPaymentDetailResponse.Error != nil {
		ch <- &SendRefundOrderOfflinePaymentServiceResponse{Error: orderWithPaymentDetailResponse.Error}
		return
	}

	if orderWithPaymentDetailResponse.OrderId == "" {
		ch <- &SendRefundOrderOfflinePaymentServiceResponse{Error: errors.New("could not find orders payment detail. TrackingNumber: " + model.TrackingNumber)}
		return
	}

	if orderWithPaymentDetailResponse != nil && orderWithPaymentDetailResponse.Status == 3 {
		s.loggr.Error("could not mark order payment as completed", zap.String("TrackingNumber", model.TrackingNumber))
		ch <- &SendRefundOrderOfflinePaymentServiceResponse{
			Error: nil,
		}
		return
	}

	offlinePaymentProxyModel, err := s.PrepareOfflinePaymentProxyModel(&model.OrderOfflinePaymentServiceModel, orderWithPaymentDetailResponse)

	if err != nil {
		ch <- &SendRefundOrderOfflinePaymentServiceResponse{Error: err}
		return
	}

	sendRefundOrderPaymentCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(sendRefundOrderPaymentCh)
	go s.LogoProxy.SendRefundOrderOfflinePayment(sendRefundOrderPaymentCh, &logo.SendRefundOrderOfflinePaymentProxyModel{
		OrderOfflinePaymentProxyModel: *offlinePaymentProxyModel,
		RefundOrderDate:               helper.ConvertTimeByZone(orderWithPaymentDetailResponse.PaymentOperationDate, "Europe/Istanbul"),
		RefundOrderAmount:             model.TargetAmount,
		RefundCollectedAmount:         model.TargetAmount,
	})

	sendRefundOrderPaymentProxyResponse := <-sendRefundOrderPaymentCh
	if sendRefundOrderPaymentProxyResponse.Error != nil {
		ch <- &SendRefundOrderOfflinePaymentServiceResponse{Error: sendRefundOrderPaymentProxyResponse.Error}
		return
	}

	markCh := make(chan *order.MarkOrderPaymentAsCompletedResponse)
	defer close(markCh)
	go s.OrderDb.MarkOrderPaymentAsCompleted(markCh, &order.MarkOrderPaymentAsCompletedModel{
		TransactionId: model.TransactionId,
		OrderId:       model.TrackingNumber,
	})

	markResponse := <-markCh
	if markResponse.Error != nil {
		ch <- &SendRefundOrderOfflinePaymentServiceResponse{Error: markResponse.Error}
		return
	}

	ch <- &SendRefundOrderOfflinePaymentServiceResponse{Error: nil}
	return
}

// SendSoftposOrderOfflinePayment
// Sends softpos order payment to Logo.
func (s *OrderPaymentService) SendSoftposOrderOfflinePayment(ch chan *SendSoftposOrderOfflinePaymentServiceResponse, model *SendOrderOfflinePaymentServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendSoftposOrderOfflinePaymentServiceResponse{Error: modelErr}
		return
	}

	orderWithPaymentDetailCh := make(chan *order.GetOrderWithPaymentDetailResponse)
	defer close(orderWithPaymentDetailCh)
	go s.OrderDb.GetSoftposOrderWithPaymentDetail(orderWithPaymentDetailCh, &order.GetSoftposOrderWithPaymentDetailModel{
		OrderId:              model.TrackingNumber,
		PaymentOperationType: enum.PaymentOperationTypeOrder,
	})

	orderWithPaymentDetailResponse := <-orderWithPaymentDetailCh
	if orderWithPaymentDetailResponse.Error != nil {
		ch <- &SendSoftposOrderOfflinePaymentServiceResponse{Error: orderWithPaymentDetailResponse.Error}
		return
	}

	if orderWithPaymentDetailResponse.OrderId == "" {
		ch <- &SendSoftposOrderOfflinePaymentServiceResponse{Error: errors.New("could not find orders payment detail. TrackingNumber: " + model.TrackingNumber)}
		return
	}

	if orderWithPaymentDetailResponse != nil && orderWithPaymentDetailResponse.Status == 3 {
		s.loggr.Error("softpos order payment has already been completed", zap.String("TrackingNumber", model.TrackingNumber))
		ch <- &SendSoftposOrderOfflinePaymentServiceResponse{
			Error: nil,
		}
		return
	}

	offlinePaymentProxyModel, err := s.PrepareOfflinePaymentProxyModel(&model.OrderOfflinePaymentServiceModel, orderWithPaymentDetailResponse)

	if err != nil {
		ch <- &SendSoftposOrderOfflinePaymentServiceResponse{Error: err}
		return
	}

	sendOrderOfflinePaymentProxyModel := &logo.SendOrderOfflinePaymentProxyModel{
		OrderOfflinePaymentProxyModel: *offlinePaymentProxyModel,
		OrderDate:                     helper.ConvertTimeByZone(orderWithPaymentDetailResponse.PaymentOperationDate, "Europe/Istanbul"),
		OrderAmount:                   model.TargetAmount,
		CollectedAmount:               model.TargetAmount,
		OrderPaymentMethodName:        model.PaymentList[0].PaymentMethod,
	}

	sendOrderPaymentCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(sendOrderPaymentCh)
	go s.LogoProxy.SendOrderOfflinePayment(sendOrderPaymentCh, sendOrderOfflinePaymentProxyModel)

	sendOrderPaymentResponse := <-sendOrderPaymentCh
	if sendOrderPaymentResponse.Error != nil {
		ch <- &SendSoftposOrderOfflinePaymentServiceResponse{Error: sendOrderPaymentResponse.Error}
		return
	}

	markCh := make(chan *order.MarkOrderPaymentAsCompletedResponse)
	defer close(markCh)
	go s.OrderDb.MarkOrderPaymentAsCompleted(markCh, &order.MarkOrderPaymentAsCompletedModel{
		TransactionId: orderWithPaymentDetailResponse.OrderPaymentId,
		OrderId:       model.TrackingNumber,
	})

	markResponse := <-markCh
	if markResponse.Error != nil {
		ch <- &SendSoftposOrderOfflinePaymentServiceResponse{Error: markResponse.Error}
		return
	}

	ch <- &SendSoftposOrderOfflinePaymentServiceResponse{Error: nil, LogoMessageModel: sendOrderOfflinePaymentProxyModel}
	return
}

func (s *OrderPaymentService) PrepareOfflinePaymentProxyModel(model *OrderOfflinePaymentServiceModel, orderPaymentResponse *order.GetOrderWithPaymentDetailResponse) (*logo.OrderOfflinePaymentProxyModel, error) {

	if len(model.PaymentList) == 0 {
		return nil, errors.New("paymentlist is empty")
	}

	paymentList := make([]logo.OrderOfflinePaymentListProxyModel, len(model.PaymentList))
	for i, x := range model.PaymentList {
		paymentList[i] = logo.OrderOfflinePaymentListProxyModel{
			BankCode:             strings.TrimSpace(x.BankCode),
			Amount:               x.Price,
			SlipTerminalNumber:   x.SlipTerminalNumber,
			BankAuthCode:         x.BankAuthCode,
			BankStan:             x.BankStan,
			PaymentCollectedDate: x.TransactionDate,
			PaymentCollectedUser: x.CollectedBy,
			CourierName:          x.CollectedUserName,
		}
	}

	userData := logo.OrderOfflinePaymentUserDataProxyModel{
		UserId:         orderPaymentResponse.UserId,
		UserFriendlyId: orderPaymentResponse.UserFriendlyId,
		UserFirstName:  orderPaymentResponse.UserFirstname,
		UserLastName:   orderPaymentResponse.UserLastname,
	}

	payment := logo.OrderOfflinePaymentProxyModel{
		MessageId:         strconv.FormatInt(orderPaymentResponse.OrderPaymentId, 10),
		TransactionDate:   time.Now(),
		TrackingNumber:    model.TrackingNumber,
		StoreId:           orderPaymentResponse.StoreId,
		PaymentMethodName: model.PaymentList[0].PaymentMethod,
		UserData:          userData,
		PaymentList:       paymentList,
	}

	return &payment, nil
}
