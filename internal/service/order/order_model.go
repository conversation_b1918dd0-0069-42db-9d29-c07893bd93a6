package order

import (
	"time"
)

type SendOrderFulfillmentServiceModel struct {
	MessageId           string                        `validate:"required"`
	Discounts           map[string]float64            `validate:"required"`
	Fees                map[string]float64            `validate:"required"`
	Items               []SendOrderServiceItemModel   `validate:"required"`
	Customer            SendOrderServiceCustomerModel `validate:"required"`
	ExternalId          string                        `validate:"required"`
	WarehouseId         string                        `validate:"required"`
	OriginalTotalNet    float64                       `validate:"gte=0"`
	OriginalTotalGross  float64                       `validate:"gte=0"`
	ConfirmedTotalGross float64                       `validate:"gte=0"`
	OrderCreatedAt      time.Time                     `validate:"required"`
	OrderCheckedOutAt   time.Time                     `validate:"required"`
	Timestamp           time.Time                     `validate:"required"`
	YemekParams         *SendOrderServiceYemekParamsModel
	PaymentMethod       string
	RiderName           string
	IsModified          bool
}

type SendOrderServiceItemModel struct {
	ExternalId       string                                `validate:"required"`
	GlobalCatalogId  string                                `validate:"required"`
	OrderItemId      string                                `validate:"required"`
	SKU              string                                `validate:"required"`
	Name             string                                `validate:"required"`
	Status           string                                `validate:"required"`
	OriginalPricing  SendOrderServicePricingModel          `validate:"required"`
	CollectedPricing SendOrderServiceCollectedPricingModel `validate:"required" json:"pricing"`
}

type SendOrderServiceCollectedPricingModel struct {
	Type       string  `validate:"required"`
	UnitPrice  float64 `validate:"gt=0"`
	TotalPrice float64 `validate:"gt=0"`
	Quantity   int     `validate:"gt=0"`
}

type SendOrderServiceCustomerModel struct {
	Id        string `validate:"required"`
	Phone     string `validate:"required"`
	FirstName string
	LastName  string
}

type SendOrderServicePricingModel struct {
	Type          string  `validate:"required"`
	UnitPrice     float64 `validate:"gt=0"`
	TotalPrice    float64 `validate:"gt=0"`
	Quantity      int     `validate:"gt=0"`
	UnitListPrice float64 `validate:"gt=0"`
}

type SendOrderServiceYemekParamsModel struct {
	BankCode            string
	Email               string
	TipAmount           float64 `validate:"gte=0"`
	AddressLine         string
	RegionName          string
	CityName            string
	CollectFromCustomer float64 `validate:"gte=0"`
}

type SendPartialRefundOrderItemModel struct {
	Sku              string
	ReturnedQuantity int
	ReturnedAt       time.Time
}

type SendPostPandoraOrderServiceModel struct {
	OrderId string `validate:"required"`
}

type SendPostPandoraOrderCancellationServiceModel struct {
	OrderId string `validate:"required"`
}

type GetOrderFulfillmentServiceModel struct {
	OrderId string `validate:"required"`
}

type SendOrderServiceModel struct {
	Message   PandoraBillingMessage `validate:"required"`
	MessageId string                `validate:"required"`
}

type SendOrderCancellationServiceModel struct {
	Message   PandoraBillingMessage `validate:"required"`
	MessageId string                `validate:"required"`
}

type PandoraBillingMessage struct {
	OrderId              string               `json:"OrderID" validate:"required"`
	Status               string               `json:"Status" validate:"required"`
	Version              int                  `json:"Version" validate:"required"`
	PaymentMethodName    string               `json:"PaymentMethodName" validate:"required"`
	PaymentList          []PaymentInfo        `json:"PaymentList"`
	TotalLineItemsAmount float64              `json:"TotalLineItemsAmount" validate:"required"`
	DeliveryFee          float64              `json:"DeliveryFee"`
	TotalAmount          float64              `json:"TotalAmount"`
	TipAmount            float64              `json:"TipAmount"`
	CouponDiscountAmount float64              `json:"CouponDiscountAmount"`
	BasketDiscountAmount float64              `json:"BasketDiscountAmount"`
	DeliveryFeeCoupon    float64              `json:"DeliveryFeeCoupon"`
	OrderDetail          []PandoraOrderDetail `json:"OrderDetail"`
	OrderAddress         OrderAddress         `json:"OrderAddress"`
	OrderCreationDate    time.Time            `json:"OrderCreationDate"`
	PaidAt               time.Time            `json:"PaidAt"`
	UserData             UserData             `json:"UserData"`
	CancelledAt          time.Time            `json:"CancelledAt"`
	Vats                 []Vat                `json:"Vats"`
}

type PaymentInfo struct {
	BankCode string  `json:"BankCode" validate:"required"`
	Amount   float64 `json:"Amount" validate:"required"`
}

type PandoraOrderDetail struct {
	LineItemId     string  `json:"LineItemId" validate:"required"`
	Name           string  `json:"Name" validate:"required"`
	ProductId      string  `json:"ProductId" validate:"required"`
	Quantity       int     `json:"Quantity"`
	TotalListPrice float64 `json:"TotalListPrice"`
	TotalPrice     float64 `json:"TotalPrice"`
	UnitListPrice  float64 `json:"UnitListPrice"  validate:"required"`
}

type OrderAddress struct {
	FirstName       string `json:"FirstName" validate:"required"`
	LastName        string `json:"LastName" validate:"required"`
	CityName        string `json:"CityName"`
	RegionName      string `json:"RegionName"`
	AddressLine     string `json:"AddressLine"`
	TelephoneNumber string `json:"TelephoneNumber" validate:"required"`
	Email           string `json:"Email" validate:"required"`
}

type UserData struct {
	UserId        string `json:"UserId" validate:"required"`
	UserFirstName string `json:"UserFirstName"`
	UserLastName  string `json:"UserLastName"`
}

type SendEttnToPandoraModel struct {
	OrderId           string    `validate:"required" json:"orderId"`
	InvoiceNumber     string    `validate:"required" json:"invoiceNumber"`
	Ettn              string    `validate:"required" json:"ettn"`
	InvoiceDate       time.Time `validate:"required" json:"invoiceDate"`
	OrderCreationDate time.Time `validate:"required" json:"OrderCreationDate"`
	Order             EttnOrder `validate:"required" json:"order"`
}

type EttnOrder struct {
	Vats                 []Vat
	OrderDetail          []EttnOrderDetail
	DeliveryFee          EttnDeliveryFee
	TipAmount            EttnTipAmount
	TotalLineItemsAmount float64
	TotalAmount          float64
	VatAddedTotalAmount  float64
	TotalDiscountAmount  float64
	TaxBasis             float64
	TotalAmountInText    string
}

type EttnOrderDetail struct {
	TotalPrice     float64
	Name           string
	TotalListPrice float64
	UnitListPrice  float64
	Quantity       float64
	ProductId      string
	DiscountAmount float64
	Vat            float64
	LineItemId     int
}

type EttnDeliveryFee struct {
	Quantity       float64
	UnitListPrice  float64
	TotalListPrice float64
	DiscountAmount float64
	TotalPrice     float64
}

type EttnTipAmount struct {
	Quantity       float64
	UnitListPrice  float64
	TotalListPrice float64
	DiscountAmount float64
	TotalPrice     float64
}

type Vat struct {
	Rate   float64
	Amount float64
	Base   float64
}

type SendOrderFulfillmentCancellationServiceModel struct {
	OrderId          string                                                `validate:"required"`
	CancellationDate time.Time                                             `validate:"required"`
	TotalGrossAmount float64                                               `validate:"gte=0"`
	Customer         SendOrderFulfillmentCancellationServiceCustomerModel  `validate:"required"`
	PaymentMethod    string                                                `validate:"required"`
	Products         []SendOrderFulfillmentCancellationServiceProductModel `validate:"required"`
	WarehouseId      string                                                `validate:"required"`
}

type SendOrderFulfillmentCancellationServiceCustomerModel struct {
	Id        string `validate:"required"`
	FirstName string
	LastName  string
}

type SendOrderFulfillmentCancellationServiceProductModel struct {
	Id          string `validate:"required"`
	ExternalId  string `validate:"required"`
	OrderItemId string `validate:"required"`
	Sku         string `validate:"required"`
	Status      string `validate:"required"`
}

type SendOrderFulfillmentReconciliationServiceModel struct {
	OrderId             string  `validate:"required"`
	PaymentMethod       string  `validate:"required"`
	CollectFromCustomer float64 `validate:"gte=0"`
	WarehouseId         string  `validate:"required"`
}

type SendOfflineYsOrderToLogisticServiceModel struct {
	OrderType        string    `validate:"required"`
	OrderId          string    `validate:"required"`
	OrderCreatedDate time.Time `validate:"required"`
	VendorId         string    `validate:"required"`
	VendorType       string    `validate:"required"`
	StoreName        string    `validate:"required"`
	CustomerId       string    `validate:"required"`
	FirstName        string    `validate:"required"`
	LastName         string    `validate:"required"`
	PaymentMethod    string    `validate:"required"`
	Amount           float64   `validate:"gte=0"`
}

type SendOfflineYsOrderCancellationToLogisticServiceModel struct {
	OrderId               string    `validate:"required"`
	OrderCancellationDate time.Time `validate:"required"`
}

type PandoraBillingSoftposMessage struct {
	TrackingNumber string                    `json:"TrackingNumber" validate:"required"`
	TransactionId  string                    `json:"TransactionId"`
	TargetAmount   float64                   `json:"TargetAmount" validate:"gt=0"`
	PaymentList    []SoftposPaymentListModel `json:"PaymentList" validate:"required"`
}

type SoftposPaymentListModel struct {
	SlipNo               string    `json:"SlipNo" validate:"required"`
	RelatedTransactionId string    `json:"RelatedTransactionId"`
	Price                float64   `json:"Price" validate:"gte=0"`
	TransactionDate      time.Time `json:"TransactionDate"`
	PaymentMethod        string    `json:"PaymentMethod" validate:"required"`
	CollectedBy          string    `json:"CollectedBy" validate:"required"`
	CollectedUserName    string    `json:"CollectedUserName" validate:"required"`
	BankCode             string    `json:"BankCode" validate:"required"`
	BankAuthCode         string    `json:"BankAuthCode" validate:"required"`
	BankStan             string    `json:"BankStan" validate:"required"`
	SlipTerminalNumber   string    `json:"SlipTerminalNumber" validate:"required"`
}

func (e EttnOrder) GetOrderDetailBySku(sku string) *EttnOrderDetail {
	if len(e.OrderDetail) == 0 {
		return nil
	}

	for _, detail := range e.OrderDetail {
		if detail.ProductId == sku {
			return &detail
		}
	}

	return nil
}

func (s SendOrderFulfillmentServiceModel) GetItemBySku(sku string) *SendOrderServiceItemModel {
	if sku == "" {
		return nil
	}

	for _, item := range s.Items {
		if item.SKU == sku {
			return &item
		}
	}

	return nil
}
