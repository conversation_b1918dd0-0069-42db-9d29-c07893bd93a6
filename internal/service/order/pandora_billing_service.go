package order

import (
	"encoding/json"
	"logo-adapter/internal/data/database/order"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"time"
)

type IPandoraBillingService interface {
	GetPandoraBillingMessage(ch chan *GetPandoraBillingMessageServiceResponse, model *GetPandoraBillingMessageServiceModel)
	GetPandoraBillingMessageExists(ch chan *GetPandoraBillingMessageExistsServiceResponse, model *GetPandoraBillingMessageExistsServiceModel)
	SavePandoraBillingMessage(ch chan *SavePandoraBillingMessageServiceResponse, model *SavePandoraBillingMessageServiceModel)
	GetPandoraBillingSoftposMessageExists(ch chan *GetPandoraBillingMessageExistsServiceResponse, model *GetPandoraBillingSoftposMessageExistsServiceModel)
	SavePandoraBillingSoftposMessage(ch chan *SavePandoraBillingMessageServiceResponse, model *SavePandoraBillingSoftposMessageServiceModel)
}

type PandoraBillingService struct {
	environment         env.IEnvironment
	loggr               logger.ILogger
	validatr            validator.IValidator
	cachr               cacher.ICacher
	pandoraBillingDb    order.IPandoraBillingDb
	serverConfigService configuration.IServerConfigurationService
}

func NewPandoraBillingService(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	pandoraBillingDb order.IPandoraBillingDb,
	serverConfigService configuration.IServerConfigurationService,
) IPandoraBillingService {
	pdBillingService := PandoraBillingService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if pandoraBillingDb != nil {
		pdBillingService.pandoraBillingDb = pandoraBillingDb
	} else {
		pdBillingService.pandoraBillingDb = order.NewPandoraBillingDb(environment, loggr, validatr, cachr)
	}

	if serverConfigService != nil {
		pdBillingService.serverConfigService = serverConfigService
	} else {
		pdBillingService.serverConfigService = configuration.NewServerConfigurationService(environment, loggr, validatr, cachr, nil)
	}

	return &pdBillingService
}

func (s *PandoraBillingService) SavePandoraBillingMessage(ch chan *SavePandoraBillingMessageServiceResponse, model *SavePandoraBillingMessageServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SavePandoraBillingMessageServiceResponse{Error: modelErr}
		return
	}

	addPdBillingMessageCh := make(chan *order.AddPandoraBillingMessageResponse)
	defer close(addPdBillingMessageCh)

	orderDetails, _ := json.Marshal(model.Message)
	go s.pandoraBillingDb.AddBillingMessage(addPdBillingMessageCh, &order.AddPandoraBillingMessageModel{
		OrderId:          model.Message.OrderId,
		OrderDetails:     string(orderDetails),
		Version:          model.Message.Version,
		Status:           model.Message.Status,
		LogoMessageId:    model.LogoMessageId,
		LogoOrderDetails: model.LogoOrderDetails,
		Timestamp:        time.Now(),
	})

	addPdBillingMessageResponse := <-addPdBillingMessageCh
	if addPdBillingMessageResponse.Error != nil {
		ch <- &SavePandoraBillingMessageServiceResponse{
			Error: addPdBillingMessageResponse.Error,
		}
		return
	}

	ch <- &SavePandoraBillingMessageServiceResponse{
		Error: nil,
	}
}

func (s *PandoraBillingService) GetPandoraBillingMessageExists(ch chan *GetPandoraBillingMessageExistsServiceResponse, model *GetPandoraBillingMessageExistsServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPandoraBillingMessageExistsServiceResponse{Error: modelErr}
		return
	}

	pdBillingMessageExistsCh := make(chan *order.GetPandoraBillingMessageExistsResponse)
	defer close(pdBillingMessageExistsCh)

	go s.pandoraBillingDb.GetBillingMessageExists(pdBillingMessageExistsCh, &order.GetPandoraBillingMessageExists{
		OrderId: model.OrderId,
		Status:  model.Status,
	})

	pdBillingMessageExistsResponse := <-pdBillingMessageExistsCh

	if pdBillingMessageExistsResponse.Error != nil {
		ch <- &GetPandoraBillingMessageExistsServiceResponse{
			Error: pdBillingMessageExistsResponse.Error,
		}
		return
	}

	ch <- &GetPandoraBillingMessageExistsServiceResponse{
		Error:  nil,
		Exists: pdBillingMessageExistsResponse.Exists,
	}
}

func (s *PandoraBillingService) GetPandoraBillingMessage(ch chan *GetPandoraBillingMessageServiceResponse, model *GetPandoraBillingMessageServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPandoraBillingMessageServiceResponse{Error: modelErr}
		return
	}

	getPdBillingMessageCh := make(chan *order.GetPandoraBillingMessageResponse)
	defer close(getPdBillingMessageCh)

	go s.pandoraBillingDb.GetBillingMessage(getPdBillingMessageCh, &order.GetPandoraBillingMessage{
		OrderId: model.OrderId,
		Status:  model.Status,
	})

	getPdBillingMessageResponse := <-getPdBillingMessageCh
	if getPdBillingMessageResponse.Error != nil {
		ch <- &GetPandoraBillingMessageServiceResponse{
			Error: getPdBillingMessageResponse.Error,
		}
		return
	}

	var response GetPandoraBillingMessageServiceResponse
	err := json.Unmarshal([]byte(getPdBillingMessageResponse.OrderDetails), &response.PandoraBillingMessage)

	if err != nil {
		ch <- &GetPandoraBillingMessageServiceResponse{
			Error: err,
		}
		return
	}

	err = json.Unmarshal([]byte(getPdBillingMessageResponse.LogoOrderDetails), &response.LogoOrderDetails)

	if err != nil {
		ch <- &GetPandoraBillingMessageServiceResponse{
			Error: err,
		}
		return
	}

	ch <- &response
}

func (s *PandoraBillingService) GetPandoraBillingSoftposMessageExists(ch chan *GetPandoraBillingMessageExistsServiceResponse, model *GetPandoraBillingSoftposMessageExistsServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPandoraBillingMessageExistsServiceResponse{Error: modelErr}
		return
	}

	pdBillingMessageExistsCh := make(chan *order.GetPandoraBillingMessageExistsResponse)
	defer close(pdBillingMessageExistsCh)

	go s.pandoraBillingDb.GetSoftposBillingMessageExists(pdBillingMessageExistsCh, &order.GetPandoraBillingSoftposMessageExists{
		OrderId: model.OrderId,
	})

	pdBillingMessageExistsResponse := <-pdBillingMessageExistsCh

	if pdBillingMessageExistsResponse.Error != nil {
		ch <- &GetPandoraBillingMessageExistsServiceResponse{
			Error: pdBillingMessageExistsResponse.Error,
		}
		return
	}

	ch <- &GetPandoraBillingMessageExistsServiceResponse{
		Error:  nil,
		Exists: pdBillingMessageExistsResponse.Exists,
	}
}

func (s *PandoraBillingService) SavePandoraBillingSoftposMessage(ch chan *SavePandoraBillingMessageServiceResponse, model *SavePandoraBillingSoftposMessageServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SavePandoraBillingMessageServiceResponse{Error: modelErr}
		return
	}

	addPdBillingMessageCh := make(chan *order.AddPandoraBillingMessageResponse)
	defer close(addPdBillingMessageCh)

	orderDetails, _ := json.Marshal(model.Message)
	go s.pandoraBillingDb.AddSoftposBillingMessage(addPdBillingMessageCh, &order.AddPandoraBillingSoftposMessageModel{
		OrderId:          model.Message.TrackingNumber,
		OrderDetails:     string(orderDetails),
		LogoMessageId:    model.LogoMessageId,
		LogoOrderDetails: model.LogoOrderDetails,
		Timestamp:        time.Now(),
	})

	addPdBillingMessageResponse := <-addPdBillingMessageCh
	if addPdBillingMessageResponse.Error != nil {
		ch <- &SavePandoraBillingMessageServiceResponse{
			Error: addPdBillingMessageResponse.Error,
		}
		return
	}

	ch <- &SavePandoraBillingMessageServiceResponse{
		Error: nil,
	}
}
