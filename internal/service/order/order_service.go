package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	orderBq "logo-adapter/internal/data/bigquery/order"
	orderDb "logo-adapter/internal/data/database/order"
	"logo-adapter/internal/data/proxy/logistic"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/data/sns/publisher"
	"logo-adapter/internal/data/sns/publisher/order"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/service/product"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

type IOrderService interface {
	SendOrderFulfillment(ch chan *SendOrderFulfillmentServiceResponse, model *SendOrderFulfillmentServiceModel)
	GetOrderFulfillment(ch chan *GetOrderFulfillmentServiceResponse, model *GetOrderFulfillmentServiceModel)
	SendOrder(ch chan *SendOrderServiceResponse, model *SendOrderServiceModel)
	SendOrderCancellation(
		ch chan *SendOrderCancellationServiceResponse,
		model *SendOrderCancellationServiceModel,
	)
	SendEttnToPandora(ch chan *SendEttnToPandoraResponse, model *SendEttnToPandoraModel)
	SendOrderFulfillmentCancellation(
		ch chan *SendOrderFulfillmentCancellationServiceResponse,
		model *SendOrderFulfillmentCancellationServiceModel,
	)
	SendOrderFulfillmentReconciliation(
		ch chan *SendOrderFulfillmentReconciliationServiceResponse,
		model *SendOrderFulfillmentReconciliationServiceModel,
	)
	SendOfflineYsOrderToLogistic(
		ch chan *SendOfflineYsOrderToLogisticServiceResponse,
		model *SendOfflineYsOrderToLogisticServiceModel,
	)
	SendOfflineYsOrderCancellationToLogistic(
		ch chan *SendOfflineYsOrderCancellationToLogisticServiceResponse,
		model *SendOfflineYsOrderCancellationToLogisticServiceModel,
	)
	SendPartialRefundOrders(ch chan *SendPartialRefundOrdersServiceResponse)
	GetPaymentMethodIds(method string) (int, int, error)
	GetPaymentMethodMap(method string) (string, error)
}

type OrderService struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	PandoraBillingService IPandoraBillingService
	ProductService        product.IProductService
	OrderDb               orderDb.IOrderDb
	LogoProxy             logo.ILogoProxy
	ServerConfigService   configuration.IServerConfigurationService
	EttnPublisher         order.IEttnPublisher
	LogisticPosProxy      logistic.ILogisticProxy
	OrderBq               orderBq.IOrderBq
}

// NewOrderService
// Returns a new OrderService.
func NewOrderService(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	PandoraBillingService IPandoraBillingService,
	OrderDb orderDb.IOrderDb,
	LogoProxy logo.ILogoProxy,
	ServerConfigService configuration.IServerConfigurationService,
	EttnPublisher order.IEttnPublisher,
	LogisticPosProxy logistic.ILogisticProxy,
	ProductService product.IProductService,
	OrderBq orderBq.IOrderBq,
) IOrderService {
	service := OrderService{
		environment:           environment,
		loggr:                 loggr,
		validatr:              validatr,
		cachr:                 cachr,
		OrderDb:               OrderDb,
		LogoProxy:             LogoProxy,
		LogisticPosProxy:      LogisticPosProxy,
		PandoraBillingService: PandoraBillingService,
		ServerConfigService:   ServerConfigService,
		EttnPublisher:         EttnPublisher,
		OrderBq:               OrderBq,
	}

	if OrderDb != nil {
		service.OrderDb = OrderDb
	} else {
		service.OrderDb = orderDb.NewOrderDb(environment, loggr, validatr, cachr)
	}

	if LogoProxy != nil {
		service.LogoProxy = LogoProxy
	} else {
		service.LogoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	if LogisticPosProxy != nil {
		service.LogisticPosProxy = LogisticPosProxy
	} else {
		service.LogisticPosProxy = logistic.NewLogisticProxy(environment, loggr, validatr, cachr)
	}

	if ServerConfigService != nil {
		service.ServerConfigService = ServerConfigService
	} else {
		service.ServerConfigService = configuration.NewServerConfigurationService(
			environment,
			loggr,
			validatr,
			cachr,
			nil,
		)
	}

	if PandoraBillingService != nil {
		service.PandoraBillingService = PandoraBillingService
	} else {
		service.PandoraBillingService = NewPandoraBillingService(environment, loggr, validatr, cachr, nil, nil)
	}

	if EttnPublisher != nil {
		service.EttnPublisher = EttnPublisher
	} else {
		service.EttnPublisher = order.NewEttnPublisher(environment, loggr, validatr, cachr)
	}

	if ProductService != nil {
		service.ProductService = ProductService
	} else {
		service.ProductService = product.New(environment, loggr, validatr, cachr, nil, nil, nil)
	}

	if OrderBq != nil {
		service.OrderBq = OrderBq
	} else {
		service.OrderBq = orderBq.NewOrderBq(environment, loggr, validatr, cachr)
	}

	return &service
}

// SendOrder
// Sends order to Logo.
func (s *OrderService) SendOrderFulfillment(ch chan *SendOrderFulfillmentServiceResponse, model *SendOrderFulfillmentServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOrderFulfillmentServiceResponse{Error: modelErr}
		return
	}

	orderCh := make(chan *orderDb.GetOrderIsDeliveredResponse)
	defer close(orderCh)
	go s.OrderDb.GetOrderIsDelivered(
		orderCh, &orderDb.GetOrderIsDeliveredModel{
			OrderId: model.ExternalId,
		},
	)

	orderResponse := <-orderCh

	if orderResponse.Error != nil {
		ch <- &SendOrderFulfillmentServiceResponse{
			Error: orderResponse.Error,
		}
		return
	}

	if orderResponse.IsDelivered {
		ch <- &SendOrderFulfillmentServiceResponse{
			Error: nil,
		}
		return
	}

	var paymentId int64
	var collectFromCustomer float64 = 0
	paymentOperationType := enum.PaymentOperationTypeOrder

	paymentMethodName, err := s.GetPaymentMethodMap(model.PaymentMethod)
	if err != nil {
		ch <- &SendOrderFulfillmentServiceResponse{
			Error: err,
		}
		return
	}

	paymentMethodId, paymentTypeId, err := s.GetPaymentMethodIds(paymentMethodName)
	if err != nil {
		ch <- &SendOrderFulfillmentServiceResponse{
			Error: err,
		}
		return
	}

	if paymentTypeId == enum.PaymentTypeOffline && model.YemekParams == nil {
		ch <- &SendOrderFulfillmentServiceResponse{
			Error: errors.New("yemek_params field is empty for offline payment order"),
		}
		return
	}

	if paymentTypeId == enum.PaymentTypeOffline {
		collectFromCustomer = model.YemekParams.CollectFromCustomer
	}

	if orderResponse.OrderId == "" {
		serializedModel, serializationErr := json.Marshal(model)
		if serializationErr != nil {
			ch <- &SendOrderFulfillmentServiceResponse{
				Error: serializationErr,
			}
			return
		}

		orderPaymentStatus := enum.PaymentStatusPending
		if paymentTypeId == enum.PaymentTypeOnline || model.YemekParams.CollectFromCustomer == 0 {
			orderPaymentStatus = enum.PaymentStatusCompleted
		}

		if paymentTypeId == enum.PaymentTypeOffline && model.IsModified {
			orderPaymentStatus = enum.PaymentStatusWaitingForReconciliation
			collectFromCustomer = 0
		}

		addOrderCh := make(chan *orderDb.AddOrderResponse)
		defer close(addOrderCh)
		go s.OrderDb.AddOrder(
			addOrderCh, &orderDb.AddOrderModel{
				OrderId:        model.ExternalId,
				OrderDetails:   string(serializedModel),
				Timestamp:      model.Timestamp,
				StoreId:        model.WarehouseId,
				UserId:         model.Customer.Id,
				UserFriendlyId: model.Customer.Id,
				UserFirstName:  model.Customer.FirstName,
				UserLastName:   model.Customer.LastName,
				OrderPayment: orderDb.AddOrderPaymentModel{
					OrderId:                  model.ExternalId,
					PaymentTypeId:            paymentTypeId,
					PaymentMethodId:          paymentMethodId,
					PaymentOperationType:     paymentOperationType,
					Status:                   orderPaymentStatus,
					PaymentOperationTypeDate: model.OrderCreatedAt,
					Amount:                   collectFromCustomer,
					TransactionId:            "",
				},
			},
		)

		addOrderResponse := <-addOrderCh
		if addOrderResponse.Error != nil {
			ch <- &SendOrderFulfillmentServiceResponse{
				Error: addOrderResponse.Error,
			}
			return
		}

		paymentId = addOrderResponse.PaymentId
	}

	orderCancellationCh := make(chan *orderDb.GetOrderCancellationResponse)
	defer close(orderCancellationCh)
	go s.OrderDb.GetOrderCancellation(
		orderCancellationCh, &orderDb.GetOrderCancellationModel{
			OrderId:              model.ExternalId,
			OrderTransactionType: enum.OrderTransactionTypeCancelled,
		},
	)

	orderCancellationResponse := <-orderCancellationCh
	if orderCancellationResponse.Error != nil {
		ch <- &SendOrderFulfillmentServiceResponse{
			Error: orderCancellationResponse.Error,
		}
		return
	}

	if orderCancellationResponse.OrderId != "" {
		s.loggr.Warn(
			"SendPostPandoraOrderFulfillment Order have cancellation process. TrackingNumber: "+model.ExternalId,
			zap.Any("Data", model),
		)
		ch <- &SendOrderFulfillmentServiceResponse{
			Error: nil,
		}
		return
	}

	// send to logistic pos
	// only offline payments can be sent
	if paymentTypeId == enum.PaymentTypeOffline && collectFromCustomer > 0 {

		orderPaymentCh := make(chan *orderDb.GetOrderPaymentResponse)
		defer close(orderPaymentCh)
		go s.OrderDb.GetOrderPayment(
			orderPaymentCh, &orderDb.GetOrderPaymentModel{
				OrderId:              model.ExternalId,
				PaymentOperationType: paymentOperationType,
				TransactionId:        "",
			},
		)

		orderPaymentResponse := <-orderPaymentCh
		if orderPaymentResponse.Error != nil {
			ch <- &SendOrderFulfillmentServiceResponse{
				Error: orderPaymentResponse.Error,
			}
			return
		}

		if orderResponse.OrderId != "" {
			paymentId = orderPaymentResponse.PaymentId
		}

		if orderPaymentResponse.Status == enum.PaymentStatusPending {
			orderCollectionModel := logistic.SendOrderCollectionModel{
				VendorName:           enum.DefaultVendorName,
				VendorTransactionId:  paymentId,
				VendorTrackingNumber: model.ExternalId,
				Amount:               collectFromCustomer,
				TransactionType:      enum.PaymentOperationTypeGetName(paymentOperationType),
				PaymentGroupType:     enum.PaymentGroupTypeFull,
				PaymentMethodName:    paymentMethodName,
				StoreId:              model.WarehouseId,
			}

			s.loggr.Info(
				"Sending order to LogisticPos. TrackingNumber: "+model.ExternalId,
				zap.Any("SendOrderToLogisticPos", orderCollectionModel),
			)

			sendOrderCollectionCh := make(chan *logistic.LogisticPosProxyBaseResponse)
			defer close(sendOrderCollectionCh)
			go s.LogisticPosProxy.SendOrderCollection(sendOrderCollectionCh, &orderCollectionModel)

			sendOrderCollectionResponse := <-sendOrderCollectionCh
			if sendOrderCollectionResponse.Error != nil {
				s.loggr.Error(
					"Failed to sending order to LogisticPos. TrackingNumber: "+model.ExternalId,
					zap.Any("Model", model),
					zap.Error(sendOrderCollectionResponse.Error),
				)
				ch <- &SendOrderFulfillmentServiceResponse{
					Error: sendOrderCollectionResponse.Error,
				}
				return
			}

			markPaymentCh := make(chan *orderDb.MarkOrderPaymentAsDeliveredResponse)
			defer close(markPaymentCh)
			go s.OrderDb.MarkOrderPaymentAsDelivered(
				markPaymentCh, &orderDb.MarkOrderPaymentAsDeliveredModel{
					OrderId:   model.ExternalId,
					PaymentId: paymentId,
				},
			)

			markPaymentResponse := <-markPaymentCh
			if markPaymentResponse.Error != nil {
				ch <- &SendOrderFulfillmentServiceResponse{
					Error: markPaymentResponse.Error,
				}
				return
			}
		}
	}

	ch <- &SendOrderFulfillmentServiceResponse{Error: nil}
}

func (s *OrderService) GetOrderFulfillment(ch chan *GetOrderFulfillmentServiceResponse, model *GetOrderFulfillmentServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetOrderFulfillmentServiceResponse{Error: modelErr}
		return
	}

	getOrderCh := make(chan *orderDb.GetOrderResponse)
	defer close(getOrderCh)

	go s.OrderDb.GetOrder(
		getOrderCh, &orderDb.GetOrderModel{
			OrderId: model.OrderId,
		},
	)

	getOrderResponse := <-getOrderCh
	if getOrderResponse.Error != nil {
		ch <- &GetOrderFulfillmentServiceResponse{
			Error: getOrderResponse.Error,
		}
		return
	}

	if getOrderResponse.OrderDetails == "" {
		ch <- &GetOrderFulfillmentServiceResponse{
			Error: fmt.Errorf("couldn't find order fulfillment ! OrderId : %s", model.OrderId),
		}
		return
	}

	response := GetOrderFulfillmentServiceResponse{Error: nil}

	unmarshalErr := json.Unmarshal([]byte(getOrderResponse.OrderDetails), &response)
	if unmarshalErr != nil {
		ch <- &GetOrderFulfillmentServiceResponse{
			Error: unmarshalErr,
		}
		return
	}

	ch <- &response
}

func (s *OrderService) SendOrder(
	ch chan *SendOrderServiceResponse,
	model *SendOrderServiceModel,
) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOrderServiceResponse{Error: modelErr}
		return
	}

	ofDetails, ofErr := s.getOrderFulfillmentDetails(model.Message.OrderId)
	if ofErr != nil {
		ch <- &SendOrderServiceResponse{
			Error: ofErr,
		}
		return
	}

	totalLineItemsAmount := 0.0
	var orderDetail []logo.SendCompletedOrderDetailModel

	for i, x := range ofDetails.Items {
		if x.CollectedPricing.Quantity < 1 {
			continue
		}
		orderDetailModel := logo.SendCompletedOrderDetailModel{
			SKU:            x.SKU,
			Quantity:       x.CollectedPricing.Quantity,
			LineItemId:     x.ExternalId,
			LineIndex:      i + 1,
			IsBundle:       false,
			TotalListPrice: math.Round(x.OriginalPricing.UnitListPrice*float64(x.CollectedPricing.Quantity)*10000) / 10000,
			TotalPrice:     x.CollectedPricing.TotalPrice,
			UnitListPrice:  float64(x.OriginalPricing.UnitListPrice),
			ProductName:    x.Name,
		}
		orderDetail = append(orderDetail, orderDetailModel)

		totalLineItemsAmount += orderDetailModel.TotalListPrice
	}

	totalLineItemsAmount = math.Round(totalLineItemsAmount*10000) / 10000

	deliveryFee := model.Message.DeliveryFee

	ofDetails.OrderCheckedOutAt = helper.ConvertTimeByZone(ofDetails.OrderCheckedOutAt, "Europe/Istanbul")
	ofDetails.OrderCreatedAt = helper.ConvertTimeByZone(ofDetails.OrderCreatedAt, "Europe/Istanbul")

	paymentMethodName, err := s.GetPaymentMethodMap(model.Message.PaymentMethodName)
	if err != nil {
		ch <- &SendOrderServiceResponse{
			Error: err,
		}
		return
	}

	if ofDetails.Customer.FirstName == "" || ofDetails.Customer.LastName == "" {
		ofDetails.Customer.FirstName = "Muhtelif"
		ofDetails.Customer.LastName = "Müşteriler"
	}

	//Gel-Al siparisleri icin depo adresi set edilir
	if strings.TrimSpace(model.Message.OrderAddress.CityName) == "" {
		getWarehouseCh := make(chan *orderDb.GetWarehouseByIdResponse)
		defer close(getWarehouseCh)

		go s.OrderDb.GetWarehouseById(
			getWarehouseCh, &orderDb.GetWarehouseByIdModel{
				WarehouseId: ofDetails.WarehouseId,
			},
		)

		getWarehouseResponse := <-getWarehouseCh
		if getWarehouseResponse.Error != nil {
			ch <- &SendOrderServiceResponse{
				Error: getWarehouseResponse.Error,
			}
			return
		}

		model.Message.OrderAddress.CityName = getWarehouseResponse.Warehouse.City

		if strings.TrimSpace(model.Message.OrderAddress.AddressLine) == "" {
			model.Message.OrderAddress.AddressLine = getWarehouseResponse.Warehouse.Address
		}
	}

	sendOrderModel := &logo.SendCompletedOrderModel{
		MessageId:       model.MessageId,
		TransactionDate: ofDetails.OrderCheckedOutAt,
		OrderInfo: logo.SendCompletedOrderInfoModel{
			TrackingNumber:       model.Message.OrderId,
			OrderDate:            ofDetails.OrderCreatedAt,
			CollectedDate:        ofDetails.OrderCheckedOutAt,
			PaymentApprovedDate:  ofDetails.OrderCreatedAt,
			PaymentCollectedDate: ofDetails.OrderCreatedAt,
			PaymentMethodName:    paymentMethodName,
			PaymentList: []logo.PaymentList{
				{
					BankCode: model.Message.PaymentList[0].BankCode,
					Amount:   model.Message.PaymentList[0].Amount,
				},
			},
			StoreId: ofDetails.WarehouseId,
			UserData: logo.UserData{
				UserId:         ofDetails.Customer.Id,
				UserFirstName:  ofDetails.Customer.FirstName,
				UserLastName:   ofDetails.Customer.LastName,
				UserFriendlyId: ofDetails.Customer.Id,
			},
			CourierName:                     ofDetails.RiderName,
			TotalLineItemsAmount:            totalLineItemsAmount,
			DeliveryFee:                     deliveryFee,
			TotalAmount:                     model.Message.PaymentList[0].Amount,
			TipAmount:                       model.Message.TipAmount,
			BasketDiscountAmount:            math.Abs(model.Message.BasketDiscountAmount),
			CouponDiscountAmount:            math.Abs(model.Message.CouponDiscountAmount),
			DeliveryFeeCouponDiscountAmount: math.Abs(model.Message.DeliveryFeeCoupon),
		},
		OrderDetail: orderDetail,
		OrderAddress: logo.OrderAddress{
			FirstName:       ofDetails.Customer.FirstName,
			LastName:        ofDetails.Customer.LastName,
			TelephoneNumber: model.Message.OrderAddress.TelephoneNumber,
			AddressLine:     model.Message.OrderAddress.AddressLine,
			RegionName:      model.Message.OrderAddress.RegionName,
			CityName:        model.Message.OrderAddress.CityName,
			Email:           model.Message.OrderAddress.Email,
		},
	}

	s.loggr.Info(
		"Sending completed order to Logo. TrackingNumber: "+model.Message.OrderId,
		zap.Any("SendCompletedOrderModel", sendOrderModel),
	)

	sendOrderCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(sendOrderCh)

	go s.LogoProxy.SendCompletedOrder(sendOrderCh, sendOrderModel)
	sendOrderResponse := <-sendOrderCh

	if sendOrderResponse.Error != nil {
		s.loggr.Error(
			"Failed to sending order to Logo. TrackingNumber: "+model.Message.OrderId,
			zap.Any("Model", sendOrderModel),
			zap.Error(sendOrderResponse.Error),
		)
		ch <- &SendOrderServiceResponse{
			Error: sendOrderResponse.Error,
		}
		return
	}

	saveMessageCh := make(chan *SavePandoraBillingMessageServiceResponse)
	defer close(saveMessageCh)

	marshaledLogoModel, _ := json.Marshal(sendOrderModel)
	go s.PandoraBillingService.SavePandoraBillingMessage(
		saveMessageCh, &SavePandoraBillingMessageServiceModel{
			Message:          model.Message,
			LogoMessageId:    sendOrderModel.MessageId,
			LogoOrderDetails: string(marshaledLogoModel),
		},
	)

	saveMessageResponse := <-saveMessageCh
	if saveMessageResponse.Error != nil {
		ch <- &SendOrderServiceResponse{
			Error: saveMessageResponse.Error,
		}
		return
	}

	markOrderAsDeliveredCh := make(chan *orderDb.MarkOrderAsDeliveredResponse)
	defer close(markOrderAsDeliveredCh)
	go s.OrderDb.MarkOrderAsDelivered(
		markOrderAsDeliveredCh, &orderDb.MarkOrderAsDeliveredModel{
			OrderId: model.Message.OrderId,
		},
	)

	markOrderAsDeliveredResponse := <-markOrderAsDeliveredCh
	if markOrderAsDeliveredResponse.Error != nil {
		ch <- &SendOrderServiceResponse{
			Error: markOrderAsDeliveredResponse.Error,
		}
		return
	}

	ch <- &SendOrderServiceResponse{
		Error: nil,
	}
}

func (s *OrderService) SendOrderCancellation(
	ch chan *SendOrderCancellationServiceResponse,
	model *SendOrderCancellationServiceModel,
) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOrderCancellationServiceResponse{Error: modelErr}
		return
	}

	ofDetails, ofErr := s.getOrderFulfillmentDetails(model.Message.OrderId)
	if ofErr != nil {
		ch <- &SendOrderCancellationServiceResponse{
			Error: ofErr,
		}
		return
	}

	model.Message.PaidAt = helper.ConvertTimeByZone(model.Message.PaidAt, "Europe/Istanbul")
	model.Message.OrderCreationDate = helper.ConvertTimeByZone(model.Message.OrderCreationDate, "Europe/Istanbul")
	model.Message.CancelledAt = helper.ConvertTimeByZone(model.Message.CancelledAt, "Europe/Istanbul")

	paymentMethod, err := s.GetPaymentMethodMap(model.Message.PaymentMethodName)
	if err != nil {
		ch <- &SendOrderCancellationServiceResponse{
			Error: err,
		}
		return
	}

	var refundedOrderDetail []logo.RefundedOrderDetail
	var totalLineItemsAmount float64

	for i, x := range ofDetails.Items {
		if x.CollectedPricing.Quantity < 1 {
			continue
		}
		refundedOrderDetailModel := logo.RefundedOrderDetail{
			SKU:                    x.SKU,
			Quantity:               x.CollectedPricing.Quantity,
			LineItemId:             x.ExternalId,
			LineIndex:              i + 1,
			IsBundle:               false,
			TotalListPrice:         math.Round(x.OriginalPricing.UnitListPrice*float64(x.CollectedPricing.Quantity)*10000) / 10000,
			TotalPrice:             x.CollectedPricing.TotalPrice,
			UnitListPrice:          float64(x.OriginalPricing.UnitListPrice),
			ProductName:            x.Name,
			OriginalTotalPrice:     x.CollectedPricing.TotalPrice,
			OriginalUnitPrice:      x.OriginalPricing.UnitListPrice,
			OriginalTotalListPrice: math.Round(x.OriginalPricing.UnitListPrice*float64(x.CollectedPricing.Quantity)*10000) / 10000,
		}
		refundedOrderDetail = append(refundedOrderDetail, refundedOrderDetailModel)

		totalLineItemsAmount += refundedOrderDetailModel.TotalListPrice
	}

	totalLineItemsAmount = math.Round(totalLineItemsAmount*10000) / 10000

	deliveryFee := model.Message.DeliveryFee
	couponDiscountAmount := model.Message.CouponDiscountAmount
	basketDiscountAmount := model.Message.BasketDiscountAmount
	deliveryFeeCouponDiscountAmount := model.Message.DeliveryFeeCoupon

	if model.Message.UserData.UserFirstName == "" || model.Message.UserData.UserLastName == "" {
		model.Message.UserData.UserFirstName = "Muhtelif"
		model.Message.UserData.UserLastName = "Müşteriler"
	}

	cancellationModel := logo.SendCompletedRefundModel{
		MessageId:       model.MessageId,
		TransactionDate: model.Message.CancelledAt,
		OrderInfo: logo.SendCompletedRefundOrderInfoModel{
			TrackingNumber:       model.Message.OrderId,
			RefundDate:           model.Message.CancelledAt,
			CollectedDate:        model.Message.CancelledAt,
			PaymentApprovedDate:  model.Message.CancelledAt,
			PaymentCollectedDate: model.Message.CancelledAt,
			PaymentList: []logo.PaymentList{
				{
					BankCode: model.Message.PaymentList[0].BankCode,
					Amount:   math.Abs(model.Message.PaymentList[0].Amount),
				},
			},
			PaymentMethodName: paymentMethod,
			StoreId:           ofDetails.WarehouseId,
			UserData: logo.UserData{
				UserId:         model.Message.UserData.UserId,
				UserFirstName:  model.Message.UserData.UserFirstName,
				UserLastName:   model.Message.UserData.UserLastName,
				UserFriendlyId: model.Message.UserData.UserId,
			},
			RefundedLineItemsAmount:                 totalLineItemsAmount,
			RefundedTotalAmount:                     math.Abs(model.Message.PaymentList[0].Amount),
			RefundedDeliveryFee:                     math.Abs(deliveryFee),
			RefundedTipAmount:                       math.Abs(model.Message.TipAmount),
			RefundedCouponDiscountAmount:            math.Abs(couponDiscountAmount),
			RefundedBasketDiscountAmount:            math.Abs(basketDiscountAmount),
			RefundedDeliveryFeeCouponDiscountAmount: math.Abs(deliveryFeeCouponDiscountAmount),
		},
		RefundedOrderDetail: refundedOrderDetail,
	}

	s.loggr.Info(
		"Sending refund to Logo. TrackingNumber: "+model.Message.OrderId,
		zap.Any("SendCompletedRefundToLogo", cancellationModel),
	)

	sendFullRefundCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(sendFullRefundCh)

	go s.LogoProxy.SendCompletedFullRefund(sendFullRefundCh, &cancellationModel)
	sendFullRefundResponse := <-sendFullRefundCh

	if sendFullRefundResponse.Error != nil {
		s.loggr.Error(
			"Failed to sending refund to Logo. TrackingNumber: "+model.Message.OrderId,
			zap.Any("Model", model),
			zap.Error(sendFullRefundResponse.Error),
		)
		ch <- &SendOrderCancellationServiceResponse{
			Error: sendFullRefundResponse.Error,
		}
		return
	}

	saveMessageCh := make(chan *SavePandoraBillingMessageServiceResponse)
	defer close(saveMessageCh)

	marshaledLogoModel, _ := json.Marshal(cancellationModel)
	go s.PandoraBillingService.SavePandoraBillingMessage(
		saveMessageCh, &SavePandoraBillingMessageServiceModel{
			Message:          model.Message,
			LogoMessageId:    cancellationModel.MessageId,
			LogoOrderDetails: string(marshaledLogoModel),
		},
	)

	saveMessageResponse := <-saveMessageCh
	if saveMessageResponse.Error != nil {
		ch <- &SendOrderCancellationServiceResponse{
			Error: saveMessageResponse.Error,
		}
		return
	}

	ch <- &SendOrderCancellationServiceResponse{
		Error: nil,
	}
}

func (s *OrderService) getOrderFulfillmentDetails(orderId string) (*GetOrderFulfillmentServiceResponse, error) {
	getOrderFulfillmentCh := make(chan *GetOrderFulfillmentServiceResponse)
	defer close(getOrderFulfillmentCh)

	go s.GetOrderFulfillment(
		getOrderFulfillmentCh, &GetOrderFulfillmentServiceModel{
			OrderId: orderId,
		},
	)

	orderFulfillmentDetails := <-getOrderFulfillmentCh
	if orderFulfillmentDetails.Error != nil {
		return nil, orderFulfillmentDetails.Error
	}

	return orderFulfillmentDetails, nil
}

func (s *OrderService) GetPaymentMethodMap(method string) (string, error) {
	paymentMethodMapsConfigValue := s.ServerConfigService.Get(enum.PaymentMethodMaps)
	if paymentMethodMapsConfigValue == nil {
		return "", errors.New("couldn't find payment method maps on config")
	}

	paymentMethodMaps := make(map[string]string)
	err := json.Unmarshal([]byte(*paymentMethodMapsConfigValue), &paymentMethodMaps)
	if err != nil {
		return "", err
	}

	paymentMethod := paymentMethodMaps[method]
	if paymentMethod == "" {
		return "", fmt.Errorf("couldn't find related payment method map - %v", method)
	}

	return paymentMethod, nil
}

func (s *OrderService) SendEttnToPandora(ch chan *SendEttnToPandoraResponse, model *SendEttnToPandoraModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &SendEttnToPandoraResponse{
			Error: err,
		}
		return
	}

	pdBillingCh := make(chan *GetPandoraBillingMessageServiceResponse)
	defer close(pdBillingCh)

	go s.PandoraBillingService.GetPandoraBillingMessage(
		pdBillingCh, &GetPandoraBillingMessageServiceModel{
			OrderId: model.OrderId,
			Status:  "created",
		},
	)

	pdBillingResponse := <-pdBillingCh
	if pdBillingResponse.Error != nil {
		ch <- &SendEttnToPandoraResponse{
			Error: pdBillingResponse.Error,
		}
		return
	}

	pandoraModel := pdBillingResponse.PandoraBillingMessage
	logoModel := pdBillingResponse.LogoOrderDetails

	var orderDetail []order.OrderDetail
	for _, logoOrderDetail := range logoModel.OrderDetail {
		ettnOrderDetail := model.Order.GetOrderDetailBySku(logoOrderDetail.SKU)
		if ettnOrderDetail == nil {
			ch <- &SendEttnToPandoraResponse{
				Error: fmt.Errorf(
					"Couldn't find order item in order fulfillment message. SKU : %v",
					logoOrderDetail.SKU,
				),
			}
			return
		}

		orderDetail = append(
			orderDetail, order.OrderDetail{
				LineItemID:     logoOrderDetail.LineItemId,
				Name:           logoOrderDetail.ProductName,
				ProductID:      logoOrderDetail.SKU,
				Quantity:       int(ettnOrderDetail.Quantity),
				TotalListPrice: ettnOrderDetail.TotalListPrice,
				TotalPrice:     ettnOrderDetail.TotalListPrice,
				UnitListPrice:  ettnOrderDetail.UnitListPrice,
				Vat:            ettnOrderDetail.Vat,
				Discount:       0,
			},
		)
	}

	var vats []order.Vat
	for _, vat := range model.Order.Vats {
		vats = append(
			vats, order.Vat{
				Amount: vat.Amount,
				Rate:   vat.Rate,
				Base:   vat.Base,
			},
		)
	}

	publishCh := make(chan publisher.PublisherResponse)
	defer close(publishCh)

	go s.EttnPublisher.Publish(
		publishCh, &order.EttnPublisherModel{
			EventId:        uuid.NewString(),
			EventTime:      time.Now().UTC(),
			Source:         "logo",
			Type:           "receipt_requested",
			Brand:          "yemeksepeti",
			CountryCode:    "tr",
			GlobalEntityId: "YS_TR",
			RequestId:      uuid.NewString(),
			Data: order.EttnData{
				OrderCode:    model.OrderId,
				LanguageCode: "tr_TR",
				Logo: order.LogoData{
					OrderCode:     model.OrderId,
					Ettn:          model.Ettn,
					InvoiceNumber: model.InvoiceNumber,
					CourierName:   logoModel.OrderInfo.CourierName,
					InvoiceDate:   model.InvoiceDate,
					Order: order.EttnOrder{
						OrderID:           logoModel.OrderInfo.TrackingNumber,
						PaymentMethodName: pandoraModel.PaymentMethodName,
						Version:           pandoraModel.Version,
						Status:            pandoraModel.Status,
						PaymentList: []order.Payment{
							{
								BankCode: logoModel.OrderInfo.PaymentList[0].BankCode,
								Amount:   model.Order.TotalAmount,
							},
						},
						TotalLineItemsAmount: model.Order.TotalLineItemsAmount,
						DeliveryFee:          model.Order.DeliveryFee.TotalPrice,
						TotalAmount:          model.Order.TotalAmount,
						TipAmount:            model.Order.TipAmount.TotalPrice,
						CouponDiscountAmount: model.Order.TotalDiscountAmount,
						BasketDiscountAmount: 0,
						OrderDetail:          orderDetail,
						OrderAddress: order.OrderAddress{
							FirstName:   logoModel.OrderAddress.FirstName,
							LastName:    logoModel.OrderAddress.LastName,
							CityName:    logoModel.OrderAddress.CityName,
							RegionName:  logoModel.OrderAddress.RegionName,
							AddressLine: logoModel.OrderAddress.AddressLine,
							Email:       logoModel.OrderAddress.Email,
						},
						UserData: order.UserData{
							UserID:        logoModel.OrderInfo.UserData.UserId,
							UserFirstName: logoModel.OrderInfo.UserData.UserFirstName,
							UserLastName:  logoModel.OrderInfo.UserData.UserLastName,
						},
						OrderCreationDate: model.OrderCreationDate,
						PaidAt:            pandoraModel.PaidAt,
						Vats:              vats,
						VatBase:           model.Order.TaxBasis,
					},
				},
			},
			OrderCode: model.OrderId,
		}, nil,
	)

	publishResponse := <-publishCh
	if publishResponse.Error != nil {
		ch <- &SendEttnToPandoraResponse{
			Error: publishResponse.Error,
		}
		return
	}

	ch <- &SendEttnToPandoraResponse{
		Error: nil,
	}
}

func (s *OrderService) SendOrderFulfillmentCancellation(
	ch chan *SendOrderFulfillmentCancellationServiceResponse,
	model *SendOrderFulfillmentCancellationServiceModel,
) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOrderFulfillmentCancellationServiceResponse{Error: modelErr}
		return
	}

	var paymentId int64
	paymentMethodName, err := s.GetPaymentMethodMap(model.PaymentMethod)
	if err != nil {
		ch <- &SendOrderFulfillmentCancellationServiceResponse{
			Error: err,
		}
		return
	}
	paymentMethodId, paymentTypeId, err := s.GetPaymentMethodIds(paymentMethodName)
	if err != nil {
		ch <- &SendOrderFulfillmentCancellationServiceResponse{
			Error: err,
		}
		return
	}

	orderPaymentCh := make(chan *orderDb.GetOrderPaymentResponse)
	defer close(orderPaymentCh)
	go s.OrderDb.GetOrderPayment(
		orderPaymentCh, &orderDb.GetOrderPaymentModel{
			OrderId:              model.OrderId,
			PaymentOperationType: enum.PaymentOperationTypeOrder,
			TransactionId:        "",
		},
	)

	orderPaymentResponse := <-orderPaymentCh
	if orderPaymentResponse.Error != nil {
		ch <- &SendOrderFulfillmentCancellationServiceResponse{
			Error: orderPaymentResponse.Error,
		}
		return
	}

	orderCancellationCh := make(chan *orderDb.AddOrderCancellationResponse)
	defer close(orderCancellationCh)
	go s.OrderDb.AddOrderCancellation(
		orderCancellationCh, &orderDb.AddOrderCancellationModel{
			OrderId:              model.OrderId,
			OrderTransactionType: enum.OrderTransactionTypeCancelled,
		},
	)

	orderCancellationResponse := <-orderCancellationCh
	if orderCancellationResponse.Error != nil {
		ch <- &SendOrderFulfillmentCancellationServiceResponse{
			Error: orderCancellationResponse.Error,
		}
		return
	}

	if orderPaymentResponse.PaymentId == 0 {
		s.loggr.Warn(
			"SendOrderFulfillmentCancellation could find the order payment detail. TrackingNumber: "+model.OrderId,
			zap.Any("Data", model),
		)

		ch <- &SendOrderFulfillmentCancellationServiceResponse{
			Error: nil,
		}
		return
	}

	if orderPaymentResponse.Status == enum.PaymentStatusWaitingForReconciliation {
		ofDetails, ofErr := s.getOrderFulfillmentDetails(model.OrderId)
		if ofErr != nil {
			ch <- &SendOrderFulfillmentCancellationServiceResponse{
				Error: ofErr,
			}
			return
		}

		ofProducts := len(ofDetails.Items)

		notFoundProducts := 0

		for _, productDetail := range model.Products {
			if productDetail.Status == "NOT_FOUND" {
				notFoundProducts++
			}
		}

		if ofProducts != notFoundProducts {
			ch <- &SendOrderFulfillmentCancellationServiceResponse{
				Error: errors.New("waiting for order reconciliation"),
			}
			return
		}
		s.loggr.Warn("SendOrderFulfillmentCancellation reconciliation step skipped due to the all products status is not found"+
			". TrackingNumber: "+model.OrderId, zap.Any("Data", model))

		markCh := make(chan *orderDb.MarkOrderPaymentAsCompletedResponse)
		defer close(markCh)
		go s.OrderDb.MarkOrderPaymentAsCompleted(markCh, &orderDb.MarkOrderPaymentAsCompletedModel{
			TransactionId: orderPaymentResponse.PaymentId,
			OrderId:       model.OrderId,
		})

		markResponse := <-markCh
		if markResponse.Error != nil {
			ch <- &SendOrderFulfillmentCancellationServiceResponse{Error: markResponse.Error}
			return
		}
	}

	paymentOperationType := enum.PaymentOperationTypeRefund
	orderPaymentStatus := enum.PaymentStatusPending
	if paymentTypeId == enum.PaymentTypeOnline || orderPaymentResponse.Amount == 0 {
		orderPaymentStatus = enum.PaymentStatusCompleted
	}

	orderRefundPaymentCh := make(chan *orderDb.GetOrderPaymentResponse)
	defer close(orderRefundPaymentCh)
	go s.OrderDb.GetOrderPayment(
		orderRefundPaymentCh, &orderDb.GetOrderPaymentModel{
			OrderId:              model.OrderId,
			PaymentOperationType: paymentOperationType,
			TransactionId:        "",
		},
	)

	orderRefundPaymentResponse := <-orderRefundPaymentCh
	if orderRefundPaymentResponse.Error != nil {
		ch <- &SendOrderFulfillmentCancellationServiceResponse{
			Error: orderRefundPaymentResponse.Error,
		}
		return
	}

	if orderRefundPaymentResponse.PaymentId != 0 {
		paymentId = orderRefundPaymentResponse.PaymentId
		orderPaymentStatus = orderRefundPaymentResponse.Status
	} else {
		addOrderPaymentCh := make(chan *orderDb.AddOrderPaymentResponse)
		defer close(addOrderPaymentCh)
		go s.OrderDb.AddOrderPayment(
			addOrderPaymentCh, &orderDb.AddOrderPaymentModel{
				OrderId:                  model.OrderId,
				PaymentTypeId:            paymentTypeId,
				PaymentMethodId:          paymentMethodId,
				Status:                   orderPaymentStatus,
				PaymentOperationType:     paymentOperationType,
				PaymentOperationTypeDate: model.CancellationDate,
				Amount:                   orderPaymentResponse.Amount,
				TransactionId:            "",
			},
		)

		addOrderPaymentResponse := <-addOrderPaymentCh
		if addOrderPaymentResponse.Error != nil {
			ch <- &SendOrderFulfillmentCancellationServiceResponse{Error: addOrderPaymentResponse.Error}
			return
		}

		paymentId = addOrderPaymentResponse.PaymentId
	}

	if paymentTypeId == enum.PaymentTypeOffline && orderPaymentStatus == enum.PaymentStatusPending {

		orderCollectionModel := logistic.SendOrderCollectionModel{
			VendorName:           enum.DefaultVendorName,
			VendorTransactionId:  paymentId,
			VendorTrackingNumber: model.OrderId,
			Amount:               orderPaymentResponse.Amount,
			TransactionType:      enum.PaymentOperationTypeGetName(paymentOperationType),
			PaymentGroupType:     enum.PaymentGroupTypeFull,
			PaymentMethodName:    paymentMethodName,
			StoreId:              model.WarehouseId,
			RiderName:            "-",
		}

		s.loggr.Info(
			"Sending order cancellation to LogisticPos. TrackingNumber: "+model.OrderId,
			zap.Any("SendOrderToLogisticPos", orderCollectionModel),
		)

		sendOrderCollectionCh := make(chan *logistic.LogisticPosProxyBaseResponse)
		defer close(sendOrderCollectionCh)
		go s.LogisticPosProxy.SendOrderCollection(sendOrderCollectionCh, &orderCollectionModel)

		SendOrderCollectionResponse := <-sendOrderCollectionCh
		if SendOrderCollectionResponse.Error != nil {
			s.loggr.Error(
				"Failed to sending order cancellation to LogisticPos. TrackingNumber: "+model.OrderId,
				zap.Any("Model", model),
				zap.Error(SendOrderCollectionResponse.Error),
			)
			ch <- &SendOrderFulfillmentCancellationServiceResponse{
				Error: SendOrderCollectionResponse.Error,
			}
			return
		}

		markPaymentCh := make(chan *orderDb.MarkOrderPaymentAsDeliveredResponse)
		defer close(markPaymentCh)
		go s.OrderDb.MarkOrderPaymentAsDelivered(
			markPaymentCh, &orderDb.MarkOrderPaymentAsDeliveredModel{
				OrderId:   model.OrderId,
				PaymentId: paymentId,
			},
		)

		markPaymentResponse := <-markPaymentCh
		if markPaymentResponse.Error != nil {
			ch <- &SendOrderFulfillmentCancellationServiceResponse{
				Error: markPaymentResponse.Error,
			}
			return
		}
	}

	ch <- &SendOrderFulfillmentCancellationServiceResponse{
		Error: nil,
	}
}

func (s *OrderService) GetPaymentMethodIds(method string) (int, int, error) {
	paymentMethodId := 0
	paymentTypeId := enum.PaymentTypeOffline
	switch method {
	case "OnlineCreditCard":
		{
			paymentMethodId = enum.PaymentMethodOnlineCreditCard
			paymentTypeId = enum.PaymentTypeOnline
		}
	case "CreditCard":
		{
			paymentMethodId = enum.PaymentMethodCreditCard
		}
	case "Cash":
		{
			paymentMethodId = enum.PaymentMethodCash
		}
	case "Other":
		{
			paymentMethodId = enum.PaymentMethodOther
			paymentTypeId = enum.PaymentTypeOther
		}
	}

	if paymentMethodId == 0 {
		return 0, 0, errors.New("couldn't find the related payment method for logo model")
	}
	return paymentMethodId, paymentTypeId, nil
}

func (s *OrderService) SendOrderFulfillmentReconciliation(
	ch chan *SendOrderFulfillmentReconciliationServiceResponse,
	model *SendOrderFulfillmentReconciliationServiceModel,
) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOrderFulfillmentReconciliationServiceResponse{Error: modelErr}
		return
	}

	paymentOperationType := enum.PaymentOperationTypeOrder

	paymentMethodName, err := s.GetPaymentMethodMap(model.PaymentMethod)
	if err != nil {
		ch <- &SendOrderFulfillmentReconciliationServiceResponse{Error: err}
		return
	}

	_, paymentTypeId, err := s.GetPaymentMethodIds(paymentMethodName)
	if err != nil {
		ch <- &SendOrderFulfillmentReconciliationServiceResponse{Error: err}
		return
	}

	if paymentTypeId == enum.PaymentTypeOffline {
		orderPaymentCh := make(chan *orderDb.GetOrderPaymentResponse)
		defer close(orderPaymentCh)
		go s.OrderDb.GetOrderPayment(
			orderPaymentCh, &orderDb.GetOrderPaymentModel{
				OrderId:              model.OrderId,
				PaymentOperationType: paymentOperationType,
				TransactionId:        "",
			},
		)

		orderPaymentResponse := <-orderPaymentCh
		if orderPaymentResponse.Error != nil {
			ch <- &SendOrderFulfillmentReconciliationServiceResponse{Error: orderPaymentResponse.Error}
			return
		}

		if orderPaymentResponse.PaymentId == 0 {
			ch <- &SendOrderFulfillmentReconciliationServiceResponse{Error: errors.New("could not find order to reconciliation")}
			return
		}

		if orderPaymentResponse.Status == enum.PaymentStatusWaitingForReconciliation {
			if model.CollectFromCustomer > 0 {
				orderCollectionModel := logistic.SendOrderCollectionModel{
					VendorName:           enum.DefaultVendorName,
					VendorTransactionId:  orderPaymentResponse.PaymentId,
					VendorTrackingNumber: model.OrderId,
					Amount:               model.CollectFromCustomer,
					TransactionType:      enum.PaymentOperationTypeGetName(paymentOperationType),
					PaymentGroupType:     enum.PaymentGroupTypeFull,
					PaymentMethodName:    paymentMethodName,
					StoreId:              model.WarehouseId,
				}

				s.loggr.Info(
					"Sending order to LogisticPos. TrackingNumber: "+model.OrderId,
					zap.Any("SendOrderToLogisticPos", orderCollectionModel),
				)

				sendOrderCollectionCh := make(chan *logistic.LogisticPosProxyBaseResponse)
				defer close(sendOrderCollectionCh)
				go s.LogisticPosProxy.SendOrderCollection(sendOrderCollectionCh, &orderCollectionModel)

				SendOrderCollectionResponse := <-sendOrderCollectionCh
				if SendOrderCollectionResponse.Error != nil {
					s.loggr.Error(
						"Failed to sending order to LogisticPos. TrackingNumber: "+model.OrderId,
						zap.Any("Model", model),
						zap.Error(SendOrderCollectionResponse.Error),
					)
					ch <- &SendOrderFulfillmentReconciliationServiceResponse{
						Error: SendOrderCollectionResponse.Error,
					}
					return
				}
			}
			updateOrderPaymentCh := make(chan *orderDb.UpdateOrderPaymentResponse)
			defer close(updateOrderPaymentCh)

			go s.OrderDb.UpdateOrderPayment(
				updateOrderPaymentCh, &orderDb.UpdateOrderPaymentModel{
					OrderId:   model.OrderId,
					PaymentId: orderPaymentResponse.PaymentId,
					Amount:    model.CollectFromCustomer,
				},
			)

			updateOrderPaymentResponse := <-updateOrderPaymentCh
			if updateOrderPaymentResponse.Error != nil {
				ch <- &SendOrderFulfillmentReconciliationServiceResponse{
					Error: updateOrderPaymentResponse.Error,
				}
				return
			}
			markPaymentCh := make(chan *orderDb.MarkOrderPaymentAsDeliveredResponse)
			defer close(markPaymentCh)
			go s.OrderDb.MarkOrderPaymentAsDelivered(
				markPaymentCh, &orderDb.MarkOrderPaymentAsDeliveredModel{
					OrderId:   model.OrderId,
					PaymentId: orderPaymentResponse.PaymentId,
				},
			)

			markPaymentResponse := <-markPaymentCh
			if markPaymentResponse.Error != nil {
				ch <- &SendOrderFulfillmentReconciliationServiceResponse{
					Error: markPaymentResponse.Error,
				}
				return
			}
		}
	}

	ch <- &SendOrderFulfillmentReconciliationServiceResponse{Error: nil}
}

func (s *OrderService) SendOfflineYsOrderToLogistic(ch chan *SendOfflineYsOrderToLogisticServiceResponse, model *SendOfflineYsOrderToLogisticServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOfflineYsOrderToLogisticServiceResponse{Error: modelErr}
		return
	}

	getOrderCh := make(chan *orderDb.GetMahalleValeOrderResponse)
	defer close(getOrderCh)
	go s.OrderDb.GetMahalleValeOrder(
		getOrderCh, &orderDb.GetMahalleValeOrderModel{
			OrderId: model.OrderId,
		},
	)

	getOrderResponse := <-getOrderCh

	if getOrderResponse.Error != nil {
		ch <- &SendOfflineYsOrderToLogisticServiceResponse{
			Error: getOrderResponse.Error,
		}
		return
	}

	if getOrderResponse.IsSendToLogistic {
		ch <- &SendOfflineYsOrderToLogisticServiceResponse{
			Error: nil,
		}
		return
	}

	if getOrderResponse.OrderId == "" {
		addOrderCh := make(chan *orderDb.AddMahalleValeOrderResponse)
		defer close(addOrderCh)
		go s.OrderDb.AddMahalleValeOrder(
			addOrderCh, &orderDb.AddMahalleValeOrderModel{
				OrderType:         model.OrderType,
				OrderId:           model.OrderId,
				Timestamp:         model.OrderCreatedDate,
				VendorId:          model.VendorId,
				VendorType:        model.VendorType,
				VendorName:        model.StoreName,
				UserId:            model.CustomerId,
				UserFirstName:     model.FirstName,
				UserLastName:      model.LastName,
				PaymentMethodName: model.PaymentMethod,
				Amount:            model.Amount,
			},
		)

		addOrderResponse := <-addOrderCh

		if addOrderResponse.Error != nil {
			ch <- &SendOfflineYsOrderToLogisticServiceResponse{
				Error: addOrderResponse.Error,
			}
			return
		}
	}

	paymentOperationType := enum.PaymentOperationTypeOrder

	orderCollectionModel := logistic.SendMahalleValeOrderCollectionModel{
		VendorName:           model.VendorType,
		VendorTrackingNumber: model.OrderId,
		Amount:               model.Amount,
		TransactionType:      enum.PaymentOperationTypeGetName(paymentOperationType),
		PaymentGroupType:     enum.PaymentGroupTypeFull,
		PaymentMethodName:    model.PaymentMethod,
		StoreName:            model.StoreName,
	}

	s.loggr.Info(
		"Sending "+model.VendorType+" order to LogisticPos. TrackingNumber: "+model.OrderId,
		zap.Any("SendMahalleValeOrderToLogisticPos", orderCollectionModel),
	)

	sendOrderCollectionCh := make(chan *logistic.LogisticPosProxyBaseResponse)
	defer close(sendOrderCollectionCh)
	go s.LogisticPosProxy.SendMahalleValeOrderCollection(sendOrderCollectionCh, &orderCollectionModel)

	sendOrderCollectionResponse := <-sendOrderCollectionCh
	if sendOrderCollectionResponse.Error != nil {
		s.loggr.Error(
			"Failed to sending "+model.VendorType+" order to LogisticPos. TrackingNumber: "+model.OrderId,
			zap.Any("Model", model),
			zap.Error(sendOrderCollectionResponse.Error),
		)
		ch <- &SendOfflineYsOrderToLogisticServiceResponse{
			Error: sendOrderCollectionResponse.Error,
		}
		return
	}

	markOrderCh := make(chan *orderDb.MarkMahalleValeOrderAsSentResponse)
	defer close(markOrderCh)
	go s.OrderDb.MarkMahalleValeOrderAsSent(
		markOrderCh, &orderDb.MarkMahalleValeOrderAsSentModel{
			OrderId: model.OrderId,
		},
	)

	markOrderResponse := <-markOrderCh

	if markOrderResponse.Error != nil {
		ch <- &SendOfflineYsOrderToLogisticServiceResponse{
			Error: markOrderResponse.Error,
		}
		return
	}

	ch <- &SendOfflineYsOrderToLogisticServiceResponse{Error: nil}
}

func (s *OrderService) SendOfflineYsOrderCancellationToLogistic(ch chan *SendOfflineYsOrderCancellationToLogisticServiceResponse, model *SendOfflineYsOrderCancellationToLogisticServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{Error: modelErr}
		return
	}

	getOrderCh := make(chan *orderDb.GetMahalleValeOrderResponse)
	defer close(getOrderCh)
	go s.OrderDb.GetMahalleValeOrder(
		getOrderCh, &orderDb.GetMahalleValeOrderModel{
			OrderId: model.OrderId,
		},
	)

	getOrderResponse := <-getOrderCh

	if getOrderResponse.Error != nil {
		ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{
			Error: getOrderResponse.Error,
		}
		return
	}

	if getOrderResponse.OrderId == "" || !getOrderResponse.IsSendToLogistic {
		s.loggr.Error(
			"Failed to sending mahalle-vale order cancellation to LogisticPos. TrackingNumber: "+model.OrderId,
			zap.Any("Model", model),
			zap.Error(errors.New("order not sent to LogisticPos cannot be cancelled.")),
		)
		ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{
			Error: errors.New("order not sent to LogisticPos cannot be cancelled."),
		}
		return
	}

	getCancellationOrderCh := make(chan *orderDb.GetMahalleValeOrderCancellationResponse)
	defer close(getCancellationOrderCh)
	go s.OrderDb.GetMahalleValeOrderCancellation(
		getCancellationOrderCh, &orderDb.GetMahalleValeOrderCancellationModel{
			OrderId: model.OrderId,
		},
	)

	getOrderCancellationResponse := <-getCancellationOrderCh

	if getOrderCancellationResponse.Error != nil {
		ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{
			Error: getOrderCancellationResponse.Error,
		}
		return
	}

	if getOrderCancellationResponse.IsSendToLogistic {
		ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{
			Error: nil,
		}
		return
	}

	if getOrderCancellationResponse.OrderId == "" {
		addOrderCancellationCh := make(chan *orderDb.AddMahalleValeOrderCancellationResponse)
		defer close(addOrderCancellationCh)
		go s.OrderDb.AddMahalleValeOrderCancellation(
			addOrderCancellationCh, &orderDb.AddMahalleValeOrderCancellationModel{
				OrderId:   model.OrderId,
				Timestamp: model.OrderCancellationDate,
			},
		)

		addOrderCancellationResponse := <-addOrderCancellationCh

		if addOrderCancellationResponse.Error != nil {
			ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{
				Error: addOrderCancellationResponse.Error,
			}
			return
		}
	}

	paymentOperationType := enum.PaymentOperationTypeRefund

	orderCollectionModel := logistic.SendMahalleValeOrderCollectionModel{
		VendorName:           getOrderResponse.VendorType,
		VendorTrackingNumber: model.OrderId,
		Amount:               getOrderResponse.Amount,
		TransactionType:      enum.PaymentOperationTypeGetName(paymentOperationType),
		PaymentGroupType:     enum.PaymentGroupTypeFull,
		PaymentMethodName:    getOrderResponse.PaymentMethod,
		StoreName:            getOrderResponse.StoreName,
		RiderName:            "-",
	}

	s.loggr.Info(
		"Sending "+getOrderResponse.VendorType+" order cancellation to LogisticPos. TrackingNumber: "+model.OrderId,
		zap.Any("SendMahalleValeOrderToLogisticPos", orderCollectionModel),
	)

	sendOrderCollectionCh := make(chan *logistic.LogisticPosProxyBaseResponse)
	defer close(sendOrderCollectionCh)
	go s.LogisticPosProxy.SendMahalleValeOrderCollection(sendOrderCollectionCh, &orderCollectionModel)

	sendOrderCollectionResponse := <-sendOrderCollectionCh
	if sendOrderCollectionResponse.Error != nil {
		s.loggr.Error(
			"Failed to sending "+getOrderResponse.VendorType+" order cancellation to LogisticPos. TrackingNumber: "+model.OrderId,
			zap.Any("Model", model),
			zap.Error(sendOrderCollectionResponse.Error),
		)
		ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{
			Error: sendOrderCollectionResponse.Error,
		}
		return
	}

	markOrderCancellationCh := make(chan *orderDb.MarkMahalleValeOrderCancellationAsSentResponse)
	defer close(markOrderCancellationCh)
	go s.OrderDb.MarkMahalleValeOrderCancellationAsSent(
		markOrderCancellationCh, &orderDb.MarkMahalleValeOrderCancellationAsSentModel{
			OrderId: model.OrderId,
		},
	)

	markOrderCancellationResponse := <-markOrderCancellationCh

	if markOrderCancellationResponse.Error != nil {
		ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{
			Error: markOrderCancellationResponse.Error,
		}
		return
	}

	ch <- &SendOfflineYsOrderCancellationToLogisticServiceResponse{Error: nil}
}

func (s *OrderService) SendPartialRefundOrders(ch chan *SendPartialRefundOrdersServiceResponse) {
	getPartialRefundOrdersResponseCh := make(chan *orderBq.GetPartialRefundOrdersResponse)
	defer close(getPartialRefundOrdersResponseCh)

	go s.OrderBq.GetPartialRefundOrders(getPartialRefundOrdersResponseCh)

	getPartialRefundOrdersResponse := <-getPartialRefundOrdersResponseCh
	if getPartialRefundOrdersResponse.Error != nil {
		ch <- &SendPartialRefundOrdersServiceResponse{
			Error: getPartialRefundOrdersResponse.Error,
		}
		return
	}

	if len(getPartialRefundOrdersResponse.PartialRefundOrders) == 0 {
		s.loggr.Info("SendPartialRefundOrdersToLogoJob nothing to sent.")
		ch <- &SendPartialRefundOrdersServiceResponse{Error: nil}
		return
	}

	//group by order id
	groupByOrders := make(map[string][]SendPartialRefundOrderItemModel)
	loc, _ := time.LoadLocation("Europe/Istanbul")

	for _, partialOrderBySku := range getPartialRefundOrdersResponse.PartialRefundOrders {
		if v, exists := groupByOrders[partialOrderBySku.OrderId]; exists {
			v = append(v, SendPartialRefundOrderItemModel{
				Sku:              partialOrderBySku.Sku,
				ReturnedQuantity: partialOrderBySku.ReturnedQuantity,
				ReturnedAt:       partialOrderBySku.ReturnedAt.In(loc),
			})
		}
		groupByOrders[partialOrderBySku.OrderId] = append(groupByOrders[partialOrderBySku.OrderId], SendPartialRefundOrderItemModel{
			Sku:              partialOrderBySku.Sku,
			ReturnedQuantity: partialOrderBySku.ReturnedQuantity,
			ReturnedAt:       partialOrderBySku.ReturnedAt.In(loc),
		})
	}

	var refundedOrder []logo.SendCompletedRefundModel

	for orderId, refundOrderItems := range groupByOrders {
		ofDetails, ofErr := s.getOrderFulfillmentDetails(orderId)
		if ofErr != nil {
			ch <- &SendPartialRefundOrdersServiceResponse{
				Error: ofErr,
			}
			return
		}

		// check if all items are returned
		if len(ofDetails.Items) == len(refundOrderItems) {
			continue
		}

		var refundedOrderDetail []logo.RefundedOrderDetail
		var totalLineItemsAmount float64
		var totalRefundedAmount float64

		for i, x := range refundOrderItems {
			var ofItemIndex int
			for j := range ofDetails.Items {
				if ofDetails.Items[j].SKU == x.Sku {
					ofItemIndex = j
					break
				}
			}
			if x.ReturnedQuantity < 1 {
				continue
			}

			refundedOrderDetailModel := logo.RefundedOrderDetail{
				SKU:                    x.Sku,
				Quantity:               x.ReturnedQuantity,
				LineItemId:             ofDetails.Items[ofItemIndex].ExternalId,
				LineIndex:              i + 1,
				IsBundle:               false,
				TotalListPrice:         math.Round(ofDetails.Items[ofItemIndex].OriginalPricing.UnitListPrice*float64(x.ReturnedQuantity)*10000) / 10000,
				TotalPrice:             math.Round(ofDetails.Items[ofItemIndex].CollectedPricing.UnitPrice*float64(x.ReturnedQuantity)*10000) / 10000,
				UnitListPrice:          float64(ofDetails.Items[ofItemIndex].OriginalPricing.UnitListPrice),
				ProductName:            ofDetails.Items[ofItemIndex].Name,
				OriginalTotalPrice:     math.Round(ofDetails.Items[ofItemIndex].CollectedPricing.UnitPrice*float64(x.ReturnedQuantity)*10000) / 10000,
				OriginalUnitPrice:      ofDetails.Items[ofItemIndex].OriginalPricing.UnitListPrice,
				OriginalTotalListPrice: math.Round(ofDetails.Items[ofItemIndex].OriginalPricing.UnitListPrice*float64(x.ReturnedQuantity)*10000) / 10000,
			}
			refundedOrderDetail = append(refundedOrderDetail, refundedOrderDetailModel)

			totalLineItemsAmount += refundedOrderDetailModel.TotalListPrice
			totalRefundedAmount += refundedOrderDetailModel.TotalPrice
		}

		totalLineItemsAmount = math.Round(totalLineItemsAmount*10000) / 10000

		pdBillingCh := make(chan *GetPandoraBillingMessageServiceResponse)
		defer close(pdBillingCh)

		go s.PandoraBillingService.GetPandoraBillingMessage(
			pdBillingCh, &GetPandoraBillingMessageServiceModel{
				OrderId: orderId,
				Status:  "created",
			},
		)

		pdBillingResponse := <-pdBillingCh
		if pdBillingResponse.Error != nil {
			ch <- &SendPartialRefundOrdersServiceResponse{
				Error: pdBillingResponse.Error,
			}
			return
		}
		paymentMethod, mapErr := s.GetPaymentMethodMap(pdBillingResponse.PandoraBillingMessage.PaymentMethodName)
		if mapErr != nil {
			ch <- &SendPartialRefundOrdersServiceResponse{
				Error: mapErr,
			}
			return
		}

		refundModel := logo.SendCompletedRefundModel{
			MessageId:       uuid.NewString(),
			TransactionDate: refundOrderItems[0].ReturnedAt,
			OrderInfo: logo.SendCompletedRefundOrderInfoModel{
				TrackingNumber:       orderId,
				RefundDate:           refundOrderItems[0].ReturnedAt,
				CollectedDate:        refundOrderItems[0].ReturnedAt,
				PaymentApprovedDate:  refundOrderItems[0].ReturnedAt,
				PaymentCollectedDate: refundOrderItems[0].ReturnedAt,
				PaymentList: []logo.PaymentList{
					{
						BankCode: pdBillingResponse.PandoraBillingMessage.PaymentList[0].BankCode,
						Amount:   totalRefundedAmount,
					},
				},
				PaymentMethodName: paymentMethod,
				StoreId:           ofDetails.WarehouseId,
				UserData: logo.UserData{
					UserId:         pdBillingResponse.PandoraBillingMessage.UserData.UserId,
					UserFirstName:  pdBillingResponse.PandoraBillingMessage.UserData.UserFirstName,
					UserLastName:   pdBillingResponse.PandoraBillingMessage.UserData.UserLastName,
					UserFriendlyId: pdBillingResponse.PandoraBillingMessage.UserData.UserId,
				},
				RefundedLineItemsAmount:                 totalLineItemsAmount,
				RefundedTotalAmount:                     totalRefundedAmount,
				RefundedDeliveryFee:                     0,
				RefundedTipAmount:                       0,
				RefundedCouponDiscountAmount:            0,
				RefundedBasketDiscountAmount:            0,
				RefundedDeliveryFeeCouponDiscountAmount: 0,
			},
			RefundedOrderDetail: refundedOrderDetail,
		}
		refundedOrder = append(refundedOrder, refundModel)
	}

	if len(refundedOrder) == 0 {
		s.loggr.Info("SendPartialRefundOrdersToLogoJob nothing to sent.")
		ch <- &SendPartialRefundOrdersServiceResponse{Error: nil}
		return
	}

	s.loggr.Info(
		"Sending partial refund to Logo.",
		zap.Any("SendPartialRefundOrders", refundedOrder),
	)

	sendPartialRefundCh := make(chan *logo.LogoProxyRefundedOrderBulkResponse)
	defer close(sendPartialRefundCh)

	go s.LogoProxy.SendCompletedFullRefundBulk(sendPartialRefundCh, &logo.SendCompletedRefundBulkModel{
		MessageId:       uuid.NewString(),
		TransactionDate: helper.ConvertTimeByZone(time.Now(), "Europe/Istanbul"),
		RefundedOrders:  refundedOrder,
	})

	sendPartialRefundResponse := <-sendPartialRefundCh
	if sendPartialRefundResponse.Error != nil {
		s.loggr.Error(
			"Failed to sending partial refund to Logo!",
			zap.Error(sendPartialRefundResponse.Error),
		)
		ch <- &SendPartialRefundOrdersServiceResponse{
			Error: sendPartialRefundResponse.Error,
		}
		return
	}

	ch <- &SendPartialRefundOrdersServiceResponse{Error: nil}
}
