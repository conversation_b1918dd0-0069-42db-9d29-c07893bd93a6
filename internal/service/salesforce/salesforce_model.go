package salesforce

import "time"

type SalesforceUpdateAccountServiceModel struct {
	MessageId                      string
	SalesforceGridId               string `validate:"required"`
	StoreType                      int    `validate:"gte=0,lte=2"` // 0: Store, 1: <PERSON>anchi<PERSON>, 2: FranchiseBakkal
	CommercialName                 string
	CommercialType                 int `validate:"gte=0,lte=1"` // 0: Şirket, 1: Kişi
	TaxAdministration              string
	TaxNumber                      string
	IBAN                           string
	BillingAddress                 *SalesforceAddressServiceModel
	RestaurantAddress              *SalesforceAddressServiceModel
	EmailAddress                   string
	MobilePhoneNumber              string
	PhoneNumber                    string
	StoreSiteName                  string
	ResponsibleSalesRepresentative string
	PayloadTimestamp               time.Time `validate:"required"`
	ReplayId                       int       `validate:"required"`
}

type SalesforceCreateAccountServiceModel struct {
	MessageId                      string
	SalesforceGridId               string `validate:"required"`
	StoreType                      int    `validate:"gte=0,lte=2"` // 0: Store, 1: <PERSON>an<PERSON><PERSON>, 2: FranchiseBakkal
	CommercialName                 string
	CommercialType                 int `validate:"gte=0,lte=1"` // 0: <PERSON>irk<PERSON>, 1: Kişi
	TaxAdministration              string
	TaxNumber                      string
	IBAN                           string
	BillingAddress                 *SalesforceAddressServiceModel
	RestaurantAddress              *SalesforceAddressServiceModel
	EmailAddress                   string
	MobilePhoneNumber              string
	PhoneNumber                    string
	StoreSiteName                  string
	ResponsibleSalesRepresentative string
	PayloadTimestamp               time.Time `validate:"required"`
	ReplayId                       int       `validate:"required"`
}

type SalesforceAddressServiceModel struct {
	Address1 string `validate:"required"`
	Address2 string
	Town     string
	City     string
	Country  string
	PostCode string
}

type SalesforceUpdateContactServiceModel struct {
	ReplayId          int       `validate:"required"`
	PayloadTimestamp  time.Time `validate:"required"`
	AccountGrid       string    `validate:"required"`
	EmailAddress      string
	MobilePhoneNumber string
	PhoneNumber       string
	MessageId         string
}

type SalesforceUpdateAddressServiceModel struct {
	MessageId         string
	ReplayId          int       `validate:"required"`
	PayloadTimestamp  time.Time `validate:"required"`
	AccountGrid       string    `validate:"required"`
	BillingAddress    *SalesforceAddressServiceModel
	RestaurantAddress *SalesforceAddressServiceModel
}

type SalesforceReceiverAddressModel struct {
	FormattedAddress         string   `json:"formatted_address"`
	GlobalCityLabel          string   `json:"global_city_label"`
	GlobalCountryCode        string   `json:"global_country_code"`
	GlobalCountryLabel       string   `json:"global_country_label"`
	GlobalPostalCode         string   `json:"global_postal_code"`
	GlobalStateProvinceCode  string   `json:"global_state_province_code"`
	GlobalStreetNumberCode   string   `json:"global_street_number_code"`
	GlobalStreetLabel        string   `json:"global_street_label"`
	GlobalStateProvinceLabel string   `json:"global_state_province_label"`
	GlobalCityCode           string   `json:"global_city_code"`
	Latitude                 float64  `json:"latitude"`
	Longitude                float64  `json:"longitude"`
	Type                     []string `json:"type"`
	TranslateStreetName      string   `json:"translate_street_name"`
	TranslateCityName        string   `json:"translate_city_name"`
}
