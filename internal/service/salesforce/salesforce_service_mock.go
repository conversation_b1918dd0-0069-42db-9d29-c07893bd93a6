// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/salesforce/salesforce_service.go

// Package salesforce is a generated GoMock package.
package salesforce

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISalesforceService is a mock of ISalesforceService interface.
type MockISalesforceService struct {
	ctrl     *gomock.Controller
	recorder *MockISalesforceServiceMockRecorder
}

// MockISalesforceServiceMockRecorder is the mock recorder for MockISalesforceService.
type MockISalesforceServiceMockRecorder struct {
	mock *MockISalesforceService
}

// NewMockISalesforceService creates a new mock instance.
func NewMockISalesforceService(ctrl *gomock.Controller) *MockISalesforceService {
	mock := &MockISalesforceService{ctrl: ctrl}
	mock.recorder = &MockISalesforceServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISalesforceService) EXPECT() *MockISalesforceServiceMockRecorder {
	return m.recorder
}

// CreateSalesforceAccount mocks base method.
func (m *MockISalesforceService) CreateSalesforceAccount(ch chan *CreateSalesforceAccountServiceResponse, model *SalesforceCreateAccountServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CreateSalesforceAccount", ch, model)
}

// CreateSalesforceAccount indicates an expected call of CreateSalesforceAccount.
func (mr *MockISalesforceServiceMockRecorder) CreateSalesforceAccount(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSalesforceAccount", reflect.TypeOf((*MockISalesforceService)(nil).CreateSalesforceAccount), ch, model)
}

// SendSalesforceAccountsToLogo mocks base method.
func (m *MockISalesforceService) SendSalesforceAccountsToLogo(ch chan *SendSalesforceAccountsToLogoServiceResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSalesforceAccountsToLogo", ch)
}

// SendSalesforceAccountsToLogo indicates an expected call of SendSalesforceAccountsToLogo.
func (mr *MockISalesforceServiceMockRecorder) SendSalesforceAccountsToLogo(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSalesforceAccountsToLogo", reflect.TypeOf((*MockISalesforceService)(nil).SendSalesforceAccountsToLogo), ch)
}

// UpdateSalesforceAccount mocks base method.
func (m *MockISalesforceService) UpdateSalesforceAccount(ch chan *UpdateSalesforceAccountServiceResponse, model *SalesforceUpdateAccountServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateSalesforceAccount", ch, model)
}

// UpdateSalesforceAccount indicates an expected call of UpdateSalesforceAccount.
func (mr *MockISalesforceServiceMockRecorder) UpdateSalesforceAccount(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSalesforceAccount", reflect.TypeOf((*MockISalesforceService)(nil).UpdateSalesforceAccount), ch, model)
}

// UpdateSalesforceAddress mocks base method.
func (m *MockISalesforceService) UpdateSalesforceAddress(ch chan *UpdateSalesforceAddressServiceResponse, model *SalesforceUpdateAddressServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateSalesforceAddress", ch, model)
}

// UpdateSalesforceAddress indicates an expected call of UpdateSalesforceAddress.
func (mr *MockISalesforceServiceMockRecorder) UpdateSalesforceAddress(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSalesforceAddress", reflect.TypeOf((*MockISalesforceService)(nil).UpdateSalesforceAddress), ch, model)
}

// UpdateSalesforceContact mocks base method.
func (m *MockISalesforceService) UpdateSalesforceContact(ch chan *UpdateSalesforceContactServiceResponse, model *SalesforceUpdateContactServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateSalesforceContact", ch, model)
}

// UpdateSalesforceContact indicates an expected call of UpdateSalesforceContact.
func (mr *MockISalesforceServiceMockRecorder) UpdateSalesforceContact(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSalesforceContact", reflect.TypeOf((*MockISalesforceService)(nil).UpdateSalesforceContact), ch, model)
}
