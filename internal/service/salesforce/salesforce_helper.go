package salesforce

import (
	"strings"
)

type SalesforceServiceModelHelper struct {
}

func (s *SalesforceServiceModelHelper) ExtractAddress(addresses []SalesforceReceiverAddressModel, addressType string) *SalesforceAddressServiceModel {
	for _, addr := range addresses {
		for _, addrType := range addr.Type {
			if addrType == addressType {
				return &SalesforceAddressServiceModel{
					Address1: strings.ReplaceAll(addr.FormattedAddress, "\n", " "),
					Address2: "",
					Town:     addr.GlobalStateProvinceLabel,
					City:     addr.GlobalCityLabel,
					Country:  addr.GlobalCountryLabel,
					PostCode: addr.GlobalPostalCode,
				}
			}
		}
	}

	return nil
}
