package salesforce

import (
	"github.com/google/uuid"
	"logo-adapter/internal/data/database/salesforce"
	"logo-adapter/internal/data/proxy/datafridge"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type ISalesforceService interface {
	CreateSalesforceAccount(ch chan *CreateSalesforceAccountServiceResponse, model *SalesforceCreateAccountServiceModel)
	UpdateSalesforceAccount(ch chan *UpdateSalesforceAccountServiceResponse, model *SalesforceUpdateAccountServiceModel)
	UpdateSalesforceContact(ch chan *UpdateSalesforceContactServiceResponse, model *SalesforceUpdateContactServiceModel)
	UpdateSalesforceAddress(ch chan *UpdateSalesforceAddressServiceResponse, model *SalesforceUpdateAddressServiceModel)
	SendSalesforceAccountsToLogo(ch chan *SendSalesforceAccountsToLogoServiceResponse)
}

type SalesforceService struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	salesforceDb          salesforce.ISalesforceDb
	dataFridgeVendorProxy datafridge.IVendorProxy
	logoProxy             logo.ILogoProxy
}

// NewSalesforceService
// Returns a new SalesforceService.
func NewSalesforceService(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	salesforceDb salesforce.ISalesforceDb,
	dataFridgeVendorProxy datafridge.IVendorProxy,
	logoProxy logo.ILogoProxy,
) ISalesforceService {
	service := SalesforceService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if salesforceDb != nil {
		service.salesforceDb = salesforceDb
	} else {
		service.salesforceDb = salesforce.NewSalesForceDb(environment, loggr, validatr, cachr)
	}

	if dataFridgeVendorProxy != nil {
		service.dataFridgeVendorProxy = dataFridgeVendorProxy
	} else {
		service.dataFridgeVendorProxy = datafridge.NewVendorProxy(environment, loggr, validatr, cachr)
	}

	if logoProxy != nil {
		service.logoProxy = logoProxy
	} else {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	return &service
}

// CreateSalesforceAccount
// Adds or updates warehouse's Salesforce info.
func (s *SalesforceService) CreateSalesforceAccount(ch chan *CreateSalesforceAccountServiceResponse, model *SalesforceCreateAccountServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &CreateSalesforceAccountServiceResponse{Error: modelErr}
		return
	}

	whDbCh := make(chan *salesforce.UpsertSalesforceAccountDbResponse)
	defer close(whDbCh)

	dbModel := salesforce.UpsertSalesforceAccountDbModel{
		ReplayId:                       model.ReplayId,
		PayloadTimestamp:               model.PayloadTimestamp,
		SalesforceGridId:               model.SalesforceGridId,
		WarehouseType:                  model.StoreType,
		CommercialName:                 model.CommercialName,
		CommercialType:                 model.CommercialType,
		TaxAdministration:              model.TaxAdministration,
		TaxNumber:                      model.TaxNumber,
		IBAN:                           model.IBAN,
		EmailAddress:                   model.EmailAddress,
		MobilePhoneNumber:              model.MobilePhoneNumber,
		PhoneNumber:                    model.PhoneNumber,
		StoreSiteName:                  model.StoreSiteName,
		ResponsibleSalesRepresentative: model.ResponsibleSalesRepresentative,
		IsCreate:                       true,
		IsUpdate:                       false,
	}

	if model.BillingAddress != nil {
		dbModel.BillingAddressLine1 = model.BillingAddress.Address1
		dbModel.BillingAddressLine2 = model.BillingAddress.Address2
		dbModel.BillingAddressTown = model.BillingAddress.Town
		dbModel.BillingAddressCity = model.BillingAddress.City
		dbModel.BillingAddressCountry = model.BillingAddress.Country
		dbModel.BillingAddressPostCode = model.BillingAddress.PostCode
	}

	if model.RestaurantAddress != nil {
		dbModel.RestaurantAddressLine1 = model.RestaurantAddress.Address1
		dbModel.RestaurantAddressLine2 = model.RestaurantAddress.Address2
		dbModel.RestaurantAddressTown = model.RestaurantAddress.Town
		dbModel.RestaurantAddressCity = model.RestaurantAddress.City
		dbModel.RestaurantAddressCountry = model.RestaurantAddress.Country
		dbModel.RestaurantAddressPostCode = model.RestaurantAddress.PostCode
	}

	go s.salesforceDb.UpsertSalesforceAccount(whDbCh, &dbModel)

	whDbResponse := <-whDbCh
	if whDbResponse.Error != nil {
		ch <- &CreateSalesforceAccountServiceResponse{Error: whDbResponse.Error}
		return
	}

	ch <- &CreateSalesforceAccountServiceResponse{Error: nil}
	return
}

// UpdateSalesforceAccount
// Updates warehouse's Salesforce info.
func (s *SalesforceService) UpdateSalesforceAccount(ch chan *UpdateSalesforceAccountServiceResponse, model *SalesforceUpdateAccountServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateSalesforceAccountServiceResponse{Error: modelErr}
		return
	}

	whDbCh := make(chan *salesforce.UpsertSalesforceAccountDbResponse)
	defer close(whDbCh)

	go s.salesforceDb.UpsertSalesforceAccount(
		whDbCh, &salesforce.UpsertSalesforceAccountDbModel{
			ReplayId:                       model.ReplayId,
			PayloadTimestamp:               model.PayloadTimestamp,
			SalesforceGridId:               model.SalesforceGridId,
			WarehouseType:                  model.StoreType,
			CommercialName:                 model.CommercialName,
			CommercialType:                 model.CommercialType,
			TaxAdministration:              model.TaxAdministration,
			TaxNumber:                      model.TaxNumber,
			IBAN:                           model.IBAN,
			StoreSiteName:                  model.StoreSiteName,
			ResponsibleSalesRepresentative: model.ResponsibleSalesRepresentative,
			IsCreate:                       false,
			IsUpdate:                       true,
		},
	)

	whDbResponse := <-whDbCh
	if whDbResponse.Error != nil {
		ch <- &UpdateSalesforceAccountServiceResponse{Error: whDbResponse.Error}
		return
	}

	ch <- &UpdateSalesforceAccountServiceResponse{Error: nil}
	return
}

// UpdateSalesforceContact
// Adds or updates a salesforce account's contact info.
func (s *SalesforceService) UpdateSalesforceContact(ch chan *UpdateSalesforceContactServiceResponse, model *SalesforceUpdateContactServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateSalesforceContactServiceResponse{Error: modelErr}
		return
	}

	dbCh := make(chan *salesforce.UpdateSalesforceContactDbResponse)
	defer close(dbCh)

	go s.salesforceDb.UpdateSalesforceContact(
		dbCh, &salesforce.UpdateSalesforceContactDbModel{
			MessageId:         model.MessageId,
			ReplayId:          model.ReplayId,
			PayloadTimestamp:  model.PayloadTimestamp,
			SalesforceGridId:  model.AccountGrid,
			EmailAddress:      model.EmailAddress,
			MobilePhoneNumber: model.MobilePhoneNumber,
			PhoneNumber:       model.PhoneNumber,
		},
	)

	whDbResponse := <-dbCh
	if whDbResponse.Error != nil {
		ch <- &UpdateSalesforceContactServiceResponse{Error: whDbResponse.Error}
		return
	}

	ch <- &UpdateSalesforceContactServiceResponse{Error: nil}
	return
}

// UpdateSalesforceAddress
// Adds or updates a salesforce account's Address info.
func (s *SalesforceService) UpdateSalesforceAddress(ch chan *UpdateSalesforceAddressServiceResponse, model *SalesforceUpdateAddressServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateSalesforceAddressServiceResponse{Error: modelErr}
		return
	}

	dbCh := make(chan *salesforce.UpdateSalesforceAddressDbResponse)
	defer close(dbCh)

	dbModel := salesforce.UpdateSalesforceAddressDbModel{
		MessageId:        model.MessageId,
		ReplayId:         model.ReplayId,
		PayloadTimestamp: model.PayloadTimestamp,
		SalesforceGridId: model.AccountGrid,
	}

	if model.BillingAddress != nil {
		dbModel.BillingAddressLine1 = model.BillingAddress.Address1
		dbModel.BillingAddressLine2 = model.BillingAddress.Address1
		dbModel.BillingAddressTown = model.BillingAddress.Town
		dbModel.BillingAddressCity = model.BillingAddress.City
		dbModel.BillingAddressCountry = model.BillingAddress.Country
		dbModel.BillingAddressPostCode = model.BillingAddress.PostCode
	}

	if model.RestaurantAddress != nil {
		dbModel.RestaurantAddressLine1 = model.RestaurantAddress.Address1
		dbModel.RestaurantAddressLine2 = model.RestaurantAddress.Address1
		dbModel.RestaurantAddressTown = model.RestaurantAddress.Town
		dbModel.RestaurantAddressCity = model.RestaurantAddress.City
		dbModel.RestaurantAddressCountry = model.RestaurantAddress.Country
		dbModel.RestaurantAddressPostCode = model.RestaurantAddress.PostCode
	}

	go s.salesforceDb.UpdateSalesforceAddress(dbCh, &dbModel)

	whDbResponse := <-dbCh
	if whDbResponse.Error != nil {
		ch <- &UpdateSalesforceAddressServiceResponse{Error: whDbResponse.Error}
		return
	}

	ch <- &UpdateSalesforceAddressServiceResponse{Error: nil}
	return
}

// SendSalesforceAccountsToLogo
// Sends salesforce accounts to Logo.
func (s *SalesforceService) SendSalesforceAccountsToLogo(ch chan *SendSalesforceAccountsToLogoServiceResponse) {
	whDbCh := make(chan *salesforce.GetUndeliveredSalesforceAccountsResponse)
	defer close(whDbCh)
	go s.salesforceDb.GetUndeliveredSalesforceAccounts(whDbCh)

	dbResponse := <-whDbCh
	if dbResponse.Error != nil {
		ch <- &SendSalesforceAccountsToLogoServiceResponse{Error: dbResponse.Error}
		return
	}

	if len(dbResponse.UndeliveredSalesforceAccounts) < 1 {
		ch <- &SendSalesforceAccountsToLogoServiceResponse{Error: nil, DeliveredCount: 0}
		return
	}

	salesforceAccounts := make([]logo.SalesforceAccount, len(dbResponse.UndeliveredSalesforceAccounts))

	for i, x := range dbResponse.UndeliveredSalesforceAccounts {
		salesforceAccounts[i] = logo.SalesforceAccount{
			SalesforceGridId:  x.SalesforceGridId,
			StoreType:         x.StoreType,
			CommercialName:    x.CommercialName,
			CommercialType:    x.CommercialType,
			TaxAdministration: x.TaxAdministration,
			TaxNumber:         x.TaxNumber,
			IBAN:              x.IBAN,
			BillingAddress: &logo.SalesforceAddress{
				Address1: x.BillingAddressLine1,
				Address2: x.BillingAddressLine2,
				Town:     x.BillingAddressTown,
				City:     x.BillingAddressCity,
				Country:  x.BillingAddressCountry,
				PostCode: x.BillingAddressPostCode,
			},
			RestaurantAddress: &logo.SalesforceAddress{
				Address1: x.RestaurantAddressLine1,
				Address2: x.RestaurantAddressLine2,
				Town:     x.RestaurantAddressCity,
				City:     x.RestaurantAddressCity,
				Country:  x.RestaurantAddressCountry,
				PostCode: x.RestaurantAddressPostCode,
			},
			EmailAddress:                   x.EmailAddress,
			MobilePhoneNumber:              x.MobilePhoneNumber,
			PhoneNumber:                    x.PhoneNumber,
			StoreSiteName:                  x.StoreSiteName,
			InvoiceDeliveryType:            "E-mail",
			ResponsibleSalesRepresentative: x.ResponsibleSalesRepresentative,
		}
	}

	logoProxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(logoProxyCh)

	go s.logoProxy.SendSalesforceAccounts(logoProxyCh, &logo.SendSalesforceEventProxyModel{
		MessageId:   uuid.NewString(),
		SalesForces: salesforceAccounts,
	})

	logoProxyResponse := <-logoProxyCh

	if logoProxyResponse.Error != nil {
		ch <- &SendSalesforceAccountsToLogoServiceResponse{Error: logoProxyResponse.Error}
		return
	}

	ids := make([]int, len(dbResponse.UndeliveredSalesforceAccounts))
	for i, x := range dbResponse.UndeliveredSalesforceAccounts {
		ids[i] = x.Id
	}

	whDbMarkCh := make(chan *salesforce.MarkSalesforceAccountsAsDeliveredResponse)
	defer close(whDbMarkCh)
	go s.salesforceDb.MarkSalesforceAccountsDelivered(whDbMarkCh, &salesforce.MarkSalesforceAccountsAsDeliveredModel{
		Ids: ids,
	})

	whDbMarkResponse := <-whDbMarkCh

	if whDbMarkResponse.Error != nil {
		ch <- &SendSalesforceAccountsToLogoServiceResponse{Error: whDbMarkResponse.Error}
		return
	}

	ch <- &SendSalesforceAccountsToLogoServiceResponse{Error: nil, DeliveredCount: len(ids)}
	return
}
