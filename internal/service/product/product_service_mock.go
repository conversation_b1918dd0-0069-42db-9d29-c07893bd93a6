// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/product/product_service.go

// Package product is a generated GoMock package.
package product

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIProductService is a mock of IProductService interface.
type MockIProductService struct {
	ctrl     *gomock.Controller
	recorder *MockIProductServiceMockRecorder
}

// MockIProductServiceMockRecorder is the mock recorder for MockIProductService.
type MockIProductServiceMockRecorder struct {
	mock *MockIProductService
}

// NewMockIProductService creates a new mock instance.
func NewMockIProductService(ctrl *gomock.Controller) *MockIProductService {
	mock := &MockIProductService{ctrl: ctrl}
	mock.recorder = &MockIProductServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIProductService) EXPECT() *MockIProductServiceMockRecorder {
	return m.recorder
}

// GetAllProductsForPricingTool mocks base method.
func (m *MockIProductService) GetAllProductsForPricingTool(ch chan GetAllProductsForPricingToolServiceResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetAllProductsForPricingTool", ch)
}

// GetAllProductsForPricingTool indicates an expected call of GetAllProductsForPricingTool.
func (mr *MockIProductServiceMockRecorder) GetAllProductsForPricingTool(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllProductsForPricingTool", reflect.TypeOf((*MockIProductService)(nil).GetAllProductsForPricingTool), ch)
}

// GetProductBySku mocks base method.
func (m *MockIProductService) GetProductBySku(ch chan GetProductBySkuServiceResponse, sku string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProductBySku", ch, sku)
}

// GetProductBySku indicates an expected call of GetProductBySku.
func (mr *MockIProductServiceMockRecorder) GetProductBySku(ch, sku interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductBySku", reflect.TypeOf((*MockIProductService)(nil).GetProductBySku), ch, sku)
}

// GetProductsBySku mocks base method.
func (m *MockIProductService) GetProductsBySku(ch chan GetProductsBySkuServiceResponse, skus []string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProductsBySku", ch, skus)
}

// GetProductsBySku indicates an expected call of GetProductsBySku.
func (mr *MockIProductServiceMockRecorder) GetProductsBySku(ch, skus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductsBySku", reflect.TypeOf((*MockIProductService)(nil).GetProductsBySku), ch, skus)
}

// SendProductsToLogo mocks base method.
func (m *MockIProductService) SendProductsToLogo(ch chan SendProductsToLogoServiceResponse, model SendProductsToLogoServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendProductsToLogo", ch, model)
}

// SendProductsToLogo indicates an expected call of SendProductsToLogo.
func (mr *MockIProductServiceMockRecorder) SendProductsToLogo(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendProductsToLogo", reflect.TypeOf((*MockIProductService)(nil).SendProductsToLogo), ch, model)
}
