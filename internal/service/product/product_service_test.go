package product

import (
	"errors"
	"testing"
	"time"

	"logo-adapter/internal/data/database/product"
	productDb "logo-adapter/internal/data/database/product"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ProductServiceTestSuite struct {
	suite.Suite
	productService          IProductService
	mockEnvironment         *env.MockIEnvironment
	mockLogger              *logger.MockILogger
	mockValidator           *validator.MockIValidator
	mockCacher              *cacher.MockICacher
	mockLogoProxy           *logo.MockILogoProxy
	mockProductDb           *productDb.MockIProductDb
	mockServerConfiguration *configuration.MockIServerConfigurationService
}

func TestProductService(t *testing.T) {
	suite.Run(t, new(ProductServiceTestSuite))
}

func (p *ProductServiceTestSuite) SetupTest() {
	p.T().Log("Setup")

	ctrl := gomock.NewController(p.T())
	defer ctrl.Finish()

	p.mockEnvironment = env.NewMockIEnvironment(ctrl)
	p.mockLogger = logger.NewMockILogger(ctrl)
	p.mockValidator = validator.NewMockIValidator(ctrl)
	p.mockCacher = cacher.NewMockICacher(ctrl)
	p.mockLogoProxy = logo.NewMockILogoProxy(ctrl)
	p.mockProductDb = productDb.NewMockIProductDb(ctrl)
	p.mockServerConfiguration = configuration.NewMockIServerConfigurationService(ctrl)

	p.mockLogger.EXPECT().With(gomock.Any()).Return(p.mockLogger)

	p.productService = New(p.mockEnvironment, p.mockLogger, p.mockValidator, p.mockCacher, p.mockLogoProxy, p.mockProductDb, p.mockServerConfiguration)
}

func (p *ProductServiceTestSuite) TearDownTest() {
	p.T().Log("Teardown")
}

func (p *ProductServiceTestSuite) TestSendProductsToLogo_HappyPath_Success() {
	// Given
	model := SendProductsToLogoServiceModel{
		Content: Content{
			ProductId: "98495ae3-24e7-410c-9001-fd2dacc12a50",
			Name:      "Simply Fresh Çilekli Limonata 330 ml",
			Names:     []Name{{Locale: "", Value: ""}},
			Attributes: []Attribute{
				{AttributeId: "banabiProductId", Name: "98495ae3-24e7-410c-9001-fd2dacc12a50", Names: []Name{{Locale: "", Value: ""}}},
				{AttributeId: "productSegment", Name: "launch", Names: []Name{{Locale: "", Value: ""}}},
				{AttributeId: "sku", Name: "MRK.06636", Names: []Name{{Locale: "", Value: ""}}}},
			Timestamp: time.Now(),
		},
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(model)).
		Return(nil)

	p.mockLogger.
		EXPECT().
		Error(gomock.Any())

	p.mockLogger.
		EXPECT().
		Info(gomock.Any(), gomock.Any(), gomock.Any())

	p.mockCacher.
		EXPECT().
		Get(gomock.Any()).
		AnyTimes()

	p.mockCacher.
		EXPECT().
		Set(gomock.Any(), gomock.Any(), gomock.Any()).
		AnyTimes()

	p.mockServerConfiguration.
		EXPECT().
		Get(gomock.Any())

	p.mockProductDb.
		EXPECT().
		UpsertProductStream(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan error, model productDb.UpsertProductStreamModel) {
			ch <- nil
			return
		})

	p.mockLogoProxy.
		EXPECT().
		SendProducts(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendProductsModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil}
			return
		})

	// When
	serviceCh := make(chan SendProductsToLogoServiceResponse)

	defer close(serviceCh)

	go p.productService.SendProductsToLogo(serviceCh, model)

	response := <-serviceCh

	// Then
	p.Nil(response.Error)
}

func (p *ProductServiceTestSuite) TestSendProductsToLogo_ModelHasValidationError_ReturnsError() {
	// Given
	model := SendProductsToLogoServiceModel{
		Content: Content{
			Name:       "Simply Fresh Çilekli Limonata 330 ml",
			Names:      []Name{{Locale: "en-TR", Value: "Simply Fresh Cilekli Limonata 330 ml"}, {Locale: "tr-TR", Value: "Simply Fresh Çilekli Limonata 330 ml"}},
			Attributes: []Attribute{{AttributeId: "chainProductId", Name: "5703496", Names: []Name{}}},
			Timestamp:  time.Now(),
		},
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(model)).
		Return(errors.New("field validation for 'ProductId' failed"))

	// When
	serviceCh := make(chan SendProductsToLogoServiceResponse)

	defer close(serviceCh)

	go p.productService.SendProductsToLogo(serviceCh, model)

	response := <-serviceCh

	// Then
	p.EqualError(response.Error, "field validation for 'ProductId' failed")
}

func (p *ProductServiceTestSuite) TestSendProductsToLogo_FindAttributeError_ReturnsError() {
	// Given
	model := SendProductsToLogoServiceModel{
		Content: Content{
			ProductId:  "",
			Name:       "Simply Fresh Çilekli Limonata 330 ml",
			Names:      []Name{{Locale: "en-TR", Value: "Simply Fresh Cilekli Limonata 330 ml"}, {Locale: "tr-TR", Value: "Simply Fresh Çilekli Limonata 330 ml"}},
			Attributes: []Attribute{{AttributeId: "chainProductId", Name: "5703496", Names: []Name{}}},
			Timestamp:  time.Now(),
		},
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(model)).
		Return(errors.New("Reason 15 0 Couldn't find 'banabiProductId' field on attributes <nil>"))

	p.mockLogger.
		EXPECT().
		Error(gomock.Any())

	p.mockLogger.
		EXPECT().
		Info(gomock.Any())

	p.mockCacher.
		EXPECT().
		Get(gomock.Any()).
		AnyTimes()

	// When
	serviceCh := make(chan SendProductsToLogoServiceResponse)

	defer close(serviceCh)

	go p.productService.SendProductsToLogo(serviceCh, model)

	response := <-serviceCh

	// Then
	p.NotNil(response.Error)
	p.EqualError(response.Error, "Reason 15 0 Couldn't find 'banabiProductId' field on attributes <nil>")
}

func (p *ProductServiceTestSuite) TestGetAllProductsForPricingTool_HappyPath_Success() {
	p.mockProductDb.
		EXPECT().
		GetAllProductsStream(gomock.Any()).
		DoAndReturn(func(ch chan *product.GetAllProductStreamResponse) {
			ch <- &product.GetAllProductStreamResponse{
				Error: nil,
				AllProductsStream: []productDb.Product{
					{
						Sku:         "MRK.00001",
						Name:        "test",
						Price:       12.5,
						ListPrice:   10.5,
						StockAmount: 1,
					},
					{
						Sku:         "MRK.00002",
						Name:        "test2",
						Price:       10.5,
						ListPrice:   8,
						StockAmount: 0,
					},
				},
			}
		})

	// When
	serviceCh := make(chan GetAllProductsForPricingToolServiceResponse)

	defer close(serviceCh)

	go p.productService.GetAllProductsForPricingTool(serviceCh)

	response := <-serviceCh

	// Then
	p.Nil(response.Error)
}

func (p *ProductServiceTestSuite) TestGetAllProductsForPricingTool_NoProductsFound_ReturnsError() {
	p.mockProductDb.
		EXPECT().
		GetAllProductsStream(gomock.Any()).
		DoAndReturn(func(ch chan *product.GetAllProductStreamResponse) {
			ch <- &product.GetAllProductStreamResponse{
				Error:             nil,
				AllProductsStream: []productDb.Product{},
			}
		})

	// When
	serviceCh := make(chan GetAllProductsForPricingToolServiceResponse)

	defer close(serviceCh)

	go p.productService.GetAllProductsForPricingTool(serviceCh)

	response := <-serviceCh

	// Then
	p.Error(response.Error)
}

func (p *ProductServiceTestSuite) TestGetAllProductsForPricingTool_GetAllProductStreamFromDb_ReturnsError() {
	p.mockProductDb.
		EXPECT().
		GetAllProductsStream(gomock.Any()).
		DoAndReturn(func(ch chan *product.GetAllProductStreamResponse) {
			ch <- &product.GetAllProductStreamResponse{
				Error: errors.New("Error"),
			}
		})

	// When
	serviceCh := make(chan GetAllProductsForPricingToolServiceResponse)

	defer close(serviceCh)

	go p.productService.GetAllProductsForPricingTool(serviceCh)

	response := <-serviceCh

	// Then
	p.Error(response.Error)
}
