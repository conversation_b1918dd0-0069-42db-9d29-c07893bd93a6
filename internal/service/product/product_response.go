package product

type SendProductsToLogoServiceResponse struct {
	Error          error
	SendProductIds []string
}

type GetProductBySkuServiceResponse struct {
	Error   error
	Message ProductMessage
}

type GetProductsBySkuServiceResponse struct {
	Error    error
	Messages map[string]ProductMessage
}

type ProductMessage struct {
	ProductID         string   `json:"ProductId"`
	Sku               string   `json:"SKU"`
	ProductName       string   `json:"ProductName"`
	BarcodeNumberList []string `json:"BarcodeNumberList"`
	SalesVat          float64  `json:"SalesVat"`
	PurchaseVat       float64  `json:"PurchaseVat"`
	Segment           string   `json:"Segment"`
	Width             float64  `json:"Width"`
	Height            float64  `json:"Height"`
	Length            float64  `json:"Length"`
	GrossWeight       float64  `json:"GrossWeight"`
	UnitPerPackage    float64  `json:"UnitPerPackage"`
	IsCoffeeProduct   bool     `json:"IsCoffeeProduct"`
}

type GetAllProductsForPricingToolServiceResponse struct {
	Error       error
	AllProducts []PricingToolProduct
}

type PricingToolProduct struct {
	Sku         string
	Name        string
	StockAmount int
	Price       float64
	ListPrice   float64
}

func (r GetProductsBySkuServiceResponse) GetSkus() []string {
	var skus []string
	for _, product := range r.Messages {
		skus = append(skus, product.Sku)
	}
	return skus
}
