package product

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"logo-adapter/internal/data/database/product"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
)

type IProductService interface {
	SendProductsToLogo(ch chan SendProductsToLogoServiceResponse, model SendProductsToLogoServiceModel)
	GetProductBySku(ch chan GetProductBySkuServiceResponse, sku string)
	GetProductsBySku(ch chan GetProductsBySkuServiceResponse, skus []string)
	GetAllProductsForPricingTool(ch chan GetAllProductsForPricingToolServiceResponse)
}

type ProductService struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	logoProxy            logo.ILogoProxy
	productDb            product.IProductDb
	serverConfigService  configuration.IServerConfigurationService
	cacheKeyPrefix       string
	defaultCacheDuration int
}

const (
	isBanabiProduct = "isBanabiProduct"
	banabiProductId = "banabiProductId"
	sku             = "sku"
	productSegment  = "productSegment"
	vatRate         = "vatRate"
	purchaseVatRate = "purchaseVatRate"
	weightValue     = "weightValue"
	heightInCm      = "heightInCm"
	lengthInCm      = "lengthInCm"
	widthInCm       = "widthInCm"
	qtyPerBox       = "qtyPerBox"
	pieceBarcodes   = "pieceBarcodes"
	isCoffeeProduct = "isCoffeeProduct"
	price           = "originalPrice"
	isBundle        = "is_bundle"
)

func New(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	logoProxy logo.ILogoProxy,
	productDb product.IProductDb,
	serverConfigService configuration.IServerConfigurationService,
) IProductService {
	service := ProductService{
		environment: environment,
		loggr: loggr.With(
			zap.String("serviceName", "ProductService"),
		),
		validatr:             validatr,
		cachr:                cachr,
		cacheKeyPrefix:       "product-stream-master-product",
		defaultCacheDuration: 30,
	}

	if logoProxy != nil {
		service.logoProxy = logoProxy
	} else {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	if productDb != nil {
		service.productDb = productDb
	} else {
		service.productDb = product.NewProductDb(environment, loggr, validatr, cachr)
	}

	if serverConfigService != nil {
		service.serverConfigService = serverConfigService
	} else {
		service.serverConfigService = configuration.NewServerConfigurationService(
			environment,
			loggr,
			validatr,
			cachr,
			nil,
		)
	}

	return &service
}

func (p *ProductService) SendProductsToLogo(
	ch chan SendProductsToLogoServiceResponse,
	model SendProductsToLogoServiceModel,
) {
	validationErr := p.validatr.ValidateStruct(model)
	if validationErr != nil {
		ch <- SendProductsToLogoServiceResponse{
			Error: validationErr,
		}
		return
	}

	banabiProductId := model.Content.FindAttribute(banabiProductId).Name
	if banabiProductId == "" {
		errorMessage := fmt.Sprintf("Failed to send product to Logo (%s)", model.Content.Name)
		infoMessage := fmt.Sprintf("Bundle product is not sent to Logo (%s)", model.Content.Name)

		brand := model.Content.FindAttribute("brand")
		if brand != nil && brand.Name != "Poşet" {
			isBundleProduct, _ := strconv.ParseBool(model.Content.FindAttribute(isBundle).Name)
			if isBundleProduct {
				p.loggr.Info(
					infoMessage,
					zap.String("Reason", "Couldn't find 'banabiProductId' field on attributes"),
					zap.Any("Model", model),
				)
			} else {
				p.loggr.Error(
					errorMessage,
					zap.String("Reason", "Couldn't find 'banabiProductId' field on attributes"),
					zap.Any("Model", model),
				)
			}
		}

		ch <- SendProductsToLogoServiceResponse{
			Error: nil,
		}
		return
	}

	sku := model.Content.FindAttribute(sku).Name
	if sku == "" {
		message := fmt.Sprintf("Failed to send product (%s) to logo", model.Content.Name)
		p.loggr.Error(message, zap.String("Reason", "Couldn't find 'sku' field on attributes"), zap.Any("Model", model))

		ch <- SendProductsToLogoServiceResponse{
			Error: nil,
		}
		return
	}

	segment := model.Content.FindAttribute(productSegment).Name
	if segment == "" {
		message := fmt.Sprintf("Failed to send product (%s) to logo", model.Content.Name)
		p.loggr.Error(
			message,
			zap.String("Reason", "Couldn't find 'segment' field on attributes"),
			zap.Any("Model", model),
		)

		ch <- SendProductsToLogoServiceResponse{
			Error: nil,
		}
		return
	}

	salesVat, _ := strconv.ParseFloat(model.Content.FindAttribute(vatRate).Name, 64)

	purchaseVat, _ := strconv.ParseFloat(model.Content.FindAttribute(purchaseVatRate).Name, 64)

	weight, _ := strconv.ParseFloat(model.Content.FindAttribute(weightValue).Name, 64)

	height, _ := strconv.ParseFloat(model.Content.FindAttribute(heightInCm).Name, 64)

	length, _ := strconv.ParseFloat(model.Content.FindAttribute(lengthInCm).Name, 64)

	width, _ := strconv.ParseFloat(model.Content.FindAttribute(widthInCm).Name, 64)

	qtyPerBox, _ := strconv.ParseFloat(model.Content.FindAttribute(qtyPerBox).Name, 64)

	isCoffeeProduct, _ := strconv.ParseBool(model.Content.FindAttribute(isCoffeeProduct).Name)

	price, _ := strconv.ParseFloat(model.Content.FindAttribute(price).Name, 64)

	barcodeAttr := model.Content.FindAttribute(pieceBarcodes)
	var barcodeList []string
	if len(barcodeAttr.Names) > 0 {
		for _, barcode := range barcodeAttr.Names {
			barcodeList = append(barcodeList, barcode.Value)
		}
	} else {
		barcodeList = append(barcodeList, barcodeAttr.Name)
	}

	productToSendLogo := logo.SendProductsProduct{
		ProductId:         banabiProductId,
		Sku:               sku,
		ProductName:       model.Content.Name,
		BarcodeNumberList: barcodeList,
		SalesVat:          salesVat,
		PurchaseVat:       purchaseVat,
		Segment:           segment,
		Width:             width,
		Height:            height,
		Length:            length,
		GrossWeight:       weight,
		UnitPerPackage:    qtyPerBox,
		IsCoffeeProduct:   isCoffeeProduct,
	}

	existingProduct, getProductStreamErr := p.getProductStreamMessageFromCache(banabiProductId)
	if getProductStreamErr != nil {
		ch <- SendProductsToLogoServiceResponse{
			Error: getProductStreamErr,
		}
		return
	}

	if existingProduct != nil {
		proxyModelAsCacheModel := p.convertProxyModelToCacheModel(productToSendLogo, existingProduct.Timestamp)

		if !existingProduct.Timestamp.Before(model.Content.Timestamp) || reflect.DeepEqual(
			proxyModelAsCacheModel,
			*existingProduct,
		) {
			p.loggr.Info("Product has already sent to Logo", zap.String("ProductId", banabiProductId))
			ch <- SendProductsToLogoServiceResponse{
				Error: nil,
			}
			return
		}
	}

	proxyResponseCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(proxyResponseCh)

	go p.logoProxy.SendProducts(
		proxyResponseCh, &logo.SendProductsModel{
			ProductList: []logo.SendProductsProduct{
				productToSendLogo,
			},
		},
	)

	res := <-proxyResponseCh
	if res.Error != nil {
		ch <- SendProductsToLogoServiceResponse{
			Error: res.Error,
		}
		return
	}

	p.loggr.Info("Product sent to logo successfully.", zap.String("ProductId", productToSendLogo.ProductId), zap.String("Sku", productToSendLogo.Sku))

	marshaledProduct, marshalErr := json.Marshal(productToSendLogo)
	if marshalErr != nil {
		ch <- SendProductsToLogoServiceResponse{
			Error: marshalErr,
		}
		return
	}

	var stockAmount int
	if strings.EqualFold(segment, "delist") {
		stockAmount = 0
	} else {
		stockAmount = 1
	}

	go p.upsertProductStreamMessage(
		product.UpsertProductStreamModel{
			ProductId:   banabiProductId,
			Sku:         sku,
			Timestamp:   model.Content.Timestamp,
			Message:     string(marshaledProduct),
			Name:        model.Content.Name,
			Price:       price,
			ListPrice:   float64(model.Content.UnitPrice) / 100,
			StockAmount: stockAmount,
		},
	)

	go p.updateProductCache(p.convertProxyModelToCacheModel(productToSendLogo, model.Content.Timestamp))

	ch <- SendProductsToLogoServiceResponse{
		Error: nil,
		SendProductIds: []string{
			banabiProductId,
		},
	}
	return
}

func (p *ProductService) GetProductBySku(ch chan GetProductBySkuServiceResponse, sku string) {
	if sku == "" {
		ch <- GetProductBySkuServiceResponse{
			Error: errors.New("sku shouldn't be empty !"),
		}
		return
	}

	productDbCh := make(chan *product.GetProductStreamResponse)
	defer close(productDbCh)

	go p.productDb.GetProductStreamBySku(productDbCh, sku)

	productMessage := <-productDbCh
	if productMessage.Error != nil {
		ch <- GetProductBySkuServiceResponse{
			Error: productMessage.Error,
		}
		return
	}

	var response GetProductBySkuServiceResponse
	err := json.Unmarshal([]byte(productMessage.Message), &response.Message)

	if err != nil {
		ch <- GetProductBySkuServiceResponse{
			Error: err,
		}
		return
	}

	ch <- response
}

func (p *ProductService) GetProductsBySku(ch chan GetProductsBySkuServiceResponse, skus []string) {
	if len(skus) == 0 {
		ch <- GetProductsBySkuServiceResponse{
			Error: errors.New("Skus array shouldn't be empty !"),
		}
		return
	}

	getProductStreamsBySkusCh := make(chan *product.GetProductStreamsBySkuResponse)
	defer close(getProductStreamsBySkusCh)

	go p.productDb.GetProductStreamsBySku(getProductStreamsBySkusCh, skus)

	getProductStreamsResponse := <-getProductStreamsBySkusCh
	if getProductStreamsResponse.Error != nil {
		ch <- GetProductsBySkuServiceResponse{
			Error: getProductStreamsResponse.Error,
		}
		return
	}

	var response GetProductsBySkuServiceResponse
	response.Messages = make(map[string]ProductMessage)

	for _, productStreamMessage := range getProductStreamsResponse.Messages {
		var product ProductMessage

		err := json.Unmarshal([]byte(productStreamMessage.Message), &product)
		if err != nil {
			ch <- GetProductsBySkuServiceResponse{
				Error: nil,
			}
			return
		}

		response.Messages[productStreamMessage.Sku] = product
	}

	ch <- response
}

func (p *ProductService) GetAllProductsForPricingTool(ch chan GetAllProductsForPricingToolServiceResponse) {
	getAllProductsDbCh := make(chan *product.GetAllProductStreamResponse)
	defer close(getAllProductsDbCh)

	go p.productDb.GetAllProductsStream(getAllProductsDbCh)

	getAllProductsResponse := <-getAllProductsDbCh
	if getAllProductsResponse.Error != nil {
		ch <- GetAllProductsForPricingToolServiceResponse{
			Error: getAllProductsResponse.Error,
		}
		return
	}

	if len(getAllProductsResponse.AllProductsStream) == 0 {
		ch <- GetAllProductsForPricingToolServiceResponse{Error: errors.New("no_products_found")}
		return
	}

	var response GetAllProductsForPricingToolServiceResponse

	ptProductList := make([]PricingToolProduct, len(getAllProductsResponse.AllProductsStream))
	for i, x := range getAllProductsResponse.AllProductsStream {

		ptProductList[i] = PricingToolProduct{
			Price:       x.Price,
			ListPrice:   x.ListPrice,
			StockAmount: x.StockAmount,
			Sku:         x.Sku,
			Name:        x.Name,
		}
	}

	response.AllProducts = ptProductList

	ch <- response
}

func (p *ProductService) updateProductCache(product ProductCacheModel) {
	var cacheDuration int
	cacheDurationStr := p.serverConfigService.Get(enum.ProductStreamCacheDuration)

	if cacheDurationStr == nil {
		cacheDuration = p.defaultCacheDuration
	} else {
		if duration, atoiErr := strconv.Atoi(*cacheDurationStr); atoiErr == nil {
			cacheDuration = duration
		} else {
			cacheDuration = p.defaultCacheDuration
		}
	}

	go p.cachr.Set(p.getCacheKey(product.ProductId), product, time.Minute*time.Duration(cacheDuration))
	return
}

func (p *ProductService) getProductStreamMessageFromCache(productId string) (*ProductCacheModel, error) {
	existingProductStr := p.cachr.Get(p.getCacheKey(productId))
	existingProduct := ProductCacheModel{}

	if existingProductStr == nil {
		return nil, nil
	}

	err := json.Unmarshal([]byte(*existingProductStr), &existingProduct)
	if err != nil {
		return nil, err
	}

	return &existingProduct, nil
}

func (p *ProductService) convertProxyModelToCacheModel(
	product logo.SendProductsProduct,
	timestamp time.Time,
) ProductCacheModel {
	return ProductCacheModel{
		ProductId:         product.ProductId,
		Sku:               product.Sku,
		ProductName:       product.ProductName,
		BarcodeNumberList: product.BarcodeNumberList,
		SalesVat:          product.SalesVat,
		PurchaseVat:       product.PurchaseVat,
		Segment:           productSegment,
		Width:             product.Width,
		Height:            product.Height,
		Length:            product.Length,
		GrossWeight:       product.GrossWeight,
		UnitPerPackage:    product.UnitPerPackage,
		IsCoffeeProduct:   product.IsCoffeeProduct,
		Timestamp:         timestamp,
	}
}

func (p *ProductService) upsertProductStreamMessage(model product.UpsertProductStreamModel) error {
	upsertProductCh := make(chan error)
	defer close(upsertProductCh)

	go p.productDb.UpsertProductStream(upsertProductCh, model)

	upsertErr := <-upsertProductCh
	if upsertErr != nil {
		p.loggr.Error("Couldn't upsert product stream !",
			zap.String("Error", upsertErr.Error()),
			zap.Any("Model", model),
		)
		return upsertErr
	}

	return nil
}

func (p *ProductService) getCacheKey(productId string) string {
	return p.cacheKeyPrefix + ":" + productId
}
