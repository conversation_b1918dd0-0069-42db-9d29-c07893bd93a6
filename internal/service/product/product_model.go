package product

import (
	"strings"
	"time"
)

type SendProductsToLogoServiceModel struct {
	Content Content `json:"content" validate:"required"`
}

type Content struct {
	ProductId  string      `json:"product_id" validate:"required"`
	Name       string      `json:"name" validate:"required"`
	Names      []Name      `json:"names"`
	Attributes []Attribute `json:"attributes"`
	Timestamp  time.Time   `json:"timestamp"`
	UnitPrice  int         `json:"unit_price"`
}

type Attribute struct {
	AttributeId string `json:"attribute_id"`
	Name        string `json:"name"`
	Names       []Name `json:"names"`
}

type Name struct {
	Locale string `json:"locale"`
	Value  string `json:"value"`
}

func (c Content) FindAttribute(id string) *Attribute {
	var response *Attribute = new(Attribute)
	for _, attr := range c.Attributes {
		if attr.AttributeId == id {
			response = &attr
			response.Name = strings.TrimSpace(response.Name)
			break
		}
	}

	return response
}

type ProductCacheModel struct {
	ProductId         string
	Sku               string
	ProductName       string
	BarcodeNumberList []string
	SalesVat          float64
	PurchaseVat       float64
	Segment           string
	Width             float64
	Height            float64
	Length            float64
	GrossWeight       float64
	UnitPerPackage    float64
	IsCoffeeProduct   bool
	Timestamp         time.Time
}
