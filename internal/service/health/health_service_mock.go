// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/health/health_service.go

// Package health is a generated GoMock package.
package health

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIHealthService is a mock of IHealthService interface.
type MockIHealthService struct {
	ctrl     *gomock.Controller
	recorder *MockIHealthServiceMockRecorder
}

// MockIHealthServiceMockRecorder is the mock recorder for MockIHealthService.
type MockIHealthServiceMockRecorder struct {
	mock *MockIHealthService
}

// NewMockIHealthService creates a new mock instance.
func NewMockIHealthService(ctrl *gomock.Controller) *MockIHealthService {
	mock := &MockIHealthService{ctrl: ctrl}
	mock.recorder = &MockIHealthServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIHealthService) EXPECT() *MockIHealthServiceMockRecorder {
	return m.recorder
}

// HealthCheck mocks base method.
func (m *MockIHealthService) HealthCheck(ch chan *HealthCheckServiceResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "HealthCheck", ch)
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockIHealthServiceMockRecorder) HealthCheck(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockIHealthService)(nil).HealthCheck), ch)
}
