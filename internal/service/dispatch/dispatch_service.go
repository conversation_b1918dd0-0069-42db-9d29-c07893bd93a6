package dispatch

import (
	"fmt"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/customerror"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"net/http"
	"strings"
)

type IDispatchService interface {
	GetDispatchDocument(ch chan *logo.LogoProxyBaseResponse, getDispatchDocumentModel *GetDispatchDocumentModel)
}

type DispatchService struct {
	environment env.IEnvironment
	loggr       logger.ILogger
	validatr    validator.IValidator
	cachr       cacher.ICacher
	logoProxy   logo.ILogoProxy
}

// NewDispatchService
// Returns a new DispatchService.
func NewDispatchService(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, logoProxy logo.ILogoProxy) IDispatchService {
	service := DispatchService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if logoProxy != nil {
		service.logoProxy = logoProxy
	} else {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	return &service
}

// GetDispatchDocument
// Gets a dispatch document from Logo using dispatchId.
func (s *DispatchService) GetDispatchDocument(ch chan *logo.LogoProxyBaseResponse, getDispatchDocumentModel *GetDispatchDocumentModel) {
	modelErr := s.validatr.ValidateStruct(getDispatchDocumentModel)
	if modelErr != nil {
		ch <- &logo.LogoProxyBaseResponse{Error: modelErr, ResultCode: http.StatusBadRequest}
		return
	}

	dispatchCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(dispatchCh)

	go s.logoProxy.GetDispatchDocument(dispatchCh, &logo.GetDispatchDocumentModel{
		DispatchNumber: getDispatchDocumentModel.DispatchNumber,
	})

	response := <-dispatchCh

	if response.Error != nil {
		ch <- &logo.LogoProxyBaseResponse{Error: customerror.New(modelErr, customerror.LogLevelWarn), ResultCode: response.ResultCode}
		return
	}

	if strings.Contains(response.Result, "Hata Oluştu") {
		errorResponse := fmt.Errorf("invalid dispatch number, couldn't get dispatch document")
		ch <- &logo.LogoProxyBaseResponse{Error: customerror.New(errorResponse, customerror.LogLevelWarn), ResultCode: http.StatusBadRequest}
		return
	}

	ch <- &logo.LogoProxyBaseResponse{
		Error:      response.Error,
		ResultCode: response.ResultCode,
		ResultMsg:  response.ResultMsg,
		Result:     response.Result,
	}
}
