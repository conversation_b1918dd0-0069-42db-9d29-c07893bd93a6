// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/dispatch/dispatch_service.go

// Package dispatch is a generated GoMock package.
package dispatch

import (
	logo "logo-adapter/internal/data/proxy/logo"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIDispatchService is a mock of IDispatchService interface.
type MockIDispatchService struct {
	ctrl     *gomock.Controller
	recorder *MockIDispatchServiceMockRecorder
}

// MockIDispatchServiceMockRecorder is the mock recorder for MockIDispatchService.
type MockIDispatchServiceMockRecorder struct {
	mock *MockIDispatchService
}

// NewMockIDispatchService creates a new mock instance.
func NewMockIDispatchService(ctrl *gomock.Controller) *MockIDispatchService {
	mock := &MockIDispatchService{ctrl: ctrl}
	mock.recorder = &MockIDispatchServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDispatchService) EXPECT() *MockIDispatchServiceMockRecorder {
	return m.recorder
}

// GetDispatchDocument mocks base method.
func (m *MockIDispatchService) GetDispatchDocument(ch chan *logo.LogoProxyBaseResponse, getDispatchDocumentModel *GetDispatchDocumentModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetDispatchDocument", ch, getDispatchDocumentModel)
}

// GetDispatchDocument indicates an expected call of GetDispatchDocument.
func (mr *MockIDispatchServiceMockRecorder) GetDispatchDocument(ch, getDispatchDocumentModel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDispatchDocument", reflect.TypeOf((*MockIDispatchService)(nil).GetDispatchDocument), ch, getDispatchDocumentModel)
}
