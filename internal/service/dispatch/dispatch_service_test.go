package dispatch

import (
	"errors"
	"net/http"
	"testing"

	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type DispatchServiceTestSuite struct {
	suite.Suite
	dispatchService IDispatchService
	mockEnvironment *env.MockIEnvironment
	mockLogger      *logger.MockILogger
	mockValidator   *validator.MockIValidator
	mockCacher      *cacher.MockICacher
	logoProxy       *logo.MockILogoProxy
}

// Run suite.
func TestDispatchService(t *testing.T) {
	suite.Run(t, new(DispatchServiceTestSuite))
}

// Runs before each test in the suite.
func (d *DispatchServiceTestSuite) SetupTest() {
	d.T().Log("Setup")

	ctrl := gomock.NewController(d.T())
	defer ctrl.Finish()

	d.mockEnvironment = env.NewMockIEnvironment(ctrl)
	d.mockLogger = logger.NewMockILogger(ctrl)
	d.mockValidator = validator.NewMockIValidator(ctrl)
	d.mockCacher = cacher.NewMockICacher(ctrl)
	d.logoProxy = logo.NewMockILogoProxy(ctrl)

	d.dispatchService = NewDispatchService(d.mockEnvironment, d.mockLogger, d.mockValidator, d.mockCacher, d.logoProxy)
}

// Runs after each test in the suite.
func (i *DispatchServiceTestSuite) TearDownTest() {
	i.T().Log("Teardown")
}

func (d *DispatchServiceTestSuite) TestGetDispatchDocument_HappyPath_Success() {
	model := GetDispatchDocumentModel{
		DispatchNumber: "D0001",
	}

	d.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(nil)

	d.logoProxy.
		EXPECT().
		GetDispatchDocument(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.GetDispatchDocumentModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil, Result: "Success"}
		})

	ch := make(chan *logo.LogoProxyBaseResponse)

	go d.dispatchService.GetDispatchDocument(ch, &model)
	response := <-ch

	d.Nil(response.Error)
}

func (d *DispatchServiceTestSuite) TestGetDispatchDocument_ModelHasValidationError_ReturnsError() {
	model := GetDispatchDocumentModel{}

	validationError := "field validation for 'DispatchNumber' failed"

	d.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(errors.New(validationError))

	ch := make(chan *logo.LogoProxyBaseResponse)

	go d.dispatchService.GetDispatchDocument(ch, &model)
	response := <-ch

	d.EqualError(response.Error, validationError)
}

func (d *DispatchServiceTestSuite) TestGetDispatchDocument_LogoProxyError_ReturnsError() {
	model := GetDispatchDocumentModel{
		DispatchNumber: "D0001",
	}

	d.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(nil)

	d.logoProxy.
		EXPECT().
		GetDispatchDocument(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.GetDispatchDocumentModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil, Result: "Hata Oluştu"}
		})

	d.mockLogger.EXPECT().Warn(gomock.Any())

	ch := make(chan *logo.LogoProxyBaseResponse)

	go d.dispatchService.GetDispatchDocument(ch, &model)
	response := <-ch

	d.Equal(response.ResultCode, http.StatusBadRequest)
}
