package supplier

import (
	"fmt"
	"logo-adapter/internal/data/database/supplier"
	"logo-adapter/internal/data/proxy/logo"
	supProxy "logo-adapter/internal/data/proxy/supplier"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strings"

	"github.com/google/uuid"
)

type IProductReturnService interface {
	SendProductReturn(ch chan *SendProductReturnServiceResponse, model *SendProductReturnServiceModel)
	SendSupplierReturn(ch chan *SendProductReturnServiceResponse, model *SendSupplierReturnServiceModel)
}

type ProductReturnService struct {
	environment     env.IEnvironment
	loggr           logger.ILogger
	validatr        validator.IValidator
	cachr           cacher.ICacher
	logoProxy       logo.ILogoProxy
	purchaseOrderDb supplier.IPurchaseOrderDb
	supplierProxy   supProxy.ISupplierProxy
}

func NewProductReturnService(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	logoProxy logo.ILogoProxy,
	purchaseOrderDb supplier.IPurchaseOrderDb,
	supplierProxy supProxy.ISupplierProxy,
) IProductReturnService {
	service := ProductReturnService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if logoProxy != nil {
		service.logoProxy = logoProxy
	} else {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	if purchaseOrderDb != nil {
		service.purchaseOrderDb = purchaseOrderDb
	} else {
		service.purchaseOrderDb = supplier.NewPurchaseOrderDb(environment, loggr, validatr, cachr)
	}

	if supplierProxy == nil {
		service.supplierProxy = supProxy.NewSupplierProxy(environment, loggr, validatr, cachr)
	} else {
		service.supplierProxy = supplierProxy
	}

	return &service
}

func (s *ProductReturnService) SendProductReturn(ch chan *SendProductReturnServiceResponse, model *SendProductReturnServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendProductReturnServiceResponse{Error: modelErr}
		return
	}

	getProductReturnResponseCh := make(chan *supplier.GetProductReturnResponse)
	defer close(getProductReturnResponseCh)

	go s.purchaseOrderDb.GetProductReturn(getProductReturnResponseCh, &supplier.GetProductReturnModel{
		ContentType:     model.ContentType,
		ReturnReference: model.ReturnReference,
	})

	getProductReturnResponse := <-getProductReturnResponseCh
	if getProductReturnResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{Error: getProductReturnResponse.Error}
		return
	}

	// If there is a return reference, this means it has been added before.
	if getProductReturnResponse.ReturnReference != "" {
		ch <- &SendProductReturnServiceResponse{Error: nil}
		return
	}

	getReturnReasonResponseCh := make(chan *supplier.GetReturnReasonResponse)
	defer close(getReturnReasonResponseCh)

	go s.purchaseOrderDb.GetReturnReason(getReturnReasonResponseCh, &supplier.GetReturnReasonModel{
		Reason: strings.ToUpper(model.ReturnReason),
	})

	getReturnReasonResponse := <-getReturnReasonResponseCh
	if getReturnReasonResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{
			Error: getReturnReasonResponse.Error,
		}
		return
	}

	logoProxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(logoProxyCh)

	go s.logoProxy.SendMaterialSlips(logoProxyCh, convertToSendMaterialSlipsProxyModel(model, getReturnReasonResponse.Code))

	logoProxyResponse := <-logoProxyCh
	if logoProxyResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{
			Error: logoProxyResponse.Error,
		}
		return
	}

	addProductReturnResponseCh := make(chan *supplier.PurchaseOrderDbBaseResponse)
	defer close(addProductReturnResponseCh)

	go s.purchaseOrderDb.AddProductReturn(addProductReturnResponseCh, &supplier.AddProductReturnModel{
		ContentType:     model.ContentType,
		ReturnId:        model.ReturnId,
		ReturnReference: model.ReturnReference,
		WarehouseId:     model.WarehouseId,
		ReasonType:      model.ReturnReason,
		CompletedAt:     model.CompletedAt,
	})

	addProductReturnResponse := <-addProductReturnResponseCh
	if addProductReturnResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{Error: addProductReturnResponse.Error}
		return
	}

	ch <- &SendProductReturnServiceResponse{Error: nil}
}

func (s *ProductReturnService) SendSupplierReturn(ch chan *SendProductReturnServiceResponse, model *SendSupplierReturnServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &SendProductReturnServiceResponse{Error: modelErr}
		return
	}

	getProductReturnResponseCh := make(chan *supplier.GetProductReturnResponse)
	defer close(getProductReturnResponseCh)

	go s.purchaseOrderDb.GetProductReturn(getProductReturnResponseCh, &supplier.GetProductReturnModel{
		ContentType:     model.ContentType,
		ReturnReference: model.ReturnReference,
	})

	getProductReturnResponse := <-getProductReturnResponseCh
	if getProductReturnResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{Error: getProductReturnResponse.Error}
		return
	}

	// If there is a return reference, this means it has been added before.
	if getProductReturnResponse.ReturnReference != "" {
		ch <- &SendProductReturnServiceResponse{Error: nil}
		return
	}

	supplierProxyChannel := make(chan *supProxy.GetSupplierListWithDetailsResponse)
	defer close(supplierProxyChannel)

	go s.supplierProxy.GetSupplierListWithDetails(supplierProxyChannel, supProxy.GetSupplierListWithDetailModel{
		Ids: []int64{int64(model.SupplierId)},
	})

	supplierProxyResponse := <-supplierProxyChannel
	if supplierProxyResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{Error: supplierProxyResponse.Error}
		return
	}
	if len(supplierProxyResponse.SupplierList) == 0 {
		ch <- &SendProductReturnServiceResponse{
			Error: fmt.Errorf("SupplierId: %v not found", model.SupplierId),
		}
		return
	}

	supplierDetail := supplierProxyResponse.SupplierList[0]

	getReturnReasonResponseCh := make(chan *supplier.GetReturnReasonResponse)
	defer close(getReturnReasonResponseCh)

	go s.purchaseOrderDb.GetReturnReason(getReturnReasonResponseCh, &supplier.GetReturnReasonModel{
		Reason: strings.ToUpper(model.ReturnReason),
	})

	getReturnReasonResponse := <-getReturnReasonResponseCh
	if getReturnReasonResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{
			Error: getReturnReasonResponse.Error,
		}
		return
	}

	logoProxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(logoProxyCh)

	go s.logoProxy.SendStoreRefund(logoProxyCh, convertToSendStoreRefundProxyModel(model, getReturnReasonResponse.Code, supplierDetail.SupplierCode))

	logoProxyResponse := <-logoProxyCh
	if logoProxyResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{
			Error: logoProxyResponse.Error,
		}
		return
	}

	addProductReturnResponseCh := make(chan *supplier.PurchaseOrderDbBaseResponse)
	defer close(addProductReturnResponseCh)

	go s.purchaseOrderDb.AddProductReturn(addProductReturnResponseCh, &supplier.AddProductReturnModel{
		ContentType:     model.ContentType,
		ReturnId:        model.ReturnId,
		ReturnReference: model.ReturnReference,
		WarehouseId:     model.WarehouseId,
		ReasonType:      model.ReturnReason,
		CompletedAt:     model.CompletedAt,
	})

	addProductReturnResponse := <-addProductReturnResponseCh
	if addProductReturnResponse.Error != nil {
		ch <- &SendProductReturnServiceResponse{Error: addProductReturnResponse.Error}
		return
	}

	ch <- &SendProductReturnServiceResponse{Error: nil}
}

// convertToSendMaterialSlipsProxyModel
// Convert product return service model to proxy model
func convertToSendMaterialSlipsProxyModel(model *SendProductReturnServiceModel, reasonCode string) *logo.SendMaterialSlipsModel {
	var products []logo.MaterialSlipProductModel
	for _, p := range model.ReturnProducts {
		products = append(products, logo.MaterialSlipProductModel{
			RequestId:  model.ReturnId,
			Quantity:   float64(p.Quantity),
			Unit:       "ADET",
			ReasonCode: reasonCode,
			SKU:        p.Sku,
		})
	}

	response := &logo.SendMaterialSlipsModel{
		MessageId:       uuid.NewString(),
		TransactionDate: helper.ConvertTimeByZone(model.CompletedAt, "Europe/Istanbul"),
		MaterialSlips: []logo.MaterialSlipModel{
			{
				StoreId:     model.WarehouseId,
				SlipType:    11,
				SlipId:      model.ReturnReference,
				ProductList: products,
			},
		},
	}
	return response
}

// convertToSendStoreRefundProxyModel
// Convert supplier return service model to proxy model
func convertToSendStoreRefundProxyModel(model *SendSupplierReturnServiceModel, reasonCode, supplierCode string) *logo.SendStoreRefundModel {
	var products []logo.Product
	for _, p := range model.ReturnProducts {
		products = append(products, logo.Product{
			SKU:        p.Sku,
			Quantity:   p.Quantity,
			ReasonCode: reasonCode,
		})
	}

	response := &logo.SendStoreRefundModel{
		MessageId:       uuid.NewString(),
		TransactionDate: helper.ConvertTimeByZone(model.CompletedAt, "Europe/Istanbul"),
		StoreId:         model.WarehouseId,
		SlipType:        6,
		CarrierType:     model.CarrierType,
		SupplierCode:    supplierCode,
		DispatchId:      model.ReturnReference,
		DispatchDate:    helper.ConvertTimeByZone(model.CompletedAt, "Europe/Istanbul"),
		ProductList:     products,
	}
	return response
}
