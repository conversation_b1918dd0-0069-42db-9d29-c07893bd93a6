package supplier

import (
	"logo-adapter/internal/data/database/supplier"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

type IPurchaseOrderService interface {
	SendPurchaseOrderInbound(ch chan *SendPurchaseOrderInboundServiceResponse, model *SendPurchaseOrderInboundServiceModel, orderType string)
	SendGlobalPurchaseOrder(ch chan *PurchaseOrderServiceBaseResponse, model *SendGlobalPurchaseOrderServiceModel)
	SendStoreRefund(ch chan *PurchaseOrderServiceBaseResponse, model *SendStoreRefundServiceModel)
	GlobalPurchaseOrderCancellation(ch chan *PurchaseOrderServiceBaseResponse, model *GlobalPurchaseOrderCancellationServiceModel)
	AddPurchaseOrderToDb(ch chan *PurchaseOrderServiceBaseResponse, model *AddPurchaseOrderToDbServiceModel)
	GetPurchaseOrderFromDb(ch chan *GetPurchaseOrderFromDbServiceResponse, model *GetPurchaseOrderServiceModel)
	GetReturnReason(ch chan *GetReturnReasonServiceResponse, model *GetReturnReasonServiceModel)
	AddPurchaseOrderRefundToDb(ch chan *PurchaseOrderServiceBaseResponse, model *AddPurchaseOrderRefundServiceModel)
	GetPurchaseOrderRefundFromDb(ch chan *GetPurchaseOrderRefundServiceResponse, model *GetPurchaseOrderRefundServiceModel)
}

type PurchaseOrderService struct {
	environment     env.IEnvironment
	loggr           logger.ILogger
	validatr        validator.IValidator
	cachr           cacher.ICacher
	logoProxy       logo.ILogoProxy
	purchaseOrderDb supplier.IPurchaseOrderDb
}

func NewPurchaseOrderService(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, logoProxy logo.ILogoProxy, purchaseOrderDb supplier.IPurchaseOrderDb) IPurchaseOrderService {
	service := PurchaseOrderService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if logoProxy != nil {
		service.logoProxy = logoProxy
	} else {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	if purchaseOrderDb != nil {
		service.purchaseOrderDb = purchaseOrderDb
	} else {
		service.purchaseOrderDb = supplier.NewPurchaseOrderDb(environment, loggr, validatr, cachr)
	}

	return &service
}

func (s *PurchaseOrderService) SendPurchaseOrderInbound(ch chan *SendPurchaseOrderInboundServiceResponse, model *SendPurchaseOrderInboundServiceModel, orderType string) {
	err := s.validatr.ValidateStruct(model)

	if err != nil {
		ch <- &SendPurchaseOrderInboundServiceResponse{
			Error:     err,
			ResultMsg: err.Error(),
		}
		return
	}

	proxyChannel := make(chan *logo.LogoProxyBaseResponse)
	defer close(proxyChannel)

	proxyModel := logo.SendSupplierOrderCollectionModel{
		MessageId:       model.MessageId,
		TransactionDate: model.TransactionDate,
		OrderId:         model.OrderId,
		OrderReference:  model.OrderReference,
		StoreId:         model.StoreId,
		BillingId:       model.BillingId,
		BillingDate:     model.BillingDate,
	}

	proxyModel.OrderDetail = make([]logo.OrderDetail, len(model.OrderDetail))

	for i := 0; i < len(model.OrderDetail); i++ {
		proxyModel.OrderDetail[i].Sku = model.OrderDetail[i].Sku
		proxyModel.OrderDetail[i].Quantity = model.OrderDetail[i].Quantity
		proxyModel.OrderDetail[i].IsOutOfOrder = orderType == enum.PurchaseOrderTypeDc &&
			strings.HasPrefix(model.OrderDetail[i].Sku, "MRP.")
	}

	go s.logoProxy.SendSupplierOrderCollection(proxyChannel, &proxyModel)

	proxyResponse := <-proxyChannel

	if proxyResponse.Error != nil {
		s.loggr.Error("An error occurred in SendPurchaseOrderInbound service.",
			zap.String("ResultMessage", proxyResponse.ResultMsg),
			zap.String("Result", proxyResponse.Result),
			zap.String("ResultCode", strconv.Itoa(proxyResponse.ResultCode)))

		ch <- &SendPurchaseOrderInboundServiceResponse{
			ResultMsg:  proxyResponse.ResultMsg,
			Error:      proxyResponse.Error,
			Result:     proxyResponse.Result,
			ResultCode: proxyResponse.ResultCode,
		}
		return
	}

	addPurchaseOrderInboundCh := make(chan *supplier.PurchaseOrderDbBaseResponse)
	defer close(addPurchaseOrderInboundCh)
	go s.purchaseOrderDb.AddPurchaseOrderInbound(addPurchaseOrderInboundCh, &supplier.AddPurchaseOrderInboundModel{
		MessageId:      model.MessageId,
		OrderId:        model.OrderId,
		OrderReference: model.OrderReference,
		BillingId:      model.BillingId,
		BillingDate:    model.BillingDate,
		OrderType:      orderType,
	})

	addPurchaseOrderInboundResponse := <-addPurchaseOrderInboundCh
	if addPurchaseOrderInboundResponse.Error != nil {
		ch <- &SendPurchaseOrderInboundServiceResponse{
			Error: addPurchaseOrderInboundResponse.Error,
		}
		return
	}

	ch <- &SendPurchaseOrderInboundServiceResponse{}
}

func (s *PurchaseOrderService) SendGlobalPurchaseOrder(ch chan *PurchaseOrderServiceBaseResponse, model *SendGlobalPurchaseOrderServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &PurchaseOrderServiceBaseResponse{
			Error: err,
		}
		return
	}

	proxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(proxyCh)
	go s.logoProxy.SendGlobalPurchaseOrder(proxyCh, convertToPurchaseOrderProxyModel(model))

	response := <-proxyCh

	if response.Error != nil {
		ch <- &PurchaseOrderServiceBaseResponse{
			Error: response.Error,
		}
		return
	}

	ch <- &PurchaseOrderServiceBaseResponse{
		Error:      nil,
		ResultCode: 0,
	}
	return
}

func (s *PurchaseOrderService) SendStoreRefund(ch chan *PurchaseOrderServiceBaseResponse, model *SendStoreRefundServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &PurchaseOrderServiceBaseResponse{
			Error: err,
		}
		return
	}

	proxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(proxyCh)
	go s.logoProxy.SendStoreRefund(proxyCh, convertToStoreRefundProxyModel(model))

	response := <-proxyCh

	if response.Error != nil {
		ch <- &PurchaseOrderServiceBaseResponse{
			Error: response.Error,
		}
		return
	}

	ch <- &PurchaseOrderServiceBaseResponse{
		Error:      nil,
		ResultCode: 0,
	}
	return
}

func (s *PurchaseOrderService) GlobalPurchaseOrderCancellation(ch chan *PurchaseOrderServiceBaseResponse, model *GlobalPurchaseOrderCancellationServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &PurchaseOrderServiceBaseResponse{
			Error: err,
		}
		return
	}

	proxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(proxyCh)
	go s.logoProxy.GlobalPurchaseOrderCancellation(proxyCh, convertToGlobalPurchaseOrderCancellationProxyModel(model))

	response := <-proxyCh

	if response.Error != nil {
		ch <- &PurchaseOrderServiceBaseResponse{
			Error: response.Error,
		}
		return
	}

	ch <- &PurchaseOrderServiceBaseResponse{
		Error:      nil,
		ResultCode: 0,
	}
	return
}

func (s *PurchaseOrderService) AddPurchaseOrderToDb(ch chan *PurchaseOrderServiceBaseResponse, model *AddPurchaseOrderToDbServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &PurchaseOrderServiceBaseResponse{
			Error: err,
		}
		return
	}

	databaseCh := make(chan *supplier.PurchaseOrderDbBaseResponse)
	defer close(databaseCh)

	go s.purchaseOrderDb.AddPurchaseOrder(databaseCh, &supplier.AddPurchaseOrderModel{
		OrderId:        model.OrderId,
		OrderReference: model.OrderReference,
		OrderType:      model.OrderType,
		EventType:      model.EventType,
	})

	response := <-databaseCh
	if response.Error != nil {
		ch <- &PurchaseOrderServiceBaseResponse{Error: response.Error}
		return
	}

	ch <- &PurchaseOrderServiceBaseResponse{
		Error:      nil,
		ResultCode: 0,
	}
	return
}

func (s *PurchaseOrderService) GetPurchaseOrderFromDb(ch chan *GetPurchaseOrderFromDbServiceResponse, model *GetPurchaseOrderServiceModel) {
	err := s.validatr.ValidateStruct(model)
	if err != nil {
		ch <- &GetPurchaseOrderFromDbServiceResponse{Error: err}
		return
	}

	databaseCh := make(chan *supplier.GetPurchaseOrderDbResponse)
	defer close(databaseCh)

	go s.purchaseOrderDb.GetPurchaseOrder(databaseCh, &supplier.GetPurchaseOrderModel{
		OrderId:   model.OrderId,
		EventType: model.EventType,
	})

	response := <-databaseCh
	if response.Error != nil {
		ch <- &GetPurchaseOrderFromDbServiceResponse{Error: response.Error}
		return
	}

	ch <- &GetPurchaseOrderFromDbServiceResponse{
		Id:             response.Id,
		OrderId:        response.OrderId,
		OrderReference: response.OrderReference,
		OrderType:      response.OrderType,
		EventType:      response.EventType,
		CreatedDate:    response.CreatedDate,
		Error:          nil,
	}
	return
}

func (s *PurchaseOrderService) GetReturnReason(ch chan *GetReturnReasonServiceResponse, model *GetReturnReasonServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetReturnReasonServiceResponse{Error: modelErr}
		return
	}

	databaseCh := make(chan *supplier.GetReturnReasonResponse)
	defer close(databaseCh)
	go s.purchaseOrderDb.GetReturnReason(databaseCh, &supplier.GetReturnReasonModel{
		Reason: model.Reason,
	})

	response := <-databaseCh

	if response.Error != nil {
		ch <- &GetReturnReasonServiceResponse{
			Error: response.Error,
		}
		return
	}

	ch <- &GetReturnReasonServiceResponse{
		Id:     response.Id,
		Code:   response.Code,
		Reason: response.Reason,
		Error:  nil,
	}

	return
}

func (s *PurchaseOrderService) AddPurchaseOrderRefundToDb(ch chan *PurchaseOrderServiceBaseResponse, model *AddPurchaseOrderRefundServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &PurchaseOrderServiceBaseResponse{Error: modelErr}
		return
	}

	databaseCh := make(chan *supplier.PurchaseOrderDbBaseResponse)
	defer close(databaseCh)

	go s.purchaseOrderDb.AddPurchaseOrderRefund(databaseCh, &supplier.AddPurchaseOrderRefundModel{
		OrderReference: model.OrderReference,
		OrderId:        model.OrderId,
	})

	response := <-databaseCh
	if response.Error != nil {
		ch <- &PurchaseOrderServiceBaseResponse{Error: response.Error}
		return
	}

	ch <- &PurchaseOrderServiceBaseResponse{Error: nil}
	return
}

func (s *PurchaseOrderService) GetPurchaseOrderRefundFromDb(ch chan *GetPurchaseOrderRefundServiceResponse, model *GetPurchaseOrderRefundServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetPurchaseOrderRefundServiceResponse{Error: modelErr}
		return
	}

	databaseCh := make(chan *supplier.GetPurchaseOrderRefundResponse)
	defer close(databaseCh)

	go s.purchaseOrderDb.GetPurchaseOrderRefund(databaseCh, &supplier.GetPurchaseOrderRefundModel{
		OrderReference: model.OrderReference,
	})

	response := <-databaseCh
	if response.Error != nil {
		ch <- &GetPurchaseOrderRefundServiceResponse{Error: response.Error}
		return
	}

	ch <- &GetPurchaseOrderRefundServiceResponse{
		Id:             response.Id,
		OrderId:        response.OrderId,
		OrderReference: response.OrderReference,
		CreatedDate:    response.CreatedDate,
		Error:          nil,
	}
	return
}

// convertToPurchaseOrderProxyModel
// Convert service model to proxy request model
func convertToPurchaseOrderProxyModel(model *SendGlobalPurchaseOrderServiceModel) *logo.SendGlobalPurchaseOrderModel {
	var poSlips []logo.SendGlobalPurchaseOrderPoSlipModel
	for _, item := range model.POSlips {
		var productList []logo.SendGlobalPurchaseOrderPoSlipProductModel

		for _, productItem := range item.ProductList {
			productList = append(productList, logo.SendGlobalPurchaseOrderPoSlipProductModel{
				SKU:      productItem.SKU,
				Quantity: productItem.Quantity,
				Price:    productItem.Price,
				VatRate:  productItem.VatRate,
			})
		}

		poSlips = append(poSlips, logo.SendGlobalPurchaseOrderPoSlipModel{
			POGuidId:     item.POGuidId,
			PODate:       item.PODate,
			POReference:  item.POReference,
			SupplierCode: item.SupplierCode,
			PayPlanCode:  item.PayPlanCode,
			StoreId:      item.StoreId,
			ProjectCode:  item.ProjectCode,
			ProductList:  productList,
			DeliveryDate: item.DeliveryDate,
		})
	}

	result := &logo.SendGlobalPurchaseOrderModel{
		MessageId:       model.MessageId,
		TransactionDate: model.TransactionDate,
		POSlips:         poSlips,
	}
	return result
}

// convertToStoreRefundProxyModel
// Convert store refund service model to proxy model
func convertToStoreRefundProxyModel(model *SendStoreRefundServiceModel) *logo.SendStoreRefundModel {
	var products []logo.Product
	for _, p := range model.ProductList {
		products = append(products, logo.Product{
			SKU:        p.SKU,
			Quantity:   p.Quantity,
			ReasonCode: p.ReasonCode,
		})
	}

	response := &logo.SendStoreRefundModel{
		MessageId:       model.MessageId,
		TransactionDate: model.TransactionDate,
		StoreId:         model.StoreId,
		SlipType:        model.SlipType,
		CarrierType:     model.CarrierType,
		DispatchId:      model.DispatchId,
		SupplierCode:    model.SupplierCode,
		ProductList:     products,
		DispatchDate:    model.DispatchDate,
	}
	return response
}

// convertToGlobalPurchaseOrderCancellationProxyModel
// Convert service model to proxy request model
func convertToGlobalPurchaseOrderCancellationProxyModel(model *GlobalPurchaseOrderCancellationServiceModel) *logo.GlobalPurchaseOrderCancellationModel {
	return &logo.GlobalPurchaseOrderCancellationModel{
		MessageId:        model.MessageId,
		POGuidId:         model.POGuidId,
		POReference:      model.POReference,
		CancellationDate: model.CancellationDate,
	}
}
