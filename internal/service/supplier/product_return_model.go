package supplier

import "time"

type SendProductReturnServiceModel struct {
	ContentType     string                     `validate:"required"`
	ReturnId        string                     `validate:"required"`
	ReturnReference string                     `validate:"required"`
	WarehouseId     string                     `validate:"required"`
	ReturnReason    string                     `validate:"required"`
	ReturnProducts  []ProductReturnProductItem `validate:"required,dive,required"`
	CompletedAt     time.Time                  `validate:"required"`
}

type ProductReturnProductItem struct {
	Sku      string `validate:"required"`
	Quantity int    `validate:"gte=1"`
}

type SendSupplierReturnServiceModel struct {
	ContentType     string                      `validate:"required"`
	CarrierType     string                      `validate:"required"`
	ReturnId        string                      `validate:"required"`
	ReturnReference string                      `validate:"required"`
	SupplierId      int                         `validate:"required"`
	ReturnReason    string                      `validate:"required"`
	WarehouseId     string                      `validate:"required"`
	ReturnProducts  []SupplierReturnProductItem `validate:"required,dive,required"`
	CompletedAt     time.Time                   `validate:"required"`
}

type SupplierReturnProductItem struct {
	Sku      string `validate:"required"`
	Quantity int    `validate:"gte=1"`
}
