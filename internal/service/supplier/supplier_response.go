package supplier

import (
	"time"
)

type SendSuppliersToLogoResponse struct {
	Error         error
	SupplierNames []string
}

type GetSuppliersResponse struct {
	Error error
}

type GetSupplierDetailResponse struct {
	Error    error
	Supplier []SupplierDetail
}

type SupplierDetail struct {
	Id                int64
	Name              string
	CreatedAt         time.Time
	UpdatedA          time.Time
	DeletedAt         time.Time
	SupplierFinanceId string
	SupplierTaxId     string
	CountryId         int
	CityId            int
	Street            string
	Complement        string
	Email             string
	Password          string
	Cellphone         string
	LandlineTelephone string
	Stores            int
	Products          int
	CurrencyId        int
	ImageUrl          string
	CountryName       string
	CityName          string
	ZipCode           string
	AddressLine_2     string
	TaxOfficeName     string
	RegionName        string
	ProjectCode       string
	Iban              string
	GroupName         string
	SupplierCode      string
	SupplierPayPlanId string
}
