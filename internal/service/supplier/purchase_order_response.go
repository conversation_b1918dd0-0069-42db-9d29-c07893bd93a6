package supplier

import "time"

type PurchaseOrderServiceBaseResponse struct {
	ResultCode int    `json:"ResultCode"`
	ResultMsg  string `json:"ResultMsg"`
	Result     string `json:"Result"`
	Error      error  `json:"-"`
}

type SendPurchaseOrderInboundServiceResponse struct {
	Error      error
	ResultMsg  string
	Result     string
	ResultCode int
}

type GetPurchaseOrderFromDbServiceResponse struct {
	Id             int64     `json:"id"`
	OrderId        string    `json:"order_id"`
	OrderReference string    `json:"order_reference"`
	OrderType      string    `json:"order_type"`
	EventType      string    `json:"event_type"`
	CreatedDate    time.Time `json:"created_date"`
	Error          error     `json:"-"`
}

type GetReturnReasonServiceResponse struct {
	Id     int32  `json:"id"`
	Code   string `json:"code"`
	Reason string `json:"reason"`
	Error  error  `json:"-"`
}

type GetPurchaseOrderRefundServiceResponse struct {
	Id             int64     `json:"id"`
	OrderId        string    `json:"order_id" validate:"required"`
	OrderReference string    `json:"order_reference"`
	CreatedDate    time.Time `json:"created_date"`
	Error          error     `json:"-"`
}
