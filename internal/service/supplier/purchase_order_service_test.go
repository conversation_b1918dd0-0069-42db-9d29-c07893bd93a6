package supplier

import (
	"errors"
	"testing"
	"time"

	"logo-adapter/internal/data/database/supplier"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/enum"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type PurchaseOrderServiceTestSuit struct {
	suite.Suite
	purchaseOrderService IPurchaseOrderService
	mockEnvironment      *env.MockIEnvironment
	mockLogger           *logger.MockILogger
	mockValidator        *validator.MockIValidator
	mockCacher           *cacher.MockICacher
	mockLogoProxy        *logo.MockILogoProxy
	mockPurchaseOrderDb  *supplier.MockIPurchaseOrderDb
}

func TestPurchaseOrderService(t *testing.T) {
	suite.Run(t, new(PurchaseOrderServiceTestSuit))
}

// Runs before each test in the suite.
func (p *PurchaseOrderServiceTestSuit) SetupTest() {
	p.T().Log("Setup")

	ctrl := gomock.NewController(p.T())
	defer ctrl.Finish()

	p.mockEnvironment = env.NewMockIEnvironment(ctrl)
	p.mockLogger = logger.NewMockILogger(ctrl)
	p.mockValidator = validator.NewMockIValidator(ctrl)
	p.mockCacher = cacher.NewMockICacher(ctrl)
	p.mockLogoProxy = logo.NewMockILogoProxy(ctrl)
	p.mockPurchaseOrderDb = supplier.NewMockIPurchaseOrderDb(ctrl)

	p.purchaseOrderService = NewPurchaseOrderService(p.mockEnvironment, p.mockLogger, p.mockValidator, p.mockCacher,
		p.mockLogoProxy, p.mockPurchaseOrderDb)
}

// Runs after each test in the suite.
func (p *PurchaseOrderServiceTestSuit) TearDownTest() {
	p.T().Log("Teardown")
}

func (p *PurchaseOrderServiceTestSuit) TestGetPurchaseOrderFromDb_ServiceModelValidation_ReturnsError() {
	var models = []struct {
		given    GetPurchaseOrderServiceModel
		expected string
	}{
		{GetPurchaseOrderServiceModel{OrderId: "", EventType: enum.PurchaseOrderCreatedEvent},
			"orderId cannot be empty"},
		{GetPurchaseOrderServiceModel{OrderId: "1234", EventType: ""},
			"eventType cannot be empty"},
	}

	ch := make(chan *GetPurchaseOrderFromDbServiceResponse)
	defer close(ch)

	for _, tc := range models {
		p.mockValidator.EXPECT().ValidateStruct(gomock.Eq(&tc.given)).Return(errors.New(tc.expected))

		go p.purchaseOrderService.GetPurchaseOrderFromDb(ch, &tc.given)

		response := <-ch

		p.EqualError(response.Error, tc.expected)
	}
}

func (p *PurchaseOrderServiceTestSuit) TestGetPurchaseOrderFromDb_NonExistingPurchaseOrder_ReturnsError() {
	model := &GetPurchaseOrderServiceModel{}
	expectedMessage := "purchase supplier not found"

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetPurchaseOrder(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetPurchaseOrderDbResponse, model *supplier.GetPurchaseOrderModel) {
			ch <- &supplier.GetPurchaseOrderDbResponse{Error: errors.New(expectedMessage)}
		})

	ch := make(chan *GetPurchaseOrderFromDbServiceResponse)
	defer close(ch)

	go p.purchaseOrderService.GetPurchaseOrderFromDb(ch, model)

	response := <-ch

	p.EqualError(response.Error, expectedMessage)
}

func (p *PurchaseOrderServiceTestSuit) TestGetPurchaseOrderFromDb_ExistingPurchaseOrder_ReturnsPurchaseOrder() {
	model := &GetPurchaseOrderServiceModel{
		OrderId:   "123",
		EventType: enum.PurchaseOrderCreatedEvent,
	}

	expected := &GetPurchaseOrderFromDbServiceResponse{
		OrderId:        "123",
		OrderType:      enum.PurchaseOrderTypeDmart,
		EventType:      enum.PurchaseOrderCreatedEvent,
		OrderReference: "PO123",
	}

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetPurchaseOrder(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetPurchaseOrderDbResponse, model *supplier.GetPurchaseOrderModel) {
			ch <- &supplier.GetPurchaseOrderDbResponse{
				OrderId:        expected.OrderId,
				OrderType:      expected.OrderType,
				EventType:      expected.EventType,
				OrderReference: expected.OrderReference,
			}
		})

	ch := make(chan *GetPurchaseOrderFromDbServiceResponse)
	defer close(ch)

	go p.purchaseOrderService.GetPurchaseOrderFromDb(ch, model)

	response := <-ch

	p.Nil(response.Error)
	p.EqualValues(response.OrderId, expected.OrderId)
	p.EqualValues(response.OrderReference, expected.OrderReference)
	p.EqualValues(response.OrderType, expected.OrderType)
	p.EqualValues(response.EventType, expected.EventType)
}

func (p *PurchaseOrderServiceTestSuit) TestGetPurchaseOrderRefundFromDb_ServiceModelValidation_ReturnsError() {
	var models = []struct {
		given    GetPurchaseOrderRefundServiceModel
		expected string
	}{
		{GetPurchaseOrderRefundServiceModel{OrderReference: ""}, "orderReference cannot be empty"},
	}

	ch := make(chan *GetPurchaseOrderRefundServiceResponse)
	defer close(ch)

	for _, tc := range models {
		p.mockValidator.EXPECT().ValidateStruct(gomock.Eq(&tc.given)).Return(errors.New(tc.expected))

		go p.purchaseOrderService.GetPurchaseOrderRefundFromDb(ch, &tc.given)

		response := <-ch

		p.EqualError(response.Error, tc.expected)
	}
}

func (p *PurchaseOrderServiceTestSuit) TestGetPurchaseOrderRefundFromDb_NonExistingPurchaseOrderRefund_ReturnsError() {
	model := &GetPurchaseOrderRefundServiceModel{}
	expectedMessage := "purchase supplier refund not found"

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetPurchaseOrderRefund(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetPurchaseOrderRefundResponse, model *supplier.GetPurchaseOrderRefundModel) {
			ch <- &supplier.GetPurchaseOrderRefundResponse{Error: errors.New(expectedMessage)}
		})

	ch := make(chan *GetPurchaseOrderRefundServiceResponse)
	defer close(ch)

	go p.purchaseOrderService.GetPurchaseOrderRefundFromDb(ch, model)

	response := <-ch

	p.EqualError(response.Error, expectedMessage)
}

func (p *PurchaseOrderServiceTestSuit) TestGetPurchaseOrderRefundFromDb_ExistingPurchaseOrderRefund_ReturnsPurchaseOrderRefund() {
	model := &GetPurchaseOrderRefundServiceModel{
		OrderReference: "PO123",
	}

	expected := &GetPurchaseOrderRefundServiceResponse{
		OrderId:        "123",
		OrderReference: "PO123",
	}

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetPurchaseOrderRefund(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetPurchaseOrderRefundResponse, model *supplier.GetPurchaseOrderRefundModel) {
			ch <- &supplier.GetPurchaseOrderRefundResponse{
				OrderReference: expected.OrderReference,
				OrderId:        expected.OrderId,
			}
		})

	ch := make(chan *GetPurchaseOrderRefundServiceResponse)
	defer close(ch)

	go p.purchaseOrderService.GetPurchaseOrderRefundFromDb(ch, model)

	response := <-ch

	p.Nil(response.Error)
	p.EqualValues(response.OrderId, expected.OrderId)
	p.EqualValues(response.OrderReference, expected.OrderReference)
}

func (p *PurchaseOrderServiceTestSuit) TestAddPurchaseOrderToDb_ServiceModelValidation_ReturnsError() {
	var models = []struct {
		given    AddPurchaseOrderToDbServiceModel
		expected string
	}{
		{AddPurchaseOrderToDbServiceModel{
			OrderId:        "",
			OrderReference: "PO123",
			OrderType:      enum.PurchaseOrderTypeDmart,
			EventType:      enum.PurchaseOrderCreatedEvent,
		}, "orderId cannot be empty"},
		{AddPurchaseOrderToDbServiceModel{
			OrderId:        "123",
			OrderReference: "",
			OrderType:      enum.PurchaseOrderTypeDmart,
			EventType:      enum.PurchaseOrderCreatedEvent,
		}, "orderReference cannot be empty"},
		{AddPurchaseOrderToDbServiceModel{
			OrderId:        "123",
			OrderReference: "PO123",
			OrderType:      "",
			EventType:      enum.PurchaseOrderCreatedEvent,
		}, "orderType cannot be empty"},
		{AddPurchaseOrderToDbServiceModel{
			OrderId:        "123",
			OrderReference: "PO123",
			OrderType:      enum.PurchaseOrderTypeDmart,
			EventType:      "",
		}, "eventType cannot be empty"},
	}

	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	for _, tc := range models {
		p.mockValidator.EXPECT().ValidateStruct(gomock.Eq(&tc.given)).Return(errors.New(tc.expected))

		go p.purchaseOrderService.AddPurchaseOrderToDb(ch, &tc.given)

		response := <-ch

		p.EqualError(response.Error, tc.expected)
	}
}

func (p *PurchaseOrderServiceTestSuit) TestAddPurchaseOrderToDb_CanNotAddToDatabase_ReturnsError() {
	model := &AddPurchaseOrderToDbServiceModel{
		OrderId:        "123",
		OrderReference: "PO123",
		OrderType:      enum.PurchaseOrderTypeDmart,
		EventType:      enum.PurchaseOrderCreatedEvent,
	}
	expectedErrorMessage := "could not add because of something"

	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().AddPurchaseOrder(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.PurchaseOrderDbBaseResponse, model *supplier.AddPurchaseOrderModel) {
			ch <- &supplier.PurchaseOrderDbBaseResponse{
				Error: errors.New(expectedErrorMessage),
			}
		})

	go p.purchaseOrderService.AddPurchaseOrderToDb(ch, model)

	response := <-ch

	p.EqualError(response.Error, expectedErrorMessage)
}

func (p *PurchaseOrderServiceTestSuit) TestAddPurchaseOrderToDb_WithValidArgs_AddsPurchaseOrder() {
	model := &AddPurchaseOrderToDbServiceModel{
		OrderId:        "123",
		OrderReference: "PO123",
		OrderType:      enum.PurchaseOrderTypeDmart,
		EventType:      enum.PurchaseOrderCreatedEvent,
	}

	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().AddPurchaseOrder(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.PurchaseOrderDbBaseResponse, model *supplier.AddPurchaseOrderModel) {
			ch <- &supplier.PurchaseOrderDbBaseResponse{
				Error: nil,
			}
		})

	go p.purchaseOrderService.AddPurchaseOrderToDb(ch, model)

	response := <-ch

	p.Nil(response.Error)
	p.EqualValues(response.ResultCode, 0)
}

func (p *PurchaseOrderServiceTestSuit) TestAddPurchaseOrderRefundToDb_ServiceModelValidation_ReturnsError() {
	var models = []struct {
		given    AddPurchaseOrderRefundServiceModel
		expected string
	}{
		{AddPurchaseOrderRefundServiceModel{
			OrderId:        "",
			OrderReference: "PO123",
		}, "orderId cannot be empty"},
		{AddPurchaseOrderRefundServiceModel{
			OrderId:        "123",
			OrderReference: "",
		}, "orderReference cannot be empty"},
	}

	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	for _, tc := range models {
		p.mockValidator.EXPECT().ValidateStruct(gomock.Eq(&tc.given)).Return(errors.New(tc.expected))

		go p.purchaseOrderService.AddPurchaseOrderRefundToDb(ch, &tc.given)

		response := <-ch

		p.EqualError(response.Error, tc.expected)
	}
}

func (p *PurchaseOrderServiceTestSuit) TestAddPurchaseOrderRefundToDb_CanNotAddToDatabase_ReturnsError() {
	model := &AddPurchaseOrderRefundServiceModel{
		OrderId:        "123",
		OrderReference: "PO123",
	}
	expectedErrorMessage := "could not add because of something"

	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().AddPurchaseOrderRefund(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.PurchaseOrderDbBaseResponse, model *supplier.AddPurchaseOrderRefundModel) {
			ch <- &supplier.PurchaseOrderDbBaseResponse{
				Error: errors.New(expectedErrorMessage),
			}
		})

	go p.purchaseOrderService.AddPurchaseOrderRefundToDb(ch, model)

	response := <-ch

	p.EqualError(response.Error, expectedErrorMessage)
}

func (p *PurchaseOrderServiceTestSuit) TestAddPurchaseOrderRefundToDb_WithValidArgs_AddsPurchaseOrderRefund() {
	model := &AddPurchaseOrderRefundServiceModel{
		OrderId:        "123",
		OrderReference: "PO123",
	}

	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().AddPurchaseOrderRefund(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.PurchaseOrderDbBaseResponse, model *supplier.AddPurchaseOrderRefundModel) {
			ch <- &supplier.PurchaseOrderDbBaseResponse{
				Error: nil,
			}
		})

	go p.purchaseOrderService.AddPurchaseOrderRefundToDb(ch, model)

	response := <-ch

	p.Nil(response.Error)
	p.EqualValues(response.ResultCode, 0)
}

func (p *PurchaseOrderServiceTestSuit) TestGetReturnReason_ServiceModelValidation_ReturnsError() {
	var models = []struct {
		given    GetReturnReasonServiceModel
		expected string
	}{
		{GetReturnReasonServiceModel{}, "reason cannot be empty"},
		{GetReturnReasonServiceModel{Reason: ""}, "reason cannot be empty"},
	}

	ch := make(chan *GetReturnReasonServiceResponse)
	defer close(ch)

	for _, tc := range models {
		p.mockValidator.EXPECT().ValidateStruct(gomock.Eq(&tc.given)).Return(errors.New(tc.expected))

		go p.purchaseOrderService.GetReturnReason(ch, &tc.given)

		response := <-ch

		p.EqualError(response.Error, tc.expected)
	}
}

func (p *PurchaseOrderServiceTestSuit) TestGetReturnReason_NonExistingReturnReason_ReturnsError() {
	model := &GetReturnReasonServiceModel{}

	expectedMessage := "return reason supplier not found"

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetReturnReason(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetReturnReasonResponse, model *supplier.GetReturnReasonModel) {
			ch <- &supplier.GetReturnReasonResponse{Error: errors.New(expectedMessage)}
		})

	ch := make(chan *GetReturnReasonServiceResponse)
	defer close(ch)

	go p.purchaseOrderService.GetReturnReason(ch, model)

	response := <-ch

	p.EqualError(response.Error, expectedMessage)
}

func (p *PurchaseOrderServiceTestSuit) TestGetReturnReason_ExistingReturnReason_ReturnsReturnReason() {
	model := &GetReturnReasonServiceModel{
		Reason: "test",
	}
	expectedId := int32(1)
	expectedCode := "test_code"

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetReturnReason(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetReturnReasonResponse, model *supplier.GetReturnReasonModel) {
			ch <- &supplier.GetReturnReasonResponse{
				Reason: model.Reason,
				Id:     expectedId,
				Code:   expectedCode,
			}
		})

	ch := make(chan *GetReturnReasonServiceResponse)
	defer close(ch)

	go p.purchaseOrderService.GetReturnReason(ch, model)

	response := <-ch

	p.Nil(response.Error)
	p.EqualValues(response.Reason, model.Reason)
	p.EqualValues(response.Id, expectedId)
	p.EqualValues(response.Code, expectedCode)
}

func (p *PurchaseOrderServiceTestSuit) TestSendPurchaseOrderInbound_ServiceModelValidation_ReturnsError() {
	var models = []struct {
		serviceModel SendPurchaseOrderInboundServiceModel
		orderType    string
		expected     string
	}{
		{SendPurchaseOrderInboundServiceModel{
			OrderId:        "",
			MessageId:      "messageId",
			OrderReference: "PO123",
			StoreId:        "store1",
			BillingId:      "billingId",
			OrderDetail:    []OrderDetail{{Sku: "123", Quantity: 5, IsOutOfOrder: false}},
		}, enum.PurchaseOrderTypeDmart, "orderId can not be empty"},
		{SendPurchaseOrderInboundServiceModel{
			OrderId:        "123",
			MessageId:      "",
			OrderReference: "PO123",
			StoreId:        "store1",
			BillingId:      "billingId",
			OrderDetail:    []OrderDetail{{Sku: "123", Quantity: 5, IsOutOfOrder: false}},
		}, enum.PurchaseOrderTypeDmart, "messageId can not be empty"},
		{SendPurchaseOrderInboundServiceModel{
			OrderId:        "123",
			MessageId:      "messageId",
			OrderReference: "",
			StoreId:        "store1",
			BillingId:      "billingId",
			OrderDetail:    []OrderDetail{{Sku: "123", Quantity: 5, IsOutOfOrder: false}},
		}, enum.PurchaseOrderTypeDmart, "orderReference can not be empty"},
		{SendPurchaseOrderInboundServiceModel{
			OrderId:        "123",
			MessageId:      "messageId",
			OrderReference: "PO123",
			StoreId:        "",
			BillingId:      "billingId",
			OrderDetail:    []OrderDetail{{Sku: "123", Quantity: 5, IsOutOfOrder: false}},
		}, enum.PurchaseOrderTypeDmart, "storeId can not be empty"},
		{SendPurchaseOrderInboundServiceModel{
			OrderId:        "123",
			MessageId:      "messageId",
			OrderReference: "PO123",
			StoreId:        "123",
			BillingId:      "",
			OrderDetail:    []OrderDetail{{Sku: "123", Quantity: 5, IsOutOfOrder: false}},
		}, enum.PurchaseOrderTypeDmart, "billingId can not be empty"},
		{SendPurchaseOrderInboundServiceModel{
			OrderId:        "123",
			MessageId:      "messageId",
			OrderReference: "PO123",
			StoreId:        "123",
			BillingId:      "123123123123123123123",
			OrderDetail:    []OrderDetail{{Sku: "123", Quantity: 5, IsOutOfOrder: false}},
		}, enum.PurchaseOrderTypeDmart, "billingId can not be longer than 20 chars"},
		{SendPurchaseOrderInboundServiceModel{
			OrderId:        "123",
			MessageId:      "messageId",
			OrderReference: "PO123",
			StoreId:        "123",
			BillingId:      "123",
			OrderDetail:    []OrderDetail{},
		}, enum.PurchaseOrderTypeDmart, "orderDetail can not be empty"},
	}

	ch := make(chan *SendPurchaseOrderInboundServiceResponse)
	defer close(ch)

	for _, tc := range models {
		p.mockValidator.EXPECT().ValidateStruct(gomock.Eq(&tc.serviceModel)).Return(errors.New(tc.expected))

		go p.purchaseOrderService.SendPurchaseOrderInbound(ch, &tc.serviceModel, tc.orderType)

		response := <-ch

		p.EqualError(response.Error, tc.expected)
	}
}

func (p *PurchaseOrderServiceTestSuit) TestSendPurchaseOrderInbound_LogoGivesError_ReturnsError() {
	model := &SendPurchaseOrderInboundServiceModel{
		OrderId:        "123",
		OrderReference: "PO123",
		StoreId:        "storeId",
		MessageId:      "messageId",
		BillingId:      "billingId",
		OrderDetail:    []OrderDetail{{Sku: "sku1", Quantity: 1, IsOutOfOrder: false}},
	}
	ch := make(chan *SendPurchaseOrderInboundServiceResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetPurchaseOrderInbound(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetPurchaseOrderInboundResponse, model *supplier.GetPurchaseOrderInboundModel) {
			ch <- &supplier.GetPurchaseOrderInboundResponse{
				Error: errors.New("no purchase supplier found"),
			}
		})

	p.mockLogger.EXPECT().Error(gomock.Any(), gomock.Any())
	p.mockLogoProxy.EXPECT().SendSupplierOrderCollection(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendSupplierOrderCollectionModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: errors.New("logo returned error"),
			}
		})

	go p.purchaseOrderService.SendPurchaseOrderInbound(ch, model, enum.PurchaseOrderTypeDmart)

	response := <-ch

	p.EqualError(response.Error, "logo returned error")
}

func (p *PurchaseOrderServiceTestSuit) TestSendPurchaseOrderInbound_CouldNotAddDatabase_ReturnsError() {
	model := &SendPurchaseOrderInboundServiceModel{
		OrderId:        "123",
		OrderReference: "PO123",
		StoreId:        "storeId",
		MessageId:      "messageId",
		BillingId:      "billingId",
		OrderDetail:    []OrderDetail{{Sku: "sku1", Quantity: 1, IsOutOfOrder: false}},
	}
	ch := make(chan *SendPurchaseOrderInboundServiceResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetPurchaseOrderInbound(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetPurchaseOrderInboundResponse, model *supplier.GetPurchaseOrderInboundModel) {
			ch <- &supplier.GetPurchaseOrderInboundResponse{
				Error: errors.New("no purchase supplier found"),
			}
		})

	p.mockLogger.EXPECT().Error(gomock.Any(), gomock.Any())
	p.mockLogoProxy.EXPECT().SendSupplierOrderCollection(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendSupplierOrderCollectionModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: nil,
			}
		})
	p.mockPurchaseOrderDb.EXPECT().AddPurchaseOrderInbound(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.PurchaseOrderDbBaseResponse, model *supplier.AddPurchaseOrderInboundModel) {
			ch <- &supplier.PurchaseOrderDbBaseResponse{
				Error: errors.New("could not add purchase supplier inbound"),
			}
		})

	go p.purchaseOrderService.SendPurchaseOrderInbound(ch, model, enum.PurchaseOrderTypeDmart)

	response := <-ch

	p.EqualError(response.Error, "could not add purchase supplier inbound")
}

func (p *PurchaseOrderServiceTestSuit) TestSendPurchaseOrderInbound_WithValidArgs_ReturnsSuccess() {
	model := &SendPurchaseOrderInboundServiceModel{
		OrderId:        "123",
		OrderReference: "PO123",
		StoreId:        "storeId",
		MessageId:      "messageId",
		BillingId:      "billingId",
		OrderDetail:    []OrderDetail{{Sku: "sku1", Quantity: 1, IsOutOfOrder: false}},
	}
	ch := make(chan *SendPurchaseOrderInboundServiceResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockPurchaseOrderDb.EXPECT().GetPurchaseOrderInbound(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.GetPurchaseOrderInboundResponse, model *supplier.GetPurchaseOrderInboundModel) {
			ch <- &supplier.GetPurchaseOrderInboundResponse{
				Error: errors.New("no purchase supplier found"),
			}
		})

	p.mockLogger.EXPECT().Error(gomock.Any(), gomock.Any())
	p.mockLogoProxy.EXPECT().SendSupplierOrderCollection(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendSupplierOrderCollectionModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: nil,
			}
		})
	p.mockPurchaseOrderDb.EXPECT().AddPurchaseOrderInbound(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplier.PurchaseOrderDbBaseResponse, model *supplier.AddPurchaseOrderInboundModel) {
			ch <- &supplier.PurchaseOrderDbBaseResponse{
				Error: nil,
			}
		})

	go p.purchaseOrderService.SendPurchaseOrderInbound(ch, model, enum.PurchaseOrderTypeDmart)

	response := <-ch

	p.Nil(response.Error)
}

func (p *PurchaseOrderServiceTestSuit) TestSendStoreRefund_LogoReturnsError_ReturnsError() {
	model := &SendStoreRefundServiceModel{
		StoreId:   "storeId",
		MessageId: "messageId",
	}
	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockLogoProxy.EXPECT().SendStoreRefund(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendStoreRefundModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: errors.New("logo returned error"),
			}
		})

	go p.purchaseOrderService.SendStoreRefund(ch, model)

	response := <-ch

	p.EqualError(response.Error, "logo returned error")
}

func (p *PurchaseOrderServiceTestSuit) TestSendStoreRefund_WithValidArgs_ReturnsSuccess() {
	model := &SendStoreRefundServiceModel{
		StoreId:   "storeId",
		MessageId: "messageId",
	}
	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockLogoProxy.EXPECT().SendStoreRefund(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendStoreRefundModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: nil,
			}
		})

	go p.purchaseOrderService.SendStoreRefund(ch, model)

	response := <-ch

	p.Nil(response.Error)
}

func (p *PurchaseOrderServiceTestSuit) TestGlobalPurchaseOrderCancellation_LogoReturnsError_ReturnsError() {
	model := &GlobalPurchaseOrderCancellationServiceModel{
		MessageId:        "messageId",
		POReference:      "ref",
		POGuidId:         "guid",
		CancellationDate: time.Now(),
	}
	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockLogoProxy.EXPECT().GlobalPurchaseOrderCancellation(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.GlobalPurchaseOrderCancellationModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: errors.New("logo returned error"),
			}
		})

	go p.purchaseOrderService.GlobalPurchaseOrderCancellation(ch, model)

	response := <-ch

	p.EqualError(response.Error, "logo returned error")
}

func (p *PurchaseOrderServiceTestSuit) TestGlobalPurchaseOrderCancellation_WithValidArgs_ReturnsSuccess() {
	model := &GlobalPurchaseOrderCancellationServiceModel{
		MessageId:        "messageId",
		POReference:      "ref",
		POGuidId:         "guid",
		CancellationDate: time.Now(),
	}
	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockLogoProxy.EXPECT().GlobalPurchaseOrderCancellation(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.GlobalPurchaseOrderCancellationModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: nil,
			}
		})

	go p.purchaseOrderService.GlobalPurchaseOrderCancellation(ch, model)

	response := <-ch

	p.Nil(response.Error)
	p.EqualValues(response.ResultCode, 0)
}

func (p *PurchaseOrderServiceTestSuit) TestSendGlobalPurchaseOrder_LogoReturnsError_ReturnsError() {
	model := &SendGlobalPurchaseOrderServiceModel{
		MessageId: "messageId",
		POSlips: []SendGlobalPurchaseOrderPoSlipServiceModel{
			{
				POReference:  "ref",
				POGuidId:     "guid",
				PODate:       time.Now(),
				SupplierCode: "supplierCode",
				PayPlanCode:  "planCode",
				StoreId:      "storeId",
				ProjectCode:  "projectCode",
				DeliveryDate: time.Now(),
				ProductList:  []SendGlobalPurchaseOrderPoSlipProductServiceModel{}},
		},
	}
	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockLogoProxy.EXPECT().SendGlobalPurchaseOrder(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendGlobalPurchaseOrderModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: errors.New("logo returned error"),
			}
		})

	go p.purchaseOrderService.SendGlobalPurchaseOrder(ch, model)

	response := <-ch

	p.EqualError(response.Error, "logo returned error")
}

func (p *PurchaseOrderServiceTestSuit) TestSendGlobalPurchaseOrder_WithValidArgs_ReturnsSuccess() {
	model := &SendGlobalPurchaseOrderServiceModel{
		MessageId: "messageId",
		POSlips: []SendGlobalPurchaseOrderPoSlipServiceModel{
			{
				POReference:  "ref",
				POGuidId:     "guid",
				PODate:       time.Now(),
				SupplierCode: "supplierCode",
				PayPlanCode:  "planCode",
				StoreId:      "storeId",
				ProjectCode:  "projectCode",
				DeliveryDate: time.Now(),
				ProductList:  []SendGlobalPurchaseOrderPoSlipProductServiceModel{}},
		},
	}
	ch := make(chan *PurchaseOrderServiceBaseResponse)
	defer close(ch)

	p.mockValidator.EXPECT().ValidateStruct(gomock.Any()).Return(nil)
	p.mockLogoProxy.EXPECT().SendGlobalPurchaseOrder(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendGlobalPurchaseOrderModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: nil,
			}
		})

	go p.purchaseOrderService.SendGlobalPurchaseOrder(ch, model)

	response := <-ch

	p.Nil(response.Error)
	p.EqualValues(response.ResultCode, 0)
}
