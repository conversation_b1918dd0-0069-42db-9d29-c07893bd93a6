// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/supplier/product_return_service.go

// Package supplier is a generated GoMock package.
package supplier

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIProductReturnService is a mock of IProductReturnService interface.
type MockIProductReturnService struct {
	ctrl     *gomock.Controller
	recorder *MockIProductReturnServiceMockRecorder
}

// MockIProductReturnServiceMockRecorder is the mock recorder for MockIProductReturnService.
type MockIProductReturnServiceMockRecorder struct {
	mock *MockIProductReturnService
}

// NewMockIProductReturnService creates a new mock instance.
func NewMockIProductReturnService(ctrl *gomock.Controller) *MockIProductReturnService {
	mock := &MockIProductReturnService{ctrl: ctrl}
	mock.recorder = &MockIProductReturnServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIProductReturnService) EXPECT() *MockIProductReturnServiceMockRecorder {
	return m.recorder
}

// SendProductReturn mocks base method.
func (m *MockIProductReturnService) SendProductReturn(ch chan *SendProductReturnServiceResponse, model *SendProductReturnServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendProductReturn", ch, model)
}

// SendProductReturn indicates an expected call of SendProductReturn.
func (mr *MockIProductReturnServiceMockRecorder) SendProductReturn(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendProductReturn", reflect.TypeOf((*MockIProductReturnService)(nil).SendProductReturn), ch, model)
}

// SendSupplierReturn mocks base method.
func (m *MockIProductReturnService) SendSupplierReturn(ch chan *SendProductReturnServiceResponse, model *SendSupplierReturnServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSupplierReturn", ch, model)
}

// SendSupplierReturn indicates an expected call of SendSupplierReturn.
func (mr *MockIProductReturnServiceMockRecorder) SendSupplierReturn(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSupplierReturn", reflect.TypeOf((*MockIProductReturnService)(nil).SendSupplierReturn), ch, model)
}
