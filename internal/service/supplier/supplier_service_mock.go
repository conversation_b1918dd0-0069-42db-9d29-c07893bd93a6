// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/supplier/supplier_service.go

// Package supplier is a generated GoMock package.
package supplier

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISupplierService is a mock of ISupplierService interface.
type MockISupplierService struct {
	ctrl     *gomock.Controller
	recorder *MockISupplierServiceMockRecorder
}

// MockISupplierServiceMockRecorder is the mock recorder for MockISupplierService.
type MockISupplierServiceMockRecorder struct {
	mock *MockISupplierService
}

// NewMockISupplierService creates a new mock instance.
func NewMockISupplierService(ctrl *gomock.Controller) *MockISupplierService {
	mock := &MockISupplierService{ctrl: ctrl}
	mock.recorder = &MockISupplierServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISupplierService) EXPECT() *MockISupplierServiceMockRecorder {
	return m.recorder
}

// GetSuppliersWithDetails mocks base method.
func (m *MockISupplierService) GetSuppliersWithDetails(ch chan *GetSupplierDetailResponse, supplierIds []int64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetSuppliersWithDetails", ch, supplierIds)
}

// GetSuppliersWithDetails indicates an expected call of GetSuppliersWithDetails.
func (mr *MockISupplierServiceMockRecorder) GetSuppliersWithDetails(ch, supplierIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuppliersWithDetails", reflect.TypeOf((*MockISupplierService)(nil).GetSuppliersWithDetails), ch, supplierIds)
}

// SendSuppliersToLogo mocks base method.
func (m *MockISupplierService) SendSuppliersToLogo(ch chan *SendSuppliersToLogoResponse, model SendSuppliersToLogoServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendSuppliersToLogo", ch, model)
}

// SendSuppliersToLogo indicates an expected call of SendSuppliersToLogo.
func (mr *MockISupplierServiceMockRecorder) SendSuppliersToLogo(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSuppliersToLogo", reflect.TypeOf((*MockISupplierService)(nil).SendSuppliersToLogo), ch, model)
}
