// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/supplier/purchase_order_service.go

// Package supplier is a generated GoMock package.
package supplier

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIPurchaseOrderService is a mock of IPurchaseOrderService interface.
type MockIPurchaseOrderService struct {
	ctrl     *gomock.Controller
	recorder *MockIPurchaseOrderServiceMockRecorder
}

// MockIPurchaseOrderServiceMockRecorder is the mock recorder for MockIPurchaseOrderService.
type MockIPurchaseOrderServiceMockRecorder struct {
	mock *MockIPurchaseOrderService
}

// NewMockIPurchaseOrderService creates a new mock instance.
func NewMockIPurchaseOrderService(ctrl *gomock.Controller) *MockIPurchaseOrderService {
	mock := &MockIPurchaseOrderService{ctrl: ctrl}
	mock.recorder = &MockIPurchaseOrderServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPurchaseOrderService) EXPECT() *MockIPurchaseOrderServiceMockRecorder {
	return m.recorder
}

// AddPurchaseOrderRefundToDb mocks base method.
func (m *MockIPurchaseOrderService) AddPurchaseOrderRefundToDb(ch chan *PurchaseOrderServiceBaseResponse, model *AddPurchaseOrderRefundServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddPurchaseOrderRefundToDb", ch, model)
}

// AddPurchaseOrderRefundToDb indicates an expected call of AddPurchaseOrderRefundToDb.
func (mr *MockIPurchaseOrderServiceMockRecorder) AddPurchaseOrderRefundToDb(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPurchaseOrderRefundToDb", reflect.TypeOf((*MockIPurchaseOrderService)(nil).AddPurchaseOrderRefundToDb), ch, model)
}

// AddPurchaseOrderToDb mocks base method.
func (m *MockIPurchaseOrderService) AddPurchaseOrderToDb(ch chan *PurchaseOrderServiceBaseResponse, model *AddPurchaseOrderToDbServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddPurchaseOrderToDb", ch, model)
}

// AddPurchaseOrderToDb indicates an expected call of AddPurchaseOrderToDb.
func (mr *MockIPurchaseOrderServiceMockRecorder) AddPurchaseOrderToDb(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPurchaseOrderToDb", reflect.TypeOf((*MockIPurchaseOrderService)(nil).AddPurchaseOrderToDb), ch, model)
}

// GetPurchaseOrderFromDb mocks base method.
func (m *MockIPurchaseOrderService) GetPurchaseOrderFromDb(ch chan *GetPurchaseOrderFromDbServiceResponse, model *GetPurchaseOrderServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPurchaseOrderFromDb", ch, model)
}

// GetPurchaseOrderFromDb indicates an expected call of GetPurchaseOrderFromDb.
func (mr *MockIPurchaseOrderServiceMockRecorder) GetPurchaseOrderFromDb(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseOrderFromDb", reflect.TypeOf((*MockIPurchaseOrderService)(nil).GetPurchaseOrderFromDb), ch, model)
}

// GetPurchaseOrderRefundFromDb mocks base method.
func (m *MockIPurchaseOrderService) GetPurchaseOrderRefundFromDb(ch chan *GetPurchaseOrderRefundServiceResponse, model *GetPurchaseOrderRefundServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetPurchaseOrderRefundFromDb", ch, model)
}

// GetPurchaseOrderRefundFromDb indicates an expected call of GetPurchaseOrderRefundFromDb.
func (mr *MockIPurchaseOrderServiceMockRecorder) GetPurchaseOrderRefundFromDb(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseOrderRefundFromDb", reflect.TypeOf((*MockIPurchaseOrderService)(nil).GetPurchaseOrderRefundFromDb), ch, model)
}

// GetReturnReason mocks base method.
func (m *MockIPurchaseOrderService) GetReturnReason(ch chan *GetReturnReasonServiceResponse, model *GetReturnReasonServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetReturnReason", ch, model)
}

// GetReturnReason indicates an expected call of GetReturnReason.
func (mr *MockIPurchaseOrderServiceMockRecorder) GetReturnReason(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReturnReason", reflect.TypeOf((*MockIPurchaseOrderService)(nil).GetReturnReason), ch, model)
}

// GlobalPurchaseOrderCancellation mocks base method.
func (m *MockIPurchaseOrderService) GlobalPurchaseOrderCancellation(ch chan *PurchaseOrderServiceBaseResponse, model *GlobalPurchaseOrderCancellationServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GlobalPurchaseOrderCancellation", ch, model)
}

// GlobalPurchaseOrderCancellation indicates an expected call of GlobalPurchaseOrderCancellation.
func (mr *MockIPurchaseOrderServiceMockRecorder) GlobalPurchaseOrderCancellation(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GlobalPurchaseOrderCancellation", reflect.TypeOf((*MockIPurchaseOrderService)(nil).GlobalPurchaseOrderCancellation), ch, model)
}

// SendGlobalPurchaseOrder mocks base method.
func (m *MockIPurchaseOrderService) SendGlobalPurchaseOrder(ch chan *PurchaseOrderServiceBaseResponse, model *SendGlobalPurchaseOrderServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendGlobalPurchaseOrder", ch, model)
}

// SendGlobalPurchaseOrder indicates an expected call of SendGlobalPurchaseOrder.
func (mr *MockIPurchaseOrderServiceMockRecorder) SendGlobalPurchaseOrder(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGlobalPurchaseOrder", reflect.TypeOf((*MockIPurchaseOrderService)(nil).SendGlobalPurchaseOrder), ch, model)
}

// SendPurchaseOrderInbound mocks base method.
func (m *MockIPurchaseOrderService) SendPurchaseOrderInbound(ch chan *SendPurchaseOrderInboundServiceResponse, model *SendPurchaseOrderInboundServiceModel, orderType string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendPurchaseOrderInbound", ch, model, orderType)
}

// SendPurchaseOrderInbound indicates an expected call of SendPurchaseOrderInbound.
func (mr *MockIPurchaseOrderServiceMockRecorder) SendPurchaseOrderInbound(ch, model, orderType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPurchaseOrderInbound", reflect.TypeOf((*MockIPurchaseOrderService)(nil).SendPurchaseOrderInbound), ch, model, orderType)
}

// SendStoreRefund mocks base method.
func (m *MockIPurchaseOrderService) SendStoreRefund(ch chan *PurchaseOrderServiceBaseResponse, model *SendStoreRefundServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendStoreRefund", ch, model)
}

// SendStoreRefund indicates an expected call of SendStoreRefund.
func (mr *MockIPurchaseOrderServiceMockRecorder) SendStoreRefund(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStoreRefund", reflect.TypeOf((*MockIPurchaseOrderService)(nil).SendStoreRefund), ch, model)
}
