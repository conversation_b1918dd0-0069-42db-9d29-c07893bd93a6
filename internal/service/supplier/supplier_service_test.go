package supplier

import (
	"errors"
	"testing"
	"time"

	"logo-adapter/internal/data/proxy/logo"
	supplierProxy "logo-adapter/internal/data/proxy/supplier"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	pbV4 "github.com/deliveryhero/dh-nv-proto-golang/packages/supplier_portal/v4/supplierpb"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type SupplierServiceTestSuite struct {
	suite.Suite
	supplierService ISupplierService
	mockEnvironment *env.MockIEnvironment
	mockLogger      *logger.MockILogger
	mockValidator   *validator.MockIValidator
	mockCacher      *cacher.MockICacher
	logoProxy       *logo.MockILogoProxy
	supplierProxy   *supplierProxy.MockISupplierProxy
}

// Run suite.
func TestService(t *testing.T) {
	suite.Run(t, new(SupplierServiceTestSuite))
}

// Runs before each test in the suite.
func (s *SupplierServiceTestSuite) SetupTest() {
	s.T().Log("Setup")

	ctrl := gomock.NewController(s.T())
	defer ctrl.Finish()

	s.mockEnvironment = env.NewMockIEnvironment(ctrl)
	s.mockLogger = logger.NewMockILogger(ctrl)
	s.mockValidator = validator.NewMockIValidator(ctrl)
	s.mockCacher = cacher.NewMockICacher(ctrl)
	s.logoProxy = logo.NewMockILogoProxy(ctrl)
	s.supplierProxy = supplierProxy.NewMockISupplierProxy(ctrl)

	s.supplierService = NewSupplierService(s.mockEnvironment, s.mockLogger, s.mockValidator, s.mockCacher, s.supplierProxy, s.logoProxy)
}

// Runs after each test in the suite.
func (s *SupplierServiceTestSuite) TearDownTest() {
	s.T().Log("Teardown")
}

func (s *SupplierServiceTestSuite) TestSendSuppliersToLogo_HappyPath_Success() {
	sendSuppliersToLogoModel := SendSuppliersToLogoServiceModel{
		UpdatedAt: time.Now(),
	}

	supplier := pbV4.Supplier{
		Address: &postaladdress.PostalAddress{
			AddressLines: []string{"Address1", "Address2"},
			PostalCode:   "34320",
			Locality:     "tr",
			Sublocality:  "tr",
			RegionCode:   "tr",
		},
		SupplierCode:    "10000",
		Name:            "Test",
		SupplierTaxId:   "1",
		TaxOfficeName:   "Test",
		Cellphone:       "5400000000",
		Email:           "<EMAIL>",
		SupplierPayPlan: "Test",
		ProjectCode:     "Code",
		Iban:            "Iban",
		GroupName:       "Test",
	}

	getSupplierResponse := &pbV4.GetSuppliersUpdatedAfterResponse{}

	getSupplierResponse.Suppliers = append(getSupplierResponse.Suppliers, &supplier)

	getSupplierRequestModel := supplierProxy.GetSuppliersModel{
		Page:      1,
		PageSize:  500,
		UpdatedAt: timestamppb.New(sendSuppliersToLogoModel.UpdatedAt),
	}

	s.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(sendSuppliersToLogoModel)).
		Return(nil)

	s.supplierProxy.
		EXPECT().
		GetSuppliers(gomock.Any(), gomock.Eq(getSupplierRequestModel)).
		DoAndReturn(func(ch chan *supplierProxy.GetSuppliersResponse, model supplierProxy.GetSuppliersModel) {
			ch <- &supplierProxy.GetSuppliersResponse{Error: nil, Response: getSupplierResponse}
		})

	s.logoProxy.
		EXPECT().
		AddOrUpdateGlobalSuppliers(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.AddOrUpdateGlobalSuppliersModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil}
		})

	s.mockLogger.
		EXPECT().
		Info(gomock.Any(), gomock.Any())

	ch := make(chan *SendSuppliersToLogoResponse)

	go s.supplierService.SendSuppliersToLogo(ch, sendSuppliersToLogoModel)
	response := <-ch

	s.Equal(response.Error, nil)
}

func (s *SupplierServiceTestSuite) TestSendSuppliersToLogo_ProxyReturnsZeroSupplier_Success() {
	sendSuppliersToLogoModel := SendSuppliersToLogoServiceModel{
		UpdatedAt: time.Now(),
	}

	s.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(sendSuppliersToLogoModel)).
		Return(nil)

	s.supplierProxy.
		EXPECT().
		GetSuppliers(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplierProxy.GetSuppliersResponse, model supplierProxy.GetSuppliersModel) {
			ch <- &supplierProxy.GetSuppliersResponse{Error: nil, Response: &pbV4.GetSuppliersUpdatedAfterResponse{}}
		})

	ch := make(chan *SendSuppliersToLogoResponse)

	go s.supplierService.SendSuppliersToLogo(ch, sendSuppliersToLogoModel)
	response := <-ch

	s.Equal(response.Error, nil)
}

func (s *SupplierServiceTestSuite) TestSendSuppliersToLogo_SupplierProxyReturnsError_ReturnsError() {
	sendSuppliersToLogoModel := SendSuppliersToLogoServiceModel{
		UpdatedAt: time.Now(),
	}

	supplierProxyError := "SupplierProxy returns error"

	s.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(sendSuppliersToLogoModel)).
		Return(nil)

	s.supplierProxy.
		EXPECT().
		GetSuppliers(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplierProxy.GetSuppliersResponse, model supplierProxy.GetSuppliersModel) {
			ch <- &supplierProxy.GetSuppliersResponse{Error: errors.New(supplierProxyError)}
		})

	ch := make(chan *SendSuppliersToLogoResponse)

	go s.supplierService.SendSuppliersToLogo(ch, sendSuppliersToLogoModel)
	response := <-ch

	s.EqualError(response.Error, supplierProxyError)
}

func (s *SupplierServiceTestSuite) TestSendSuppliersToLogo_LogoProxyReturnsError_ReturnsError() {
	sendSuppliersToLogoModel := SendSuppliersToLogoServiceModel{
		UpdatedAt: time.Now(),
	}

	supplier := pbV4.Supplier{
		Address: &postaladdress.PostalAddress{
			AddressLines: []string{"Address1", "Address2"},
			PostalCode:   "34320",
			Locality:     "tr",
			Sublocality:  "tr",
			RegionCode:   "tr",
		},
		SupplierCode:    "10000",
		Name:            "Test",
		SupplierTaxId:   "1",
		TaxOfficeName:   "Test",
		Cellphone:       "5400000000",
		Email:           "<EMAIL>",
		SupplierPayPlan: "Test",
		ProjectCode:     "Code",
		Iban:            "Iban",
		GroupName:       "Test",
	}

	getSupplierResponse := &pbV4.GetSuppliersUpdatedAfterResponse{}

	getSupplierResponse.Suppliers = append(getSupplierResponse.Suppliers, &supplier)

	logoProxyError := "LogoProxy returns error"

	s.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(sendSuppliersToLogoModel)).
		Return(nil)

	s.supplierProxy.
		EXPECT().
		GetSuppliers(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *supplierProxy.GetSuppliersResponse, model supplierProxy.GetSuppliersModel) {
			ch <- &supplierProxy.GetSuppliersResponse{Error: nil, Response: getSupplierResponse}
		})

	s.logoProxy.
		EXPECT().
		AddOrUpdateGlobalSuppliers(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.AddOrUpdateGlobalSuppliersModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: errors.New(logoProxyError)}
		})

	ch := make(chan *SendSuppliersToLogoResponse)

	go s.supplierService.SendSuppliersToLogo(ch, sendSuppliersToLogoModel)
	response := <-ch

	s.EqualError(response.Error, logoProxyError)
}

func (s *SupplierServiceTestSuite) TestGetSuppliersWithDetails_HappyPath_Success() {

	supplier := pbV4.Supplier{
		Address: &postaladdress.PostalAddress{
			AddressLines: []string{"Address1", "Address2"},
			PostalCode:   "34320",
			Locality:     "tr",
			Sublocality:  "tr",
			RegionCode:   "tr",
		},
		SupplierCode:    "10000",
		Name:            "Test",
		SupplierTaxId:   "1",
		TaxOfficeName:   "Test",
		Cellphone:       "5400000000",
		Email:           "<EMAIL>",
		SupplierPayPlan: "Test",
		ProjectCode:     "Code",
		Iban:            "Iban",
		GroupName:       "Test",
	}

	getSupplierResponse := []*pbV4.Supplier{}

	getSupplierResponse = append(getSupplierResponse, &supplier)

	getSupplierRequestModel := supplierProxy.GetSupplierListWithDetailModel{
		Ids: []int64{100},
	}

	s.supplierProxy.
		EXPECT().
		GetSupplierListWithDetails(gomock.Any(), gomock.Eq(getSupplierRequestModel)).
		DoAndReturn(func(ch chan *supplierProxy.GetSupplierListWithDetailsResponse, model supplierProxy.GetSupplierListWithDetailModel) {
			ch <- &supplierProxy.GetSupplierListWithDetailsResponse{Error: nil, SupplierList: getSupplierResponse}
		})

	ch := make(chan *GetSupplierDetailResponse)

	go s.supplierService.GetSuppliersWithDetails(ch, getSupplierRequestModel.Ids)
	response := <-ch

	s.Equal(response.Error, nil)
}

func (s *SupplierServiceTestSuite) TestGetSuppliersWithDetails_SupplierProxyReturnsError_ReturnsError() {

	supplierProxyError := "SupplierProxy returns error"

	getSupplierRequestModel := supplierProxy.GetSupplierListWithDetailModel{
		Ids: []int64{100},
	}

	s.supplierProxy.
		EXPECT().
		GetSupplierListWithDetails(gomock.Any(), gomock.Eq(getSupplierRequestModel)).
		DoAndReturn(func(ch chan *supplierProxy.GetSupplierListWithDetailsResponse, model supplierProxy.GetSupplierListWithDetailModel) {
			ch <- &supplierProxy.GetSupplierListWithDetailsResponse{Error: errors.New(supplierProxyError)}
		})

	ch := make(chan *GetSupplierDetailResponse)

	go s.supplierService.GetSuppliersWithDetails(ch, getSupplierRequestModel.Ids)
	response := <-ch

	s.EqualError(response.Error, supplierProxyError)
}
