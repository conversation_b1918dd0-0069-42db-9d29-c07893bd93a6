package supplier

import (
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/data/proxy/supplier"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type ISupplierService interface {
	SendSuppliersToLogo(ch chan *SendSuppliersToLogoResponse, model SendSuppliersToLogoServiceModel)
	GetSuppliersWithDetails(ch chan *GetSupplierDetailResponse, supplierIds []int64)
}

type SupplierService struct {
	environment   env.IEnvironment
	loggr         logger.ILogger
	validatr      validator.IValidator
	cachr         cacher.ICacher
	supplierProxy supplier.ISupplierProxy
	logoProxy     logo.ILogoProxy
}

func NewSupplierService(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, supplierProxy supplier.ISupplierProxy, logoProxy logo.ILogoProxy) ISupplierService {
	service := SupplierService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if supplierProxy == nil {
		service.supplierProxy = supplier.NewSupplierProxy(environment, loggr, validatr, cachr)
	} else {
		service.supplierProxy = supplierProxy
	}

	if logoProxy == nil {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	} else {
		service.logoProxy = logoProxy
	}

	return &service
}

func (s *SupplierService) SendSuppliersToLogo(ch chan *SendSuppliersToLogoResponse, model SendSuppliersToLogoServiceModel) {
	valErr := s.validatr.ValidateStruct(model)
	if valErr != nil {
		ch <- &SendSuppliersToLogoResponse{
			Error: valErr,
		}
		return
	}

	supplierProxyChannel := make(chan *supplier.GetSuppliersResponse)
	defer close(supplierProxyChannel)

	go s.supplierProxy.GetSuppliers(supplierProxyChannel, supplier.GetSuppliersModel{
		Page:      1,
		PageSize:  500,
		UpdatedAt: timestamppb.New(model.UpdatedAt),
	})

	supplierProxyResponse := <-supplierProxyChannel
	if supplierProxyResponse.Error != nil {
		ch <- &SendSuppliersToLogoResponse{
			Error: supplierProxyResponse.Error,
		}
		return
	}

	var suppliers []logo.AddOrUpdateGlobalSuppliersSupplierModel

	for _, supplier := range supplierProxyResponse.Response.GetSuppliers() {
		var addressLineOne, addressLineTwo string
		addressLineCount := len(supplier.Address.AddressLines)

		if addressLineCount > 0 {
			addressLineOne = supplier.Address.AddressLines[0]
			if addressLineCount > 1 {
				addressLineTwo = supplier.Address.AddressLines[1]
			}
		}

		suppliers = append(suppliers, logo.AddOrUpdateGlobalSuppliersSupplierModel{
			SupplierCode:   supplier.SupplierCode,
			SupplierName:   supplier.Name,
			Taxnr:          supplier.SupplierTaxId,
			TaxOffice:      supplier.TaxOfficeName,
			TelNumber:      supplier.Cellphone,
			Email:          supplier.Email,
			AddressLineOne: addressLineOne,
			AddressLineTwo: addressLineTwo,
			Town:           supplier.Address.Sublocality,
			City:           supplier.Address.Locality,
			Country:        supplier.Address.RegionCode,
			PayPlanCode:    supplier.SupplierPayPlan,
			ProjectCode:    supplier.ProjectCode,
			IBAN:           supplier.Iban,
			Mark:           supplier.GroupName,
			PostCode:       supplier.Address.PostalCode,
		})
	}

	if len(suppliers) == 0 {
		ch <- &SendSuppliersToLogoResponse{
			Error: nil,
		}
		return
	}

	logoProxyChannel := make(chan *logo.LogoProxyBaseResponse)
	defer close(logoProxyChannel)
	logoProxyModel := logo.AddOrUpdateGlobalSuppliersModel{
		MessageId: uuid.NewString(),
		Suppliers: suppliers,
	}

	go s.logoProxy.AddOrUpdateGlobalSuppliers(logoProxyChannel, &logoProxyModel)

	logoProxyResponse := <-logoProxyChannel

	if logoProxyResponse.Error != nil {
		ch <- &SendSuppliersToLogoResponse{
			Error: logoProxyResponse.Error,
		}
		return
	}

	s.loggr.Info("Updated Suppliers", zap.Any("Logo Proxy Model", logoProxyModel))

	var supplierNames []string
	for _, sp := range supplierProxyResponse.Response.GetSuppliers() {
		supplierNames = append(supplierNames, sp.Name)
	}
	ch <- &SendSuppliersToLogoResponse{
		SupplierNames: supplierNames,
	}
}

func (s *SupplierService) GetSuppliersWithDetails(ch chan *GetSupplierDetailResponse, supplierIds []int64) {
	supplierProxyChannel := make(chan *supplier.GetSupplierListWithDetailsResponse)
	defer close(supplierProxyChannel)

	go s.supplierProxy.GetSupplierListWithDetails(supplierProxyChannel, supplier.GetSupplierListWithDetailModel{
		Ids: supplierIds,
	})

	response := <-supplierProxyChannel

	if response.Error != nil {
		ch <- &GetSupplierDetailResponse{
			Error: response.Error,
		}
		return
	}

	var suppliers []SupplierDetail
	for _, s := range response.SupplierList {
		suppliers = append(suppliers, SupplierDetail{
			Id:                s.Id,
			Name:              s.Name,
			CreatedAt:         s.CreatedAt.AsTime(),
			UpdatedA:          s.UpdatedAt.AsTime(),
			SupplierFinanceId: s.SupplierFinanceId,
			SupplierTaxId:     s.SupplierTaxId,
			CountryId:         int(s.CountryId),
			CityId:            int(s.CityId),
			Complement:        s.Complement,
			Email:             s.Email,
			Cellphone:         s.Cellphone,
			LandlineTelephone: s.LandlineTelephone,
			Stores:            int(s.Stores),
			Products:          int(s.Products),
			CurrencyId:        int(s.CurrencyId),
			ImageUrl:          s.ImageUrl,
			TaxOfficeName:     s.TaxOfficeName,
			ProjectCode:       s.ProjectCode,
			Iban:              s.Iban,
			GroupName:         s.GroupName,
			SupplierCode:      s.SupplierCode,
			SupplierPayPlanId: s.SupplierPayPlan,
		})
	}

	ch <- &GetSupplierDetailResponse{
		Error:    nil,
		Supplier: suppliers,
	}
}
