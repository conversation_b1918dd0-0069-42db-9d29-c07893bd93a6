package supplier

import "time"

type SendPurchaseOrderInboundServiceModel struct {
	MessageId       string `validate:"required"`
	TransactionDate time.Time
	OrderId         string `validate:"required"`
	OrderReference  string `validate:"required"`
	StoreId         string `validate:"required"`
	BillingId       string `validate:"required"`
	BillingDate     time.Time
	OrderDetail     []OrderDetail `validate:"required"`
}

type OrderDetail struct {
	Sku          string `validate:"required"`
	Quantity     int    `validate:"gte=0"`
	IsOutOfOrder bool
}

type SendGlobalPurchaseOrderServiceModel struct {
	MessageId       string                                      `json:"MessageId"`
	TransactionDate time.Time                                   `json:"TransactionDate"`
	POSlips         []SendGlobalPurchaseOrderPoSlipServiceModel `json:"PO_Slips"`
}

type SendGlobalPurchaseOrderPoSlipServiceModel struct {
	POReference  string                                             `json:"PO_Reference"`
	POGuidId     string                                             `json:"PO_Guid_Id"`
	PODate       time.Time                                          `json:"PO_Date"`
	SupplierCode string                                             `json:"SupplierCode"`
	PayPlanCode  string                                             `json:"PayPlanCode"`
	StoreId      string                                             `json:"StoreId"`
	ProjectCode  string                                             `json:"ProjectCode"`
	DeliveryDate time.Time                                          `json:"DeliveryDate"`
	ProductList  []SendGlobalPurchaseOrderPoSlipProductServiceModel `json:"ProductList"`
}

type SendGlobalPurchaseOrderPoSlipProductServiceModel struct {
	SKU      string  `json:"SKU"`
	Quantity int     `json:"Quantity"`
	Price    float64 `json:"Price"`
	VatRate  float32 `json:"VatRate"`
}

type SendStoreRefundServiceModel struct {
	MessageId       string                `json:"MessageId"`
	TransactionDate time.Time             `json:"TransactionDate"`
	StoreId         string                `json:"StoreId"`
	SlipType        int                   `json:"SlipType"`
	CarrierType     string                `json:"CarrierType"`
	SupplierCode    string                `json:"SupplierCode"`
	DispatchId      string                `json:"DispatchId"`
	DispatchDate    time.Time             `json:"DispatchDate"`
	ProductList     []ProductServiceModel `json:"ProductList"`
}

type ProductServiceModel struct {
	SKU        string `json:"SKU"`
	Quantity   int    `json:"Quantity"`
	ReasonCode string `json:"ReasonCode"`
}

type GlobalPurchaseOrderCancellationServiceModel struct {
	MessageId        string    `json:"MessageId"`
	POGuidId         string    `json:"PO_Guid_Id"`
	POReference      string    `json:"PO_Reference"`
	CancellationDate time.Time `json:"Cancellation_Date"`
}

type AddPurchaseOrderToDbServiceModel struct {
	OrderId        string `json:"order_id" validate:"required"`
	OrderReference string `json:"order_reference" validate:"required"`
	OrderType      string `json:"order_type" validate:"required"`
	EventType      string `json:"event_type" validate:"required"`
}

type GetPurchaseOrderServiceModel struct {
	OrderId   string `json:"order_id" validate:"required"`
	EventType string `json:"event_type" validate:"required"`
}

type GetReturnReasonServiceModel struct {
	Reason string `json:"reason" validate:"required"`
}

type AddPurchaseOrderRefundServiceModel struct {
	OrderId        string `json:"order_id" validate:"required"`
	OrderReference string `json:"order_reference" validate:"required"`
}

type GetPurchaseOrderRefundServiceModel struct {
	OrderReference string `json:"order_reference" validate:"required"`
}
