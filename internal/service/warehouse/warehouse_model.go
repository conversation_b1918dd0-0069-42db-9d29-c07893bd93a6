package warehouse

import "time"

type AddOrUpdateWarehouseServiceModel struct {
	GlobalEntityId   string    `validate:"required"`
	PlatformVendorId string    `validate:"required"`
	WarehouseId      string    `validate:"required"`
	WarehouseName    string    `validate:"required"`
	Address          string    `validate:"required"`
	City             string    `validate:"required"`
	PayloadTimestamp time.Time `validate:"required"`
	IsActive         bool
}
