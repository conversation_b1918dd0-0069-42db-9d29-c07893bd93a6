package warehouse

import (
	"errors"
	"testing"

	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type WarehouseTransferServiceTestSuit struct {
	suite.Suite
	storeTransferService IWarehouseTransferService
	mockEnvironment      *env.MockIEnvironment
	mockLogger           *logger.MockILogger
	mockValidator        *validator.MockIValidator
	mockCacher           *cacher.MockICacher
	mockLogoProxy        *logo.MockILogoProxy
}

func TestWarehouseTransferService(t *testing.T) {
	suite.Run(t, new(WarehouseTransferServiceTestSuit))
}

// Runs before each test in the suite.
func (p *WarehouseTransferServiceTestSuit) SetupTest() {
	p.T().Log("Setup")

	ctrl := gomock.NewController(p.T())
	defer ctrl.Finish()

	p.mockEnvironment = env.NewMockIEnvironment(ctrl)
	p.mockLogger = logger.NewMockILogger(ctrl)
	p.mockValidator = validator.NewMockIValidator(ctrl)
	p.mockCacher = cacher.NewMockICacher(ctrl)
	p.mockLogoProxy = logo.NewMockILogoProxy(ctrl)

	p.storeTransferService = NewWarehouseTransferService(p.mockEnvironment, p.mockLogger, p.mockValidator, p.mockCacher, p.mockLogoProxy)
}

// Runs after each test in the suite.
func (p *WarehouseTransferServiceTestSuit) TearDownTest() {
	p.T().Log("Teardown")
}

func (p *WarehouseTransferServiceTestSuit) TestSendStoreTransferInboundCollection_ServiceModelValidation_ReturnsError() {
	model := SendWarehouseTransferCollectionModel{
		MessageId:   "messageId",
		OrderDetail: []OrderDetailModel{{SKU: "mrk", Quantity: 1}},
	}

	ch := make(chan *SendWarehouseTransferCollectionResponse)
	defer close(ch)

	p.mockLogger.EXPECT().Error(gomock.Any(), gomock.Any())
	p.mockLogoProxy.EXPECT().SendStoreTransferCollection(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendStoreTransferCollectionModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: errors.New("logo returned error"),
			}
		})

	go p.storeTransferService.SendStoreTransferInboundCollection(ch, model)

	response := <-ch

	p.EqualError(response.Error, "logo returned error")
}

func (p *WarehouseTransferServiceTestSuit) TestSendStoreTransferInboundCollection_WithValidArgs_ReturnsSuccess() {
	model := SendWarehouseTransferCollectionModel{
		MessageId:   "messageId",
		OrderDetail: []OrderDetailModel{{SKU: "mrk", Quantity: 1}},
	}

	ch := make(chan *SendWarehouseTransferCollectionResponse)
	defer close(ch)

	p.mockLogoProxy.EXPECT().SendStoreTransferCollection(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendStoreTransferCollectionModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: nil,
			}
		})

	go p.storeTransferService.SendStoreTransferInboundCollection(ch, model)

	response := <-ch

	p.Nil(response.Error)
}

func (p *WarehouseTransferServiceTestSuit) TestSendStoreTransferOutboundCollection_ServiceModelValidation_ReturnsError() {
	model := SendWarehouseTransferCollectionModel{
		MessageId:   "messageId",
		OrderDetail: []OrderDetailModel{{SKU: "mrk", Quantity: 1}},
	}

	ch := make(chan *SendWarehouseTransferCollectionResponse)
	defer close(ch)

	p.mockLogger.EXPECT().Error(gomock.Any(), gomock.Any())
	p.mockLogoProxy.EXPECT().SendStoreTransferCollection(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendStoreTransferCollectionModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: errors.New("logo returned error"),
			}
		})

	go p.storeTransferService.SendStoreTransferOutboundCollection(ch, model)

	response := <-ch

	p.EqualError(response.Error, "logo returned error")
}

func (p *WarehouseTransferServiceTestSuit) TestSendStoreTransferOutboundCollection_WithValidArgs_ReturnsSuccess() {
	model := SendWarehouseTransferCollectionModel{
		MessageId:   "messageId",
		OrderDetail: []OrderDetailModel{{SKU: "mrk", Quantity: 1}},
	}

	ch := make(chan *SendWarehouseTransferCollectionResponse)
	defer close(ch)

	p.mockLogoProxy.EXPECT().SendStoreTransferCollection(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendStoreTransferCollectionModel) {
			ch <- &logo.LogoProxyBaseResponse{
				Error: nil,
			}
		})

	go p.storeTransferService.SendStoreTransferOutboundCollection(ch, model)

	response := <-ch

	p.Nil(response.Error)
}
