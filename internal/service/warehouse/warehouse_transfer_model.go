package warehouse

import "time"

type OrderDetailModel struct {
	Quantity int    `validate:"gte=0"`
	SKU      string `validate:"required"`
}

type SendWarehouseTransferCollectionModel struct {
	MessageId                string             `validate:"required"`
	TransactionDate          time.Time          `validate:"required"`
	TransferId               string             `validate:"required"`
	TransferReference        string             `validate:"required"`
	SourceStoreId            string             `validate:"required"`
	SourceWarehouseType      int                `validate:"gt=0"`
	TargetStoreId            string             `validate:"required"`
	TargetWarehouseType      int                `validate:"gt=0"`
	DispatchId               string             `validate:"required"`
	TransferDate             time.Time          `validate:"required"`
	OrderDetail              []OrderDetailModel `validate:"required"`
	DriverName               string             `validate:"required"`
	DriverIdentityCardNumber string             `validate:"required"`
	DriverPlate              string             `validate:"required"`
}
