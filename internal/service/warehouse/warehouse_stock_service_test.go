package warehouse

import (
	"errors"
	"testing"

	warehouseDb "logo-adapter/internal/data/database/warehouse"
	"logo-adapter/internal/data/proxy/logistic"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	pb "github.com/deliveryhero/dh-nv-proto-golang/packages/inventory_management/movements/event/v1"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

type WarehouseStockServiceTestSuite struct {
	suite.Suite
	storeStockService       IWarehouseStockService
	mockEnvironment         *env.MockIEnvironment
	mockLogger              *logger.MockILogger
	mockValidator           *validator.MockIValidator
	mockCacher              *cacher.MockICacher
	mockLogoProxy           *logo.MockILogoProxy
	mockStockAdjustmentDb   *warehouseDb.MockIWarehouseStockDb
	mockServerConfiguration *configuration.MockIServerConfigurationService
	mockLogisticProxy       *logistic.MockILogisticProxy
}

func TestWarehouseStockService(t *testing.T) {
	suite.Run(t, new(WarehouseStockServiceTestSuite))
}

func (p *WarehouseStockServiceTestSuite) SetupTest() {
	p.T().Log("Setup")

	ctrl := gomock.NewController(p.T())
	defer ctrl.Finish()

	p.mockEnvironment = env.NewMockIEnvironment(ctrl)
	p.mockLogger = logger.NewMockILogger(ctrl)
	p.mockValidator = validator.NewMockIValidator(ctrl)
	p.mockCacher = cacher.NewMockICacher(ctrl)
	p.mockLogoProxy = logo.NewMockILogoProxy(ctrl)
	p.mockStockAdjustmentDb = warehouseDb.NewMockIWarehouseStockDb(ctrl)
	p.mockServerConfiguration = configuration.NewMockIServerConfigurationService(ctrl)
	p.mockLogisticProxy = logistic.NewMockILogisticProxy(ctrl)

	p.mockLogger.EXPECT().With(gomock.Any())

	p.storeStockService = NewWarehouseStockService(p.mockEnvironment, p.mockLogger, p.mockValidator, p.mockCacher, p.mockLogoProxy, p.mockStockAdjustmentDb, p.mockServerConfiguration, p.mockLogisticProxy)
}

func (p *WarehouseStockServiceTestSuite) TearDownTest() {
	p.T().Log("Teardown")
}

func (p *WarehouseStockServiceTestSuite) TestService_ExcludeReasonCode_ReturnsNotError() {
	//Given
	model := StockMovementServiceModel{
		MessageId: "",
		Event: pb.MovementsEvent{
			Id:          "1be0de6a-3a35-4e9d-834f-3a3389ee7b25",
			Timestamp:   timestamppb.Now(),
			ReasonCode:  pb.ReasonCode_REASON_CODE_EVERYDAY_COFFEE_SALE,
			EventType:   pb.EventType_EVENT_TYPE_MANUAL_ADJUSTMENT,
			TransferRef: &wrapperspb.StringValue{Value: "313131"},
			WarehouseId: "13004c8a-45e1-ea11-80d9-0050569c6abd",
			UserEmail:   &wrapperspb.StringValue{Value: "<EMAIL>"},
			Products: []*pb.MovementsEvent_Product{
				{MovementId: "63e1246b-cc06-4700-996e-1e2895cee787",
					Sku:                         "MRK.02076",
					CurrentStockOnHandQuantity:  0,
					PreviousStockOnHandQuantity: 0},
			},
		},
	}

	// When
	serviceCh := make(chan *StockMovementServiceResponse)

	defer close(serviceCh)

	go p.storeStockService.StockMovement(serviceCh, model)

	response := <-serviceCh

	// Then
	p.Nil(response.Error)
}

func (p *WarehouseStockServiceTestSuite) TestService_RelatedReasonError_ReturnsError() {
	//Given
	model := StockMovementServiceModel{
		MessageId: "",
		Event: pb.MovementsEvent{
			Id:          "1be0de6a-3a35-4e9d-834f-3a3389ee7b25",
			Timestamp:   timestamppb.Now(),
			ReasonCode:  -1,
			EventType:   pb.EventType_EVENT_TYPE_MANUAL_ADJUSTMENT,
			TransferRef: &wrapperspb.StringValue{Value: "313131"},
			WarehouseId: "13004c8a-45e1-ea11-80d9-0050569c6abd",
			UserEmail:   &wrapperspb.StringValue{Value: "<EMAIL>"},
			Products: []*pb.MovementsEvent_Product{
				{MovementId: "63e1246b-cc06-4700-996e-1e2895cee787",
					Sku:                         "MRK.02076",
					CurrentStockOnHandQuantity:  0,
					PreviousStockOnHandQuantity: 0},
			},
		},
	}

	p.mockStockAdjustmentDb.
		EXPECT().
		GetReasonCode(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *warehouseDb.GetReasonCodesResponse, model *warehouseDb.GetReasonCodesModel) {
			ch <- &warehouseDb.GetReasonCodesResponse{
				Error: errors.New("error"),
			}
			return
		})

	// When
	serviceCh := make(chan *StockMovementServiceResponse)

	defer close(serviceCh)

	go p.storeStockService.StockMovement(serviceCh, model)

	response := <-serviceCh

	// Then
	p.EqualError(response.Error, "error")
}

func (p *WarehouseStockServiceTestSuite) TestSendStoreTransferShrinkageToLogo_HappyPath_Success() {
	//Given
	model := StockMovementServiceModel{
		MessageId: "",
		Event: pb.MovementsEvent{
			Id:          "1be0de6a-3a35-4e9d-834f-3a3389ee7b25",
			Timestamp:   timestamppb.Now(),
			ReasonCode:  pb.ReasonCode_REASON_CODE_STOCK_ADDITION,
			EventType:   pb.EventType_EVENT_TYPE_STORE_TRANSFER_SHRINKAGE,
			TransferRef: &wrapperspb.StringValue{Value: "313131"},
			WarehouseId: "13004c8a-45e1-ea11-80d9-0050569c6abd",
			UserEmail:   &wrapperspb.StringValue{Value: "<EMAIL>"},
			Products: []*pb.MovementsEvent_Product{
				{MovementId: "63e1246b-cc06-4700-996e-1e2895cee787",
					Sku:                         "MRK.02076",
					CurrentStockOnHandQuantity:  0,
					PreviousStockOnHandQuantity: 0},
			},
		},
	}

	p.mockStockAdjustmentDb.
		EXPECT().
		GetReasonCode(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *warehouseDb.GetReasonCodesResponse, model *warehouseDb.GetReasonCodesModel) {
			ch <- &warehouseDb.GetReasonCodesResponse{
				Error: nil,
			}
			return
		})

	p.mockLogoProxy.
		EXPECT().
		SendStoreTransferShrinkage(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendStoreTransferShrinkageModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: nil}
			return
		})

	// When
	serviceCh := make(chan *StockMovementServiceResponse)

	defer close(serviceCh)

	go p.storeStockService.StockMovement(serviceCh, model)

	response := <-serviceCh

	// Then
	p.Nil(response.Error)
}

func (p *WarehouseStockServiceTestSuite) TestService_EventTypeError_ReturnsError() {
	//Given
	model := StockMovementServiceModel{
		MessageId: "",
		Event: pb.MovementsEvent{
			Id:          "1be0de6a-3a35-4e9d-834f-3a3389ee7b25",
			Timestamp:   timestamppb.Now(),
			ReasonCode:  pb.ReasonCode_REASON_CODE_STOCK_ADDITION,
			EventType:   -1,
			TransferRef: &wrapperspb.StringValue{Value: "313131"},
			WarehouseId: "13004c8a-45e1-ea11-80d9-0050569c6abd",
			UserEmail:   &wrapperspb.StringValue{Value: "<EMAIL>"},
			Products: []*pb.MovementsEvent_Product{
				{MovementId: "63e1246b-cc06-4700-996e-1e2895cee787",
					Sku:                         "MRK.02076",
					CurrentStockOnHandQuantity:  0,
					PreviousStockOnHandQuantity: 0},
			},
		},
	}

	p.mockStockAdjustmentDb.
		EXPECT().
		GetReasonCode(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *warehouseDb.GetReasonCodesResponse, model *warehouseDb.GetReasonCodesModel) {
			ch <- &warehouseDb.GetReasonCodesResponse{
				Error: nil,
			}
			return
		})

	// When
	serviceCh := make(chan *StockMovementServiceResponse)

	defer close(serviceCh)

	go p.storeStockService.StockMovement(serviceCh, model)

	response := <-serviceCh

	// Then
	p.EqualError(response.Error, "invalid event type")
}

func (p *WarehouseStockServiceTestSuite) TestService_SendToLogoError_ReturnsError() {
	//Given
	model := StockMovementServiceModel{
		MessageId: "31",
		Event: pb.MovementsEvent{
			Id:          "1be0de6a-3a35-4e9d-834f-3a3389ee7b25",
			Timestamp:   timestamppb.Now(),
			ReasonCode:  pb.ReasonCode_REASON_CODE_STOCK_ADDITION,
			EventType:   pb.EventType_EVENT_TYPE_MANUAL_ADJUSTMENT,
			TransferRef: &wrapperspb.StringValue{Value: "313131"},
			WarehouseId: "13004c8a-45e1-ea11-80d9-0050569c6abd",
			UserEmail:   &wrapperspb.StringValue{Value: "<EMAIL>"},
			Products: []*pb.MovementsEvent_Product{
				{MovementId: "63e1246b-cc06-4700-996e-1e2895cee787",
					Sku:                         "MRK.02076",
					CurrentStockOnHandQuantity:  0,
					PreviousStockOnHandQuantity: 0},
			},
		},
	}

	p.mockStockAdjustmentDb.
		EXPECT().
		GetReasonCode(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *warehouseDb.GetReasonCodesResponse, model *warehouseDb.GetReasonCodesModel) {
			ch <- &warehouseDb.GetReasonCodesResponse{
				Error: nil,
			}
			return
		})

	p.mockLogoProxy.
		EXPECT().
		SendMaterialSlips(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logo.LogoProxyBaseResponse, model *logo.SendMaterialSlipsModel) {
			ch <- &logo.LogoProxyBaseResponse{Error: errors.New("error")}
			return
		})

	// When
	serviceCh := make(chan *StockMovementServiceResponse)

	defer close(serviceCh)

	go p.storeStockService.StockMovement(serviceCh, model)

	response := <-serviceCh

	// Then
	p.EqualError(response.Error, "error")
}
