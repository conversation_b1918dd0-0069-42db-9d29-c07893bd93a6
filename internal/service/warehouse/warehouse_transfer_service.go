package warehouse

import (
	"fmt"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"strconv"

	"go.uber.org/zap"
)

type IWarehouseTransferService interface {
	SendStoreTransferInboundCollection(ch chan *SendWarehouseTransferCollectionResponse, model SendWarehouseTransferCollectionModel)
	SendStoreTransferOutboundCollection(ch chan *SendWarehouseTransferCollectionResponse, model SendWarehouseTransferCollectionModel)
}

type WarehouseTransferService struct {
	environment env.IEnvironment
	loggr       logger.ILogger
	validatr    validator.IValidator
	cachr       cacher.ICacher
	logoProxy   logo.ILogoProxy
}

// NewWarehouseTransferService
// Returns a new WarehouseTransferService.
func NewWarehouseTransferService(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.I<PERSON>acher, logoProxy logo.ILogoProxy) IWarehouseTransferService {
	service := WarehouseTransferService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if logoProxy != nil {
		service.logoProxy = logoProxy
	} else {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	return &service
}

// SendStoreTransferCollection
// Sends an inbound warehouse transfer with details provided by google pub sub message.
func (s *WarehouseTransferService) SendStoreTransferInboundCollection(ch chan *SendWarehouseTransferCollectionResponse, model SendWarehouseTransferCollectionModel) {

	if len(model.OrderDetail) == 0 {
		s.loggr.Error("OrderDetail of StoreTransfer is empty. TransferId: "+model.TransferId,
			zap.String("data", fmt.Sprintf("%+v", model)))

		ch <- &SendWarehouseTransferCollectionResponse{}
		return
	}

	storeTransferCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(storeTransferCh)

	proxyModel := prepareStoreTransferCollectionModel(&model)

	go s.logoProxy.SendStoreTransferCollection(storeTransferCh, &proxyModel)

	response := <-storeTransferCh

	if response.Error != nil {
		s.loggr.Error("An error occurred in SendStoreTransferInboundCollection service.",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
			zap.String("ResultCode", strconv.Itoa(response.ResultCode)),
			zap.Error(response.Error),
			zap.String("data", fmt.Sprintf("%+v", model)))

		ch <- &SendWarehouseTransferCollectionResponse{
			Error:      response.Error,
			ResultMsg:  response.Error.Error(),
			Result:     response.Error.Error(),
			ResultCode: 0,
		}
		return
	}

	ch <- &SendWarehouseTransferCollectionResponse{}
}

func (s *WarehouseTransferService) SendStoreTransferOutboundCollection(ch chan *SendWarehouseTransferCollectionResponse, model SendWarehouseTransferCollectionModel) {
	storeTransferCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(storeTransferCh)

	proxyModel := prepareStoreTransferCollectionModel(&model)

	go s.logoProxy.SendStoreTransferCollection(storeTransferCh, &proxyModel)

	response := <-storeTransferCh

	if response.Error != nil {
		s.loggr.Error("An error occurred in SendStoreTransferOutboundCollection service.",
			zap.String("ResultMessage", response.ResultMsg),
			zap.String("Result", response.Result),
			zap.String("ResultCode", strconv.Itoa(response.ResultCode)),
			zap.Error(response.Error),
			zap.String("data", fmt.Sprintf("%+v", model)))

		ch <- &SendWarehouseTransferCollectionResponse{
			Error:      response.Error,
			ResultMsg:  response.Error.Error(),
			Result:     response.Error.Error(),
			ResultCode: 0,
		}
		return
	}

	ch <- &SendWarehouseTransferCollectionResponse{}
}

func prepareStoreTransferCollectionModel(model *SendWarehouseTransferCollectionModel) logo.SendStoreTransferCollectionModel {
	proxyModel := logo.SendStoreTransferCollectionModel{
		MessageId:                model.MessageId,
		TransactionDate:          model.TransactionDate,
		TransferId:               model.TransferId,
		SourceStoreId:            model.SourceStoreId,
		SourceWarehouseType:      model.SourceWarehouseType,
		TargetStoreId:            model.TargetStoreId,
		TargetWarehouseType:      model.TargetWarehouseType,
		DispatchId:               model.DispatchId,
		TransferReference:        model.TransferReference,
		TransferDate:             model.TransferDate,
		DriverName:               model.DriverName,
		DriverIdentityCardNumber: model.DriverIdentityCardNumber,
		DriverPlate:              model.DriverPlate,
	}

	proxyModel.OrderDetail = make([]logo.StoreTransferOrderDetail, len(model.OrderDetail))
	for i := 0; i < len(model.OrderDetail); i++ {
		proxyModel.OrderDetail[i].Quantity = model.OrderDetail[i].Quantity
		proxyModel.OrderDetail[i].SKU = model.OrderDetail[i].SKU
	}

	return proxyModel
}
