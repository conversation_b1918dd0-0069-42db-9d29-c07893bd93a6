// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/warehouse/warehouse_service.go

// Package warehouse is a generated GoMock package.
package warehouse

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIWarehouseService is a mock of IWarehouseService interface.
type MockIWarehouseService struct {
	ctrl     *gomock.Controller
	recorder *MockIWarehouseServiceMockRecorder
}

// MockIWarehouseServiceMockRecorder is the mock recorder for MockIWarehouseService.
type MockIWarehouseServiceMockRecorder struct {
	mock *MockIWarehouseService
}

// NewMockIWarehouseService creates a new mock instance.
func NewMockIWarehouseService(ctrl *gomock.Controller) *MockIWarehouseService {
	mock := &MockIWarehouseService{ctrl: ctrl}
	mock.recorder = &MockIWarehouseServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWarehouseService) EXPECT() *MockIWarehouseServiceMockRecorder {
	return m.recorder
}

// AddOrUpdateWarehouse mocks base method.
func (m *MockIWarehouseService) AddOrUpdateWarehouse(ch chan *AddOrUpdateWarehouseServiceResponse, model *AddOrUpdateWarehouseServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddOrUpdateWarehouse", ch, model)
}

// AddOrUpdateWarehouse indicates an expected call of AddOrUpdateWarehouse.
func (mr *MockIWarehouseServiceMockRecorder) AddOrUpdateWarehouse(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOrUpdateWarehouse", reflect.TypeOf((*MockIWarehouseService)(nil).AddOrUpdateWarehouse), ch, model)
}

// SendUndeliveredWarehousesToLogo mocks base method.
func (m *MockIWarehouseService) SendUndeliveredWarehousesToLogo(ch chan *SendUndeliveredWarehousesToLogoServiceResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendUndeliveredWarehousesToLogo", ch)
}

// SendUndeliveredWarehousesToLogo indicates an expected call of SendUndeliveredWarehousesToLogo.
func (mr *MockIWarehouseServiceMockRecorder) SendUndeliveredWarehousesToLogo(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendUndeliveredWarehousesToLogo", reflect.TypeOf((*MockIWarehouseService)(nil).SendUndeliveredWarehousesToLogo), ch)
}
