// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/warehouse/warehouse_stock_service.go

// Package warehouse is a generated GoMock package.
package warehouse

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIWarehouseStockService is a mock of IWarehouseStockService interface.
type MockIWarehouseStockService struct {
	ctrl     *gomock.Controller
	recorder *MockIWarehouseStockServiceMockRecorder
}

// MockIWarehouseStockServiceMockRecorder is the mock recorder for MockIWarehouseStockService.
type MockIWarehouseStockServiceMockRecorder struct {
	mock *MockIWarehouseStockService
}

// NewMockIWarehouseStockService creates a new mock instance.
func NewMockIWarehouseStockService(ctrl *gomock.Controller) *MockIWarehouseStockService {
	mock := &MockIWarehouseStockService{ctrl: ctrl}
	mock.recorder = &MockIWarehouseStockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWarehouseStockService) EXPECT() *MockIWarehouseStockServiceMockRecorder {
	return m.recorder
}

// SendWarehouseStockCountsApprovalStatusAsMail mocks base method.
func (m *MockIWarehouseStockService) SendWarehouseStockCountsApprovalStatusAsMail(ch chan *SendWarehouseStockCountsApprovalStatusAsMailServiceResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendWarehouseStockCountsApprovalStatusAsMail", ch)
}

// SendWarehouseStockCountsApprovalStatusAsMail indicates an expected call of SendWarehouseStockCountsApprovalStatusAsMail.
func (mr *MockIWarehouseStockServiceMockRecorder) SendWarehouseStockCountsApprovalStatusAsMail(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWarehouseStockCountsApprovalStatusAsMail", reflect.TypeOf((*MockIWarehouseStockService)(nil).SendWarehouseStockCountsApprovalStatusAsMail), ch)
}

// SendWarehouseStockCountsToLogo mocks base method.
func (m *MockIWarehouseStockService) SendWarehouseStockCountsToLogo(ch chan *SendWarehouseStockCountsToLogoServiceResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendWarehouseStockCountsToLogo", ch)
}

// SendWarehouseStockCountsToLogo indicates an expected call of SendWarehouseStockCountsToLogo.
func (mr *MockIWarehouseStockServiceMockRecorder) SendWarehouseStockCountsToLogo(ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWarehouseStockCountsToLogo", reflect.TypeOf((*MockIWarehouseStockService)(nil).SendWarehouseStockCountsToLogo), ch)
}

// StockMovement mocks base method.
func (m *MockIWarehouseStockService) StockMovement(ch chan *StockMovementServiceResponse, model StockMovementServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StockMovement", ch, model)
}

// StockMovement indicates an expected call of StockMovement.
func (mr *MockIWarehouseStockServiceMockRecorder) StockMovement(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StockMovement", reflect.TypeOf((*MockIWarehouseStockService)(nil).StockMovement), ch, model)
}
