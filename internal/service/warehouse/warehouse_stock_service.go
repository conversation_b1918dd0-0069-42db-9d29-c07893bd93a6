package warehouse

import (
	"errors"
	"logo-adapter/internal/data/database/warehouse"
	"logo-adapter/internal/data/proxy/logistic"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/service/configuration"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/enum"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/helper"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
	"math"
	"os"
	"strconv"
	"time"

	pb "github.com/deliveryhero/dh-nv-proto-golang/packages/inventory_management/movements/event/v1"
	"github.com/matcornic/hermes/v2"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

var reasonOtherDecrease = Reason{
	GlobalReasonDescription: "Other (decrease)",
	LogoReasonCode:          "SAYEKS.002",
	LogoReasonDescription:   "Sayım Eksiği Merkez Depo Mutabakatsız Kayıt",
	LogoSlipTypeId:          51,
}
var reasonOtherIncrease = Reason{
	GlobalReasonDescription: "Other (increase)",
	LogoReasonCode:          "SAYFAZ.002",
	LogoReasonDescription:   "Sayım Fazlası merkez depo Mutabakatsız kayıt",
	LogoSlipTypeId:          50,
}
var reasonSurprisePackageDecrease = Reason{
	GlobalReasonDescription: "Surprise Package (decrease)",
	LogoReasonCode:          "SAYEKS.004",
	LogoReasonDescription:   "Sayım Eksiği cDC Depo Mutabaksız Kayıt",
	LogoSlipTypeId:          51,
}
var reasonSurprisePackageIncrease = Reason{
	GlobalReasonDescription: "Surprise Package (increase)",
	LogoReasonCode:          "SAYFAZ.004",
	LogoReasonDescription:   "Sayım Fazlası cDC Depo Mutabaksız Kayıt",
	LogoSlipTypeId:          50,
}

type IWarehouseStockService interface {
	StockMovement(ch chan *StockMovementServiceResponse, model StockMovementServiceModel)
	SendWarehouseStockCountsToLogo(ch chan *SendWarehouseStockCountsToLogoServiceResponse)
	SendWarehouseStockCountsApprovalStatusAsMail(ch chan *SendWarehouseStockCountsApprovalStatusAsMailServiceResponse)
}

type WarehouseStockService struct {
	environment                env.IEnvironment
	loggr                      logger.ILogger
	validatr                   validator.IValidator
	cachr                      cacher.ICacher
	logoProxy                  logo.ILogoProxy
	stockAdjustmentDb          warehouse.IWarehouseStockDb
	serverConfigurationService configuration.IServerConfigurationService
	logisticPosProxy           logistic.ILogisticProxy
}

func NewWarehouseStockService(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	logoProxy logo.ILogoProxy,
	stockAdjustmentDb warehouse.IWarehouseStockDb,
	serverConfigurationService configuration.IServerConfigurationService,
	logisticPosProxy logistic.ILogisticProxy,
) IWarehouseStockService {

	storeStockService := &WarehouseStockService{
		environment: environment,
		loggr:       loggr.With(zap.String("serviceName", "WarehouseStockService")),
		validatr:    validatr,
		cachr:       cachr,
	}

	if logoProxy != nil {
		storeStockService.logoProxy = logoProxy
	} else {
		storeStockService.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}
	if stockAdjustmentDb != nil {
		storeStockService.stockAdjustmentDb = stockAdjustmentDb
	} else {
		storeStockService.stockAdjustmentDb = warehouse.NewWarehouseStockDb(environment, loggr, validatr, cachr)
	}
	if serverConfigurationService != nil {
		storeStockService.serverConfigurationService = serverConfigurationService
	} else {
		storeStockService.serverConfigurationService = configuration.NewServerConfigurationService(environment, loggr, validatr, cachr, nil)
	}
	if logisticPosProxy != nil {
		storeStockService.logisticPosProxy = logisticPosProxy
	} else {
		storeStockService.logisticPosProxy = logistic.NewLogisticProxy(environment, loggr, validatr, cachr)
	}

	return storeStockService
}

func (s *WarehouseStockService) StockMovement(ch chan *StockMovementServiceResponse, model StockMovementServiceModel) {
	logoProxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(logoProxyCh)

	products := model.Event.GetProducts()

	userEmail := model.Event.GetUserEmail()
	reasonCode := model.Event.GetReasonCode()
	eventType := model.Event.GetEventType()
	storeId := model.Event.GetWarehouseId()

	if reasonCode == pb.ReasonCode_REASON_CODE_EVERYDAY_COFFEE_SALE || reasonCode == pb.ReasonCode_REASON_CODE_INTERNAL_VENDOR_ADDITION || reasonCode == pb.ReasonCode_REASON_CODE_DONATION {
		ch <- &StockMovementServiceResponse{
			Error: nil,
		}
		return
	}

	if reasonCode == pb.ReasonCode_REASON_CODE_UNITS_TO_PACKS {
		s.loggr.Error("Unexpected UNITS_TO_PACKS reason code !")
		ch <- &StockMovementServiceResponse{
			Error: nil,
		}
		return
	}

	if eventType == pb.EventType_EVENT_TYPE_MANUAL_ADJUSTMENT {
		productModels := []logo.MaterialSlipProductServiceModel{}

		relatedReason, reasonErr := s.getRelatedReason(int(reasonCode))
		if reasonErr != nil && reasonCode != pb.ReasonCode_REASON_CODE_OTHER && reasonCode != pb.ReasonCode_REASON_CODE_SURPRISE_PACKAGE {
			ch <- &StockMovementServiceResponse{
				Error: reasonErr,
			}
			return
		}

		for _, v := range products {
			quantitiyDiff := int(v.GetPreviousStockOnHandQuantity()) - int(v.GetCurrentStockOnHandQuantity())

			if reasonCode == pb.ReasonCode_REASON_CODE_OTHER {
				if quantitiyDiff > 0 {
					relatedReason = &reasonOtherDecrease
				} else {
					relatedReason = &reasonOtherIncrease
				}
			}
			if reasonCode == pb.ReasonCode_REASON_CODE_SURPRISE_PACKAGE {
				if quantitiyDiff > 0 {
					relatedReason = &reasonSurprisePackageDecrease
				} else {
					relatedReason = &reasonSurprisePackageIncrease
				}
			}

			productModels = append(productModels, logo.MaterialSlipProductServiceModel{
				RequestId:      v.GetMovementId(),
				Quantity:       int(math.Abs(float64(quantitiyDiff))),
				Unit:           "ADET",
				ReasonCode:     relatedReason.LogoReasonCode,
				CreatedBy:      userEmail.GetValue(),
				SKU:            v.GetSku(),
				LogoSlipTypeId: relatedReason.LogoSlipTypeId,
			})
		}

		for idx, p := range productModels {
			go s.logoProxy.SendMaterialSlips(logoProxyCh, &logo.SendMaterialSlipsModel{
				MessageId:       model.MessageId,
				TransactionDate: time.Now(),
				MaterialSlips: []logo.MaterialSlipModel{
					{
						StoreId:  storeId,
						SlipType: p.LogoSlipTypeId,
						SlipId:   model.Event.GetId() + "-" + strconv.Itoa(idx+1),
						ProductList: []logo.MaterialSlipProductModel{
							{
								RequestId:  p.RequestId,
								Quantity:   float64(p.Quantity),
								Unit:       p.Unit,
								ReasonCode: p.ReasonCode,
								CreatedBy:  p.CreatedBy,
								SKU:        p.SKU,
							},
						},
					},
				},
			})

			logoProxyResponse := <-logoProxyCh
			if logoProxyResponse.Error != nil {
				ch <- &StockMovementServiceResponse{
					Error: logoProxyResponse.Error,
				}
				return
			}
		}

		ch <- &StockMovementServiceResponse{
			Error: nil,
		}
		return

	} else if eventType == pb.EventType_EVENT_TYPE_CYCLE_COUNT_ADJUSTMENT {

		//Check warehouse cycle count config from pos api (pos management)
		logisticPosProxyCh := make(chan *logistic.GetWarehouseCountConfigProxyResponse)
		defer close(logisticPosProxyCh)

		go s.logisticPosProxy.GetWarehouseCountConfig(logisticPosProxyCh, &logistic.GetWarehouseCountConfigModel{
			WarehouseId: storeId,
		})
		getWarehouseCountConfigResponse := <-logisticPosProxyCh
		if getWarehouseCountConfigResponse.Error != nil {
			ch <- &StockMovementServiceResponse{Error: getWarehouseCountConfigResponse.Error}
			return
		}

		if !getWarehouseCountConfigResponse.Data.IsCycleCountOpen {
			s.loggr.Info(storeId + " warehouse closed to cycle count in Logistic POS")
			ch <- &StockMovementServiceResponse{Error: nil}
			return
		}

		//Check coffee products config
		coffeeProducts := s.serverConfigurationService.GetCoffeeProductSKUs(enum.CoffeeProductSKUs)
		if len(coffeeProducts) < 1 {
			s.loggr.Error(enum.CoffeeProductSKUs + " config is empty or got error!")
			ch <- &StockMovementServiceResponse{Error: errors.New(enum.CoffeeProductSKUs + " config is empty or got error")}
			return
		}
		coffeeMaterials := s.serverConfigurationService.GetCoffeeMaterialSKUs(enum.CoffeeMaterialSKUs)
		if len(coffeeMaterials) < 1 {
			s.loggr.Error(enum.CoffeeMaterialSKUs + " config is empty or got error!")
			ch <- &StockMovementServiceResponse{Error: errors.New(enum.CoffeeMaterialSKUs + " config is empty or got error")}
			return
		}

		inventoryStocks := []warehouse.AddStockCount{}
		deltaDecreaseModels := []logo.MaterialSlipProductModel{}
		deltaIncreaseModels := []logo.MaterialSlipProductModel{}
		for _, v := range products {
			existCoffeeProduct := helper.Contains(coffeeProducts, v.GetSku())
			if existCoffeeProduct {
				continue
			}

			storeCount := float64(v.GetCurrentStockOnHandQuantity())
			centerStoreQuantity := float64(v.GetPreviousStockOnHandQuantity())
			existCoffeeMaterial := helper.Contains(coffeeMaterials, v.GetSku())
			if existCoffeeMaterial {
				switch v.GetSku() {
				case "MRK.03921":
					{
						storeCount = storeCount * 0.08
						centerStoreQuantity = centerStoreQuantity * 0.08
					}
				case "MRK.03882", "MRK.03884", "MRK.03885", "MRK.04268", "MRK.06044", "MRK.06045", "MRK.06046", "MRK.06047", "MRK.06048", "MRK.06049", "MRK.06187", "MRK.06188", "MRK.06189", "MRK.06190":
					{
						storeCount = storeCount * 0.7
						centerStoreQuantity = centerStoreQuantity * 0.7
					}
				case "VU3T1K", "6NIQ19":
					{
						storeCount = storeCount * 2.5
						centerStoreQuantity = centerStoreQuantity * 2.5
					}
				}
			}

			if getWarehouseCountConfigResponse.Data.IsDeltaOpen && !existCoffeeMaterial {

				quantitiyDiff := centerStoreQuantity - storeCount
				if quantitiyDiff > 0 {
					deltaDecreaseModels = append(deltaDecreaseModels, logo.MaterialSlipProductModel{
						RequestId:  v.GetMovementId(),
						Quantity:   math.Abs(quantitiyDiff),
						Unit:       "ADET",
						ReasonCode: "SAYEKS.DLT",
						CreatedBy:  userEmail.GetValue(),
						SKU:        v.GetSku(),
					})
				} else {
					deltaIncreaseModels = append(deltaIncreaseModels, logo.MaterialSlipProductModel{
						RequestId:  v.GetMovementId(),
						Quantity:   math.Abs(quantitiyDiff),
						Unit:       "ADET",
						ReasonCode: "SAYFAZ.DLT",
						CreatedBy:  userEmail.GetValue(),
						SKU:        v.GetSku(),
					})
				}

			} else {
				inventoryStocks = append(inventoryStocks, warehouse.AddStockCount{
					StoreCount:          storeCount,
					StoreId:             storeId,
					CenterStoreQuantity: float64(v.GetPreviousStockOnHandQuantity()),
					MessageId:           model.MessageId,
					Sku:                 v.GetSku(),
				})
			}
		}

		if len(inventoryStocks) > 0 {
			chAddStockCount := make(chan *warehouse.AddStockCountResponse)
			defer close(chAddStockCount)

			go s.stockAdjustmentDb.AddStockCount(chAddStockCount, &warehouse.AddStockCountListModel{
				StockCountTypeId: enum.WarehouseCountTypePartial,
				StockCountList:   inventoryStocks,
			})

			addStockCountResponse := <-chAddStockCount
			if addStockCountResponse.Error != nil {
				ch <- &StockMovementServiceResponse{Error: addStockCountResponse.Error}
				return
			}
		}

		idx := 1
		if len(deltaDecreaseModels) > 0 {
			go s.logoProxy.SendMaterialSlips(logoProxyCh, &logo.SendMaterialSlipsModel{
				MessageId:       model.MessageId,
				TransactionDate: time.Now(),
				MaterialSlips: []logo.MaterialSlipModel{
					{
						StoreId:     storeId,
						SlipType:    51,
						SlipId:      model.Event.GetId() + "-" + strconv.Itoa(idx),
						ProductList: deltaDecreaseModels,
					},
				},
			})

			logoProxyResponse := <-logoProxyCh
			if logoProxyResponse.Error != nil {
				ch <- &StockMovementServiceResponse{
					Error: logoProxyResponse.Error,
				}
				return
			}

			idx = 2
		}
		if len(deltaIncreaseModels) > 0 {
			go s.logoProxy.SendMaterialSlips(logoProxyCh, &logo.SendMaterialSlipsModel{
				MessageId:       model.MessageId,
				TransactionDate: time.Now(),
				MaterialSlips: []logo.MaterialSlipModel{
					{
						StoreId:     storeId,
						SlipType:    50,
						SlipId:      model.Event.GetId() + "-" + strconv.Itoa(idx),
						ProductList: deltaIncreaseModels,
					},
				},
			})

			logoProxyResponse := <-logoProxyCh
			if logoProxyResponse.Error != nil {
				ch <- &StockMovementServiceResponse{
					Error: logoProxyResponse.Error,
				}
				return
			}
		}

		ch <- &StockMovementServiceResponse{
			Error: nil,
		}
		return

	} else if eventType == pb.EventType_EVENT_TYPE_STORE_TRANSFER_SHRINKAGE {
		storeTransferShrinkages := []logo.StoreTransferShrinkage{}

		for _, v := range products {
			quantitiyDiff := int(v.GetPreviousStockOnHandQuantity()) - int(v.GetCurrentStockOnHandQuantity())

			storeTransferShrinkages = append(storeTransferShrinkages, logo.StoreTransferShrinkage{
				TransferRef:        model.Event.GetTransferRef().GetValue(),
				StoreId:            storeId,
				SKU:                v.GetSku(),
				DifferenceQuantity: int(math.Abs(float64(quantitiyDiff))),
			})
		}

		go s.logoProxy.SendStoreTransferShrinkage(logoProxyCh, &logo.SendStoreTransferShrinkageModel{
			MessageId:                  model.MessageId,
			StoreTransferShrinkageList: storeTransferShrinkages,
		})

	} else if eventType == pb.EventType_EVENT_TYPE_FULL_SYNC {
		// delaying simultaneous receiving messages for the same warehouse
		cacheValue := s.cachr.Get(storeId)
		if cacheValue == nil {
			s.cachr.Set(storeId, "", 90*time.Second)
		} else {
			s.loggr.Warn("Waiting to be removed from the cache in the CC flow. WarehouseId: " + storeId)
			ch <- &StockMovementServiceResponse{Error: errors.New("waiting to be removed from the cache in the cc flow")}
			return
		}

		coffeeProducts := s.serverConfigurationService.GetCoffeeProductSKUs(enum.CoffeeProductSKUs)
		if len(coffeeProducts) < 1 {
			s.loggr.Warn(enum.CoffeeProductSKUs + " config is empty or got error!")
			ch <- &StockMovementServiceResponse{Error: errors.New(enum.CoffeeProductSKUs + " config is empty or got error")}
			return
		}
		coffeeMaterials := s.serverConfigurationService.GetCoffeeMaterialSKUs(enum.CoffeeMaterialSKUs)
		if len(coffeeMaterials) < 1 {
			s.loggr.Warn(enum.CoffeeMaterialSKUs + " config is empty or got error!")
			ch <- &StockMovementServiceResponse{Error: errors.New(enum.CoffeeMaterialSKUs + " config is empty or got error")}
			return
		}

		inventoryStocks := []logo.InventoryStock{}
		for _, v := range products {
			existCoffeeProduct := helper.Contains(coffeeProducts, v.GetSku())
			if existCoffeeProduct {
				continue
			}

			storeCount := float64(v.GetCurrentStockOnHandQuantity())
			existCoffeeMaterial := helper.Contains(coffeeMaterials, v.GetSku())
			if existCoffeeMaterial {
				switch v.GetSku() {
				case "MRK.03921":
					{
						storeCount = storeCount * 0.08
					}
				case "MRK.03882", "MRK.03884", "MRK.03885", "MRK.04268", "MRK.06044", "MRK.06045", "MRK.06046", "MRK.06047", "MRK.06048", "MRK.06049", "MRK.06187", "MRK.06188", "MRK.06189", "MRK.06190":
					{
						storeCount = storeCount * 0.7
					}
				case "VU3T1K", "6NIQ19":
					{
						storeCount = storeCount * 2.5
					}
				}
			}

			inventoryStocks = append(inventoryStocks, logo.InventoryStock{
				StoreCount:          storeCount,
				StoreId:             storeId,
				CenterStoreQuantity: float64(v.GetPreviousStockOnHandQuantity()),
				MessageId:           model.MessageId,
				SKU:                 v.GetSku(),
			})
		}
		s.loggr.Info(
			"Sending full stock count to Logo. WarehouseId: "+storeId,
			zap.String("MessageId", model.MessageId),
			zap.Int("TotalCount", len(inventoryStocks)),
		)

		go s.logoProxy.SendInventoryStocks(logoProxyCh, &logo.SendInventoryStocksModel{
			StoreStockCountList: inventoryStocks,
		}, storeId)

		logoProxyResponse := <-logoProxyCh
		if logoProxyResponse.Error != nil {
			ch <- &StockMovementServiceResponse{
				Error: logoProxyResponse.Error,
			}
			return
		}

		chAddStockCount := make(chan *warehouse.AddStockCountResponse)
		defer close(chAddStockCount)

		go s.stockAdjustmentDb.AddStockCount(chAddStockCount, &warehouse.AddStockCountListModel{
			StockCountTypeId: enum.WarehouseCountTypeFull,
			StockCountList: []warehouse.AddStockCount{
				{
					StoreId:             storeId,
					MessageId:           model.MessageId,
					Sku:                 "Total: " + strconv.Itoa(len(inventoryStocks)),
					StoreCount:          0,
					CenterStoreQuantity: 0,
				},
			},
		})

		addStockCountResponse := <-chAddStockCount
		if addStockCountResponse.Error != nil {
			ch <- &StockMovementServiceResponse{Error: addStockCountResponse.Error}
			return
		}

		chMarkWarehouseStockCounts := make(chan *warehouse.MarkWareHouseStockCountsAsSentResponse)
		defer close(chMarkWarehouseStockCounts)

		go s.stockAdjustmentDb.MarkWarehouseStockCountsAsSent(chMarkWarehouseStockCounts, &warehouse.MarkWareHouseStockCountsAsSentModel{
			StoreId:    storeId,
			MessageIds: []string{model.MessageId},
		})

		markWarehouseStockCountsResponse := <-chMarkWarehouseStockCounts
		if markWarehouseStockCountsResponse.Error != nil {
			ch <- &StockMovementServiceResponse{Error: markWarehouseStockCountsResponse.Error}
			return
		}

		ch <- &StockMovementServiceResponse{
			Error: nil,
		}
		return

	} else {
		ch <- &StockMovementServiceResponse{
			Error: errors.New("invalid event type"),
		}
		return
	}

	logoProxyResponse := <-logoProxyCh
	if logoProxyResponse.Error != nil {
		ch <- &StockMovementServiceResponse{
			Error: logoProxyResponse.Error,
		}
		return
	}

	ch <- &StockMovementServiceResponse{
		Error: nil,
	}
}

func (s *WarehouseStockService) getRelatedReason(globalReasonCode int) (*Reason, error) {
	chGetReason := make(chan *warehouse.GetReasonCodesResponse)
	defer close(chGetReason)

	go s.stockAdjustmentDb.GetReasonCode(chGetReason, &warehouse.GetReasonCodesModel{
		GlobalReasonCode: globalReasonCode,
	})

	getReasonCodeResponse := <-chGetReason
	if getReasonCodeResponse.Error != nil {
		return &Reason{}, getReasonCodeResponse.Error
	}

	return &Reason{
		GlobalReasonDescription: getReasonCodeResponse.GlobalReasonDescription,
		GlobalReasonCode:        getReasonCodeResponse.GlobalReasonCode,
		LogoSlipTypeId:          getReasonCodeResponse.LogoSlipTypeId,
		LogoReasonDescription:   getReasonCodeResponse.LogoReasonDescription,
		LogoReasonCode:          getReasonCodeResponse.LogoReasonCode,
	}, nil
}

func (s *WarehouseStockService) SendWarehouseStockCountsToLogo(ch chan *SendWarehouseStockCountsToLogoServiceResponse) {
	chGetWarehouseStockCounts := make(chan *warehouse.GetWarehouseStockCountsResponse)
	defer close(chGetWarehouseStockCounts)

	go s.stockAdjustmentDb.GetWarehouseStockCountsByNotSentToLogo(chGetWarehouseStockCounts)

	getStockCountResponse := <-chGetWarehouseStockCounts

	if getStockCountResponse.Error != nil {
		ch <- &SendWarehouseStockCountsToLogoServiceResponse{Error: getStockCountResponse.Error}
		return
	}

	if len(getStockCountResponse.GetAllStockCounts) < 1 {
		ch <- &SendWarehouseStockCountsToLogoServiceResponse{Error: nil, SentCount: 0}
		return
	}

	//group by storeId
	groupByWarehouses := make(map[string][]warehouse.GetStockCountModel)

	for _, stockCount := range getStockCountResponse.GetAllStockCounts {
		if v, exists := groupByWarehouses[stockCount.StoreId]; exists {
			v = append(v, stockCount)
		}
		groupByWarehouses[stockCount.StoreId] = append(groupByWarehouses[stockCount.StoreId], stockCount)
	}

	logoProxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(logoProxyCh)

	var sentCount int
	for storeId, warehouseStockCounts := range groupByWarehouses {
		inventoryStocks := []logo.InventoryStock{}
		messageIds := []string{}

		for _, count := range warehouseStockCounts {
			inventoryStocks = append(inventoryStocks, logo.InventoryStock{
				StoreCount:          count.StoreCount,
				StoreId:             count.StoreId,
				CenterStoreQuantity: count.CenterStoreQuantity,
				MessageId:           count.MessageId,
				SKU:                 count.Sku,
			})

			exist := helper.Contains(messageIds, count.MessageId)
			if !exist {
				messageIds = append(messageIds, count.MessageId)
			}
		}
		s.loggr.Info(
			"SendWarehouseStockCountsToLogoJob - Sending stock count to Logo. WarehouseId: "+storeId,
			zap.Strings("MessageIds", messageIds),
			zap.Int("TotalCount", len(inventoryStocks)),
		)

		go s.logoProxy.SendInventoryStocks(logoProxyCh, &logo.SendInventoryStocksModel{
			StoreStockCountList: inventoryStocks,
		}, storeId)

		logoProxyResponse := <-logoProxyCh
		if logoProxyResponse.Error != nil {
			s.loggr.Error("Failed to send store stock counts to Logo!", zap.Error(logoProxyResponse.Error))
			ch <- &SendWarehouseStockCountsToLogoServiceResponse{
				Error: logoProxyResponse.Error,
			}
			return
		}

		chMarkWarehouseStockCounts := make(chan *warehouse.MarkWareHouseStockCountsAsSentResponse)
		defer close(chMarkWarehouseStockCounts)
		go s.stockAdjustmentDb.MarkWarehouseStockCountsAsSent(chMarkWarehouseStockCounts, &warehouse.MarkWareHouseStockCountsAsSentModel{
			StoreId:    storeId,
			MessageIds: messageIds,
		})

		markWarehouseStockCountsResponse := <-chMarkWarehouseStockCounts

		if markWarehouseStockCountsResponse.Error != nil {
			ch <- &SendWarehouseStockCountsToLogoServiceResponse{Error: markWarehouseStockCountsResponse.Error}
			return
		}

		sentCount = len(inventoryStocks)
		s.loggr.Info("SendWarehouseStockCountsToLogoJob successful for StoreId: " + storeId + ", SentCount: " + strconv.Itoa(sentCount))
	}

	ch <- &SendWarehouseStockCountsToLogoServiceResponse{Error: nil, SentCount: sentCount}
}

func (s *WarehouseStockService) SendWarehouseStockCountsApprovalStatusAsMail(ch chan *SendWarehouseStockCountsApprovalStatusAsMailServiceResponse) {
	chGetWarehouseStockCountsByNotSentMail := make(chan *warehouse.GetWarehouseStockCountsByNotSentMailResponse)
	defer close(chGetWarehouseStockCountsByNotSentMail)

	go s.stockAdjustmentDb.GetWarehouseStockCountsByNotSentMail(chGetWarehouseStockCountsByNotSentMail)

	getStockCountsByNotSentMailResponse := <-chGetWarehouseStockCountsByNotSentMail

	if getStockCountsByNotSentMailResponse.Error != nil {
		ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{Error: getStockCountsByNotSentMailResponse.Error}
		return
	}

	if len(getStockCountsByNotSentMailResponse.StoreIds) < 1 {
		s.loggr.Info("SendWarehouseStockCountsApprovalStatusJob nothing to sent.")
		ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{Error: nil}
		return
	}

	//group by storeId
	groupByWarehouses := make(map[string][]int)
	storeIdsWithSentDate := make(map[string]time.Time)

	for _, storeIdWithId := range getStockCountsByNotSentMailResponse.StoreIds {
		if v, exists := groupByWarehouses[storeIdWithId.StoreId]; exists {
			v = append(v, storeIdWithId.Id)
		}
		groupByWarehouses[storeIdWithId.StoreId] = append(groupByWarehouses[storeIdWithId.StoreId], storeIdWithId.Id)

		if _, existsStore := storeIdsWithSentDate[storeIdWithId.StoreId]; !existsStore {
			storeIdsWithSentDate[storeIdWithId.StoreId] = storeIdWithId.SentDateToLogo
		}
	}

	logoProcessTime := s.serverConfigurationService.GetLogoStockCountProcessTime(enum.LogoStockCountProcessTime)
	if logoProcessTime < 1 {
		s.loggr.Warn("SendWarehouseStockCountsApprovalStatusJob - " + enum.LogoStockCountProcessTime + " config is 0 or got error!")
		ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{Error: nil}
		return
	}

	logoProxyCh := make(chan *logo.GetStoreStockCountApprovalStatusResponse)
	defer close(logoProxyCh)

	for storeId, ids := range groupByWarehouses {

		var sentDateToLogo time.Time
		if v, exists := storeIdsWithSentDate[storeId]; exists {
			sentDateToLogo = helper.ConvertTimeByZone(v, "Europe/Istanbul")
		}

		//logoProcessTime minutes should elapse after being sent to the Logo
		diff := time.Since(sentDateToLogo)
		if diff.Minutes() < float64(logoProcessTime) {
			s.loggr.Warn("SendWarehouseStockCountsApprovalStatusJob - It has not been " + strconv.Itoa(logoProcessTime) + " minutes since the sent to Logo yet! StoreId: " + storeId)
			continue
		}

		go s.logoProxy.GetStoreStockCountApprovalStatus(logoProxyCh, &logo.GetStoreStockCountApprovalStatusModel{
			StoreId:        storeId,
			SentDateToLogo: sentDateToLogo,
		})

		logoProxyResponse := <-logoProxyCh
		if logoProxyResponse.Error != nil {
			s.loggr.Error("Failed to get store stock count approval status!",
				zap.Error(logoProxyResponse.Error),
				zap.String("StoreId", storeId),
			)
			ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{
				Error: logoProxyResponse.Error,
			}
			return
		}

		if len(logoProxyResponse.Result) < 1 {
			s.loggr.Warn("SendWarehouseStockCountsApprovalStatusJob - Cycle count data not found in Logo! StoreId: " + storeId)
			continue
		}

		//get warehouse name by id
		chGetWarehouseNameByIdResponse := make(chan *warehouse.GetWarehouseNameByIdResponse)
		defer close(chGetWarehouseNameByIdResponse)
		go s.stockAdjustmentDb.GetWarehouseNameById(chGetWarehouseNameByIdResponse, &warehouse.GetWarehouseByIdModel{
			WarehouseId: storeId,
		})

		getWarehouseNameByIdResponse := <-chGetWarehouseNameByIdResponse

		if getWarehouseNameByIdResponse.Error != nil {
			ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{Error: getWarehouseNameByIdResponse.Error}
			return
		}

		//set email and create excel file
		f := excelize.NewFile()
		f.SetCellValue("Sheet1", "A1", "Store Name")
		f.SetCellValue("Sheet1", "B1", "SKU")
		f.SetCellValue("Sheet1", "C1", "Store Count")
		f.SetCellValue("Sheet1", "D1", "Logo Stock Before")
		f.SetCellValue("Sheet1", "E1", "Logo Stock After")
		f.SetCellValue("Sheet1", "F1", "Is Equal")
		f.SetCellValue("Sheet1", "G1", "Status")

		i := 2
		areAllProcessed := true
		var unprocessedSkus []string
		for _, count := range logoProxyResponse.Result {
			if count.Status != 1 {
				areAllProcessed = false
				unprocessedSkus = append(unprocessedSkus, count.SKU)
			}

			isEqual := "False"
			if count.StoreCount == count.LogoStockAfterProcess {
				isEqual = "True"
			}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), getWarehouseNameByIdResponse.WarehouseName)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), count.SKU)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), count.StoreCount)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), count.LogoStockBeforeProcess)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), count.LogoStockAfterProcess)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), isEqual)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), count.Status)

			i++
		}

		fileName := "Count-Report.xlsx"
		if err := f.SaveAs(fileName); err != nil {
			s.loggr.Error("Failed to create excel for cycle count report!", zap.Error(err))
			ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{
				Error: err,
			}
			return
		}

		var note string
		if !areAllProcessed {
			s.loggr.Warn("SendWarehouseStockCountsApprovalStatusJob - The count includes products that have not yet been processed in Logo! StoreId: "+storeId,
				zap.Strings("Unprocessed SKUs", unprocessedSkus),
			)
			note = " Not: Bu sayımda henüz Logo stoklarına işlenmemiş ürünler bulunmaktadır!"
		}

		email := hermes.Email{
			Body: hermes.Body{
				Name:      "Logo Sayım Raporu",
				Greeting:  getWarehouseNameByIdResponse.WarehouseName,
				Intros:    []string{"Sayım raporu ektedir." + note},
				Signature: "İyi çalışmalar",
			},
		}

		to := os.Getenv("EMAIL_CYCLE_COUNT_APPROVAL_STATUS_TO")
		subject := "Logo Sayım Raporu / " + time.Now().Format("2006-01-02 15:04")

		//send email
		mailErr := helper.SendMail(email, to, subject, fileName)
		if mailErr != nil {
			s.loggr.Error("Failed to sending email!",
				zap.Error(mailErr),
				zap.String("StoreId", storeId),
			)
			ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{
				Error: mailErr,
			}
			return
		}

		if areAllProcessed {
			chMarkWarehouseStockCountsAsSentMailResponse := make(chan *warehouse.MarkWarehouseStockCountsAsSentMailResponse)
			defer close(chMarkWarehouseStockCountsAsSentMailResponse)

			go s.stockAdjustmentDb.MarkWarehouseStockCountsAsSentMail(chMarkWarehouseStockCountsAsSentMailResponse, &warehouse.MarkWarehouseStockCountsAsSentMailModel{
				Ids: ids,
			})

			markWarehouseStockCountsAsSentMailResponse := <-chMarkWarehouseStockCountsAsSentMailResponse
			if markWarehouseStockCountsAsSentMailResponse.Error != nil {
				ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{Error: markWarehouseStockCountsAsSentMailResponse.Error}
				return
			}
		}

		s.loggr.Info("SendWarehouseStockCountsApprovalStatusJob as email successful for StoreId: " + storeId)
	}

	ch <- &SendWarehouseStockCountsApprovalStatusAsMailServiceResponse{}
}
