package warehouse

import (
	"logo-adapter/internal/data/database/warehouse"
	"logo-adapter/internal/data/proxy/datafridge"
	"logo-adapter/internal/data/proxy/logistic"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"go.uber.org/zap"
)

type IWarehouseService interface {
	AddOrUpdateWarehouse(ch chan *AddOrUpdateWarehouseServiceResponse, model *AddOrUpdateWarehouseServiceModel)
	SendUndeliveredWarehousesToLogo(ch chan *SendUndeliveredWarehousesToLogoServiceResponse)
}

type WarehouseService struct {
	environment           env.IEnvironment
	loggr                 logger.ILogger
	validatr              validator.IValidator
	cachr                 cacher.ICacher
	warehouseDb           warehouse.IWarehouseDb
	dataFridgeVendorProxy datafridge.IVendorProxy
	logoProxy             logo.ILogoProxy
	logisticPosProxy      logistic.ILogisticProxy
}

// NewWarehouseService
// Returns a new WarehouseService.
func NewWarehouseService(
	environment env.IEnvironment,
	loggr logger.ILogger,
	validatr validator.IValidator,
	cachr cacher.ICacher,
	warehouseDb warehouse.IWarehouseDb,
	dataFridgeVendorProxy datafridge.IVendorProxy,
	logoProxy logo.ILogoProxy,
	logisticPosProxy logistic.ILogisticProxy,
) IWarehouseService {
	service := WarehouseService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if warehouseDb != nil {
		service.warehouseDb = warehouseDb
	} else {
		service.warehouseDb = warehouse.NewWarehouseDb(environment, loggr, validatr, cachr)
	}

	if dataFridgeVendorProxy != nil {
		service.dataFridgeVendorProxy = dataFridgeVendorProxy
	} else {
		service.dataFridgeVendorProxy = datafridge.NewVendorProxy(environment, loggr, validatr, cachr)
	}

	if logoProxy != nil {
		service.logoProxy = logoProxy
	} else {
		service.logoProxy = logo.NewLogoProxy(environment, loggr, validatr, cachr)
	}

	if logisticPosProxy != nil {
		service.logisticPosProxy = logisticPosProxy
	} else {
		service.logisticPosProxy = logistic.NewLogisticProxy(environment, loggr, validatr, cachr)
	}

	return &service
}

// AddOrUpdateWarehouse
// Adds or updates warehouse.
func (s *WarehouseService) AddOrUpdateWarehouse(ch chan *AddOrUpdateWarehouseServiceResponse, model *AddOrUpdateWarehouseServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &AddOrUpdateWarehouseServiceResponse{Error: modelErr}
		return
	}

	dfProxyCh := make(chan *datafridge.GetVendorProxyResponse)
	defer close(dfProxyCh)
	go s.dataFridgeVendorProxy.GetVendor(
		dfProxyCh, &datafridge.GetVendorProxyModel{
			GlobalEntityId: model.GlobalEntityId,
			VendorId:       model.PlatformVendorId,
		},
	)

	dfProxyResponse := <-dfProxyCh
	if dfProxyResponse.Error != nil {
		ch <- &AddOrUpdateWarehouseServiceResponse{Error: dfProxyResponse.Error}
		return
	}

	whDbCh := make(chan *warehouse.AddOrUpdateWarehouseResponse)
	defer close(whDbCh)
	go s.warehouseDb.AddOrUpdateWarehouse(
		whDbCh, &warehouse.AddOrUpdateWarehouseModel{
			SalesforceGridId: dfProxyResponse.Data.Vendor.GlobalVendorId,
			WarehouseId:      model.WarehouseId,
			WarehouseName:    model.WarehouseName,
			Address:          model.Address,
			City:             model.City,
			PayloadTimestamp: model.PayloadTimestamp,
			VendorCode:       model.PlatformVendorId,
		},
	)

	whDbResponse := <-whDbCh
	if whDbResponse.Error != nil {
		ch <- &AddOrUpdateWarehouseServiceResponse{Error: whDbResponse.Error}
		return
	}

	sendWarehouseCh := make(chan *logistic.LogisticPosProxyBaseResponse)
	defer close(sendWarehouseCh)
	go s.logisticPosProxy.SendWarehouse(sendWarehouseCh, &logistic.SendWarehouseModel{
		WarehouseId:   model.WarehouseId,
		WarehouseName: model.WarehouseName,
		IsActive:      model.IsActive,
	})

	sendWarehouseResponse := <-sendWarehouseCh
	if sendWarehouseResponse.Error != nil {
		s.loggr.Error(
			"Failed to sending warehouse to LogisticPos. WarehouseId: "+model.WarehouseId,
			zap.Any("Model", model),
			zap.Error(sendWarehouseResponse.Error),
		)
		ch <- &AddOrUpdateWarehouseServiceResponse{
			Error: sendWarehouseResponse.Error,
		}
		return
	}

	ch <- &AddOrUpdateWarehouseServiceResponse{Error: nil}
	return
}

// SendUndeliveredWarehousesToLogo
// Sends undelivered warehouses to Logo.
func (s *WarehouseService) SendUndeliveredWarehousesToLogo(ch chan *SendUndeliveredWarehousesToLogoServiceResponse) {
	whDbCh := make(chan *warehouse.GetUndeliveredWarehousesResponse)
	defer close(whDbCh)
	go s.warehouseDb.GetUndeliveredWarehouses(whDbCh)

	whDbResponse := <-whDbCh
	if whDbResponse.Error != nil {
		ch <- &SendUndeliveredWarehousesToLogoServiceResponse{Error: whDbResponse.Error}
		return
	}

	if len(whDbResponse.UndeliveredWarehouses) < 1 {
		ch <- &SendUndeliveredWarehousesToLogoServiceResponse{Error: nil, DeliveredCount: 0}
		return
	}

	storeList := make([]logo.Store, len(whDbResponse.UndeliveredWarehouses))
	for i, x := range whDbResponse.UndeliveredWarehouses {
		storeList[i] = logo.Store{
			StoreId:          x.WarehouseId,
			StoreName:        x.WarehouseName,
			AddressText:      x.Address,
			City:             x.City,
			Email:            "",
			StoreType:        x.WarehouseType,
			SalesforceGridId: x.SalesforceGridId,
		}
	}

	logoProxyCh := make(chan *logo.LogoProxyBaseResponse)
	defer close(logoProxyCh)
	go s.logoProxy.SendStores(logoProxyCh, &logo.SendStoresModel{
		StoreList: storeList,
	})

	logoProxyResponse := <-logoProxyCh

	if logoProxyResponse.Error != nil {
		ch <- &SendUndeliveredWarehousesToLogoServiceResponse{Error: logoProxyResponse.Error}
		return
	}

	ids := make([]int, len(whDbResponse.UndeliveredWarehouses))
	for i, x := range whDbResponse.UndeliveredWarehouses {
		ids[i] = x.Id
	}

	whDbMarkCh := make(chan *warehouse.MarkWarehousesAsDeliveredResponse)
	defer close(whDbMarkCh)
	go s.warehouseDb.MarkWarehousesDelivered(whDbMarkCh, &warehouse.MarkWarehousesAsDeliveredModel{
		Ids: ids,
	})

	whDbMarkResponse := <-whDbMarkCh

	if whDbMarkResponse.Error != nil {
		ch <- &SendUndeliveredWarehousesToLogoServiceResponse{Error: whDbMarkResponse.Error}
		return
	}

	ch <- &SendUndeliveredWarehousesToLogoServiceResponse{Error: nil, DeliveredCount: len(ids)}
	return
}
