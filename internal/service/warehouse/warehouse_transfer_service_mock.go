// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/warehouse/warehouse_transfer_service.go

// Package warehouse is a generated GoMock package.
package warehouse

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIWarehouseTransferService is a mock of IWarehouseTransferService interface.
type MockIWarehouseTransferService struct {
	ctrl     *gomock.Controller
	recorder *MockIWarehouseTransferServiceMockRecorder
}

// MockIWarehouseTransferServiceMockRecorder is the mock recorder for MockIWarehouseTransferService.
type MockIWarehouseTransferServiceMockRecorder struct {
	mock *MockIWarehouseTransferService
}

// NewMockIWarehouseTransferService creates a new mock instance.
func NewMockIWarehouseTransferService(ctrl *gomock.Controller) *MockIWarehouseTransferService {
	mock := &MockIWarehouseTransferService{ctrl: ctrl}
	mock.recorder = &MockIWarehouseTransferServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWarehouseTransferService) EXPECT() *MockIWarehouseTransferServiceMockRecorder {
	return m.recorder
}

// SendStoreTransferInboundCollection mocks base method.
func (m *MockIWarehouseTransferService) SendStoreTransferInboundCollection(ch chan *SendWarehouseTransferCollectionResponse, model SendWarehouseTransferCollectionModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendStoreTransferInboundCollection", ch, model)
}

// SendStoreTransferInboundCollection indicates an expected call of SendStoreTransferInboundCollection.
func (mr *MockIWarehouseTransferServiceMockRecorder) SendStoreTransferInboundCollection(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStoreTransferInboundCollection", reflect.TypeOf((*MockIWarehouseTransferService)(nil).SendStoreTransferInboundCollection), ch, model)
}

// SendStoreTransferOutboundCollection mocks base method.
func (m *MockIWarehouseTransferService) SendStoreTransferOutboundCollection(ch chan *SendWarehouseTransferCollectionResponse, model SendWarehouseTransferCollectionModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendStoreTransferOutboundCollection", ch, model)
}

// SendStoreTransferOutboundCollection indicates an expected call of SendStoreTransferOutboundCollection.
func (mr *MockIWarehouseTransferServiceMockRecorder) SendStoreTransferOutboundCollection(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStoreTransferOutboundCollection", reflect.TypeOf((*MockIWarehouseTransferService)(nil).SendStoreTransferOutboundCollection), ch, model)
}
