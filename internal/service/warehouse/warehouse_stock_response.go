package warehouse

type StockMovementServiceResponse struct {
	Error error
}

type StockCountResponse struct {
	Error error
}

type Reason struct {
	GlobalReasonCode        int
	GlobalReasonDescription string
	LogoReasonCode          string
	LogoReasonDescription   string
	LogoSlipTypeId          int
}

type SendWarehouseStockCountsToLogoServiceResponse struct {
	SentCount int   `json:"-"`
	Error     error `json:"-"`
}

type SendWarehouseStockCountsApprovalStatusAsMailServiceResponse struct {
	Error error `json:"-"`
}
