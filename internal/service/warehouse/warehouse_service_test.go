package warehouse

import (
	"errors"
	"testing"
	"time"

	warehouseDb "logo-adapter/internal/data/database/warehouse"
	"logo-adapter/internal/data/proxy/datafridge"
	"logo-adapter/internal/data/proxy/logistic"
	"logo-adapter/internal/data/proxy/logo"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type WarehouseServiceTestSuite struct {
	suite.Suite
	warehouseService          IWarehouseService
	mockEnvironment           *env.MockIEnvironment
	mockLogger                *logger.MockILogger
	mockValidator             *validator.MockIValidator
	mockCacher                *cacher.MockICacher
	mockDataFridgeVendorProxy *datafridge.MockIVendorProxy
	mockLogoProxy             *logo.MockILogoProxy
	mockWarehouseDb           *warehouseDb.MockIWarehouseDb
	mockLogisticProxy         *logistic.MockILogisticProxy
}

// Run suite.
func TestService(t *testing.T) {
	suite.Run(t, new(WarehouseServiceTestSuite))
}

// Runs before each test in the suite.
func (p *WarehouseServiceTestSuite) SetupTest() {
	p.T().Log("Setup")

	ctrl := gomock.NewController(p.T())
	defer ctrl.Finish()

	p.mockEnvironment = env.NewMockIEnvironment(ctrl)
	p.mockLogger = logger.NewMockILogger(ctrl)
	p.mockValidator = validator.NewMockIValidator(ctrl)
	p.mockCacher = cacher.NewMockICacher(ctrl)
	p.mockDataFridgeVendorProxy = datafridge.NewMockIVendorProxy(ctrl)
	p.mockLogoProxy = logo.NewMockILogoProxy(ctrl)
	p.mockWarehouseDb = warehouseDb.NewMockIWarehouseDb(ctrl)
	p.mockLogisticProxy = logistic.NewMockILogisticProxy(ctrl)

	p.warehouseService = NewWarehouseService(p.mockEnvironment, p.mockLogger, p.mockValidator, p.mockCacher, p.mockWarehouseDb, p.mockDataFridgeVendorProxy, p.mockLogoProxy, p.mockLogisticProxy)
}

// Runs after each test in the suite.
func (p *WarehouseServiceTestSuite) TearDownTest() {
	p.T().Log("Teardown")
}

func (p *WarehouseServiceTestSuite) TestAddOrUpdateWarehouse_ServiceModelValidation_ReturnsError() {
	// Arrange
	model := AddOrUpdateWarehouseServiceModel{
		GlobalEntityId:   "",
		PlatformVendorId: "1",
		WarehouseId:      "2",
		WarehouseName:    "3",
		Address:          "4",
		City:             "5",
		PayloadTimestamp: time.Now(),
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&model)).
		Return(errors.New("GlobalEntityId cannot be empty"))

	// Act
	ch := make(chan *AddOrUpdateWarehouseServiceResponse)
	defer close(ch)

	go p.warehouseService.AddOrUpdateWarehouse(ch, &model)
	response := <-ch

	// Assert
	p.EqualError(response.Error, "GlobalEntityId cannot be empty")
}

func (p *WarehouseServiceTestSuite) TestAddOrUpdateWarehouse_DataFridgeVendorProxy_ReturnsError() {
	// Arrange
	serviceModel := AddOrUpdateWarehouseServiceModel{
		GlobalEntityId:   "0",
		PlatformVendorId: "1",
		WarehouseId:      "2",
		WarehouseName:    "3",
		Address:          "4",
		City:             "5",
		PayloadTimestamp: time.Now(),
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&serviceModel)).
		Return(nil)

	proxyModel := &datafridge.GetVendorProxyModel{
		GlobalEntityId: serviceModel.GlobalEntityId,
		VendorId:       serviceModel.PlatformVendorId,
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&proxyModel)).
		Return(nil)

	p.mockDataFridgeVendorProxy.
		EXPECT().
		GetVendor(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *datafridge.GetVendorProxyResponse, model *datafridge.GetVendorProxyModel) {
			ch <- &datafridge.GetVendorProxyResponse{
				Error: errors.New("DataFridge error"),
			}
			return
		})

	// Act
	ch := make(chan *AddOrUpdateWarehouseServiceResponse)
	defer close(ch)

	go p.warehouseService.AddOrUpdateWarehouse(ch, &serviceModel)
	response := <-ch

	// Assert
	p.EqualError(response.Error, "DataFridge error")
}

func (p *WarehouseServiceTestSuite) TestAddOrUpdateWarehouse_WarehouseDb_ReturnsError() {
	// Arrange
	serviceModel := AddOrUpdateWarehouseServiceModel{
		GlobalEntityId:   "0",
		PlatformVendorId: "1",
		WarehouseId:      "2",
		WarehouseName:    "3",
		Address:          "4",
		City:             "5",
		PayloadTimestamp: time.Now(),
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&serviceModel)).
		Return(nil)

	proxyModel := &datafridge.GetVendorProxyModel{
		GlobalEntityId: serviceModel.GlobalEntityId,
		VendorId:       serviceModel.PlatformVendorId,
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&proxyModel)).
		Return(nil)

	p.mockDataFridgeVendorProxy.
		EXPECT().
		GetVendor(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *datafridge.GetVendorProxyResponse, model *datafridge.GetVendorProxyModel) {
			ch <- &datafridge.GetVendorProxyResponse{
				Error: nil,
			}
			return
		})

	dbResponse := &warehouseDb.AddOrUpdateWarehouseResponse{
		Error: errors.New("DB error occurred"),
	}

	p.mockWarehouseDb.EXPECT().
		AddOrUpdateWarehouse(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *warehouseDb.AddOrUpdateWarehouseResponse, model *warehouseDb.AddOrUpdateWarehouseModel) {
			ch <- dbResponse
			return
		})

	// Act
	ch := make(chan *AddOrUpdateWarehouseServiceResponse)
	defer close(ch)

	go p.warehouseService.AddOrUpdateWarehouse(ch, &serviceModel)
	response := <-ch

	// Assert
	p.EqualError(response.Error, "DB error occurred")
}

func (p *WarehouseServiceTestSuite) TestAddOrUpdateWarehouse_HappyPath() {
	// Arrange
	serviceModel := AddOrUpdateWarehouseServiceModel{
		GlobalEntityId:   "0",
		PlatformVendorId: "1",
		WarehouseId:      "2",
		WarehouseName:    "3",
		Address:          "4",
		City:             "5",
		PayloadTimestamp: time.Now(),
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&serviceModel)).
		Return(nil)

	proxyModel := &datafridge.GetVendorProxyModel{
		GlobalEntityId: serviceModel.GlobalEntityId,
		VendorId:       serviceModel.PlatformVendorId,
	}

	p.mockValidator.
		EXPECT().
		ValidateStruct(gomock.Eq(&proxyModel)).
		Return(nil)

	p.mockDataFridgeVendorProxy.
		EXPECT().
		GetVendor(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *datafridge.GetVendorProxyResponse, model *datafridge.GetVendorProxyModel) {
			ch <- &datafridge.GetVendorProxyResponse{
				Error: nil,
			}
			return
		})

	dbResponse := &warehouseDb.AddOrUpdateWarehouseResponse{
		Error: nil,
	}

	p.mockWarehouseDb.EXPECT().
		AddOrUpdateWarehouse(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *warehouseDb.AddOrUpdateWarehouseResponse, model *warehouseDb.AddOrUpdateWarehouseModel) {
			ch <- dbResponse
			return
		})

	p.mockLogisticProxy.EXPECT().
		SendWarehouse(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ch chan *logistic.LogisticPosProxyBaseResponse, model *logistic.SendWarehouseModel) {
			ch <- &logistic.LogisticPosProxyBaseResponse{Error: nil}
			return
		})

	// Act
	ch := make(chan *AddOrUpdateWarehouseServiceResponse)
	defer close(ch)

	go p.warehouseService.AddOrUpdateWarehouse(ch, &serviceModel)
	response := <-ch

	// Assert
	p.Equal(response.Error, nil)
}
