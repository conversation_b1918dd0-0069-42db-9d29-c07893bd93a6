// Code generated by MockGen. DO NOT EDIT.
// Source: ../internal/service/auth/auth_service.go

// Package auth is a generated GoMock package.
package auth

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIAuthService is a mock of IAuthService interface.
type MockIAuthService struct {
	ctrl     *gomock.Controller
	recorder *MockIAuthServiceMockRecorder
}

// MockIAuthServiceMockRecorder is the mock recorder for MockIAuthService.
type MockIAuthServiceMockRecorder struct {
	mock *MockIAuthService
}

// NewMockIAuthService creates a new mock instance.
func NewMockIAuthService(ctrl *gomock.Controller) *MockIAuthService {
	mock := &MockIAuthService{ctrl: ctrl}
	mock.recorder = &MockIAuthServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAuthService) EXPECT() *MockIAuthServiceMockRecorder {
	return m.recorder
}

// GetAccessToken mocks base method.
func (m *MockIAuthService) GetAccessToken(ch chan *LoginServiceResponse, model *GetAccessTokenServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetAccessToken", ch, model)
}

// GetAccessToken indicates an expected call of GetAccessToken.
func (mr *MockIAuthServiceMockRecorder) GetAccessToken(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccessToken", reflect.TypeOf((*MockIAuthService)(nil).GetAccessToken), ch, model)
}

// GetProgrammaticAccessToken mocks base method.
func (m *MockIAuthService) GetProgrammaticAccessToken(ch chan *GetProgrammaticAccessTokenServiceResponse, model *GetProgrammaticAccessTokenServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GetProgrammaticAccessToken", ch, model)
}

// GetProgrammaticAccessToken indicates an expected call of GetProgrammaticAccessToken.
func (mr *MockIAuthServiceMockRecorder) GetProgrammaticAccessToken(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProgrammaticAccessToken", reflect.TypeOf((*MockIAuthService)(nil).GetProgrammaticAccessToken), ch, model)
}

// Login mocks base method.
func (m *MockIAuthService) Login(ch chan *LoginServiceResponse, model *LoginServiceModel) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Login", ch, model)
}

// Login indicates an expected call of Login.
func (mr *MockIAuthServiceMockRecorder) Login(ch, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockIAuthService)(nil).Login), ch, model)
}
