package sample

import (
	pbSample "logo-adapter/internal/data/pubsub/publisher/sample"
	snsPublisher "logo-adapter/internal/data/sns/publisher/sample"
	sqsPublisher "logo-adapter/internal/data/sqs/publisher/sample"
)

type GetSampleServiceModel struct {
	Id         int    `validate:"required,gte=0"`
	SampleName string `validate:"required"`
}

type UpdateSampleServiceModel struct {
	SampleId     int    `validate:"required,gte=0"`
	SampleStatus int    `validate:"required,gte=0"`
	ModifiedBy   string `validate:"required"`
}

type PublishPubSubMessageServiceModel struct {
	Count   int                           `validate:"required,gte=0,lte=50"`
	Message pbSample.SamplePublisherModel `validate:"required"`
}

type PublishPubSubProtoMessageServiceModel struct {
	Count   int                                `validate:"required,gte=0,lte=50"`
	Message pbSample.SampleProtoPublisherModel `validate:"required"`
}

type PublishSqsMessageServiceModel struct {
	Count   int                               `validate:"required,gte=0,lte=50"`
	Message sqsPublisher.SamplePublisherModel `validate:"required"`
}

type PublishSnsMessageServiceModel struct {
	Count   int                                  `validate:"required,gte=0,lte=50"`
	Message snsPublisher.SampleSnsPublisherModel `validate:"required"`
}

type PostSampleXmlServiceModel struct {
	SampleName string `validate:"required"`
	SampleType string `validate:"required"`
	SampleCode *int
}
