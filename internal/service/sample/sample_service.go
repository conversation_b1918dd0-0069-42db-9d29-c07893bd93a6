package sample

import (
	"encoding/json"
	"logo-adapter/internal/data/database/sample"
	proxySample "logo-adapter/internal/data/proxy/sample"
	pbSample "logo-adapter/internal/data/pubsub/publisher/sample"
	pbSnsSample "logo-adapter/internal/data/sns/publisher/sample"
	pbSqsSample "logo-adapter/internal/data/sqs/publisher/sample"
	"time"

	"logo-adapter/internal/data/pubsub/publisher"
	snsPublisher "logo-adapter/internal/data/sns/publisher"
	sqsPublisher "logo-adapter/internal/data/sqs/publisher"
	"logo-adapter/internal/util/cacher"
	"logo-adapter/internal/util/env"
	"logo-adapter/internal/util/logger"
	"logo-adapter/internal/util/validator"
)

type ISampleService interface {
	GetGoogle(ch chan *GetSampleServiceResponse, model *GetSampleServiceModel)
	GetDatabase(ch chan *GetSampleServiceResponse, model *GetSampleServiceModel)
	GetCache(ch chan *GetSampleServiceResponse, model *GetSampleServiceModel)
	PublishPubSubMessage(ch chan *PublishPubSubMessageServiceResponse, model *PublishPubSubMessageServiceModel)
	PublishPubSubProtoMessage(ch chan *PublishPubSubMessageServiceResponse, model *PublishPubSubProtoMessageServiceModel)
	PublishSqsMessage(ch chan *PublishSqsMessageServiceResponse, model *PublishSqsMessageServiceModel)
	UpdateSample(ch chan *UpdateSampleServiceResponse, model *UpdateSampleServiceModel)
	PublishSnsMessage(ch chan *PublishSnsMessageServiceResponse, model *PublishSnsMessageServiceModel)
}

type SampleService struct {
	environment          env.IEnvironment
	loggr                logger.ILogger
	validatr             validator.IValidator
	cachr                cacher.ICacher
	sampleProxy          proxySample.ISampleProxy
	sampleDb             sample.ISampleDb
	samplePublisher      pbSample.ISamplePublisher
	sampleProtoPublisher pbSample.ISampleProtoPublisher
	sampleSqsPublisher   pbSqsSample.ISamplePublisher
	sampleSnsPublisher   pbSnsSample.ISampleSnsPublisher
}

// NewSampleService
// Returns a new SampleService.
func NewSampleService(environment env.IEnvironment, loggr logger.ILogger, validatr validator.IValidator, cachr cacher.ICacher, sampleProxy proxySample.ISampleProxy, sampleDb sample.ISampleDb, samplePublisher pbSample.ISamplePublisher, sampleProtoPublisher pbSample.ISampleProtoPublisher, sampleSqsPublisher pbSqsSample.ISamplePublisher, sampleSnsPublisher pbSnsSample.ISampleSnsPublisher) ISampleService {
	service := SampleService{
		environment: environment,
		loggr:       loggr,
		validatr:    validatr,
		cachr:       cachr,
	}

	if sampleProxy != nil {
		service.sampleProxy = sampleProxy
	} else {
		service.sampleProxy = proxySample.NewSampleProxy(environment, loggr, validatr, cachr)
	}

	if sampleDb != nil {
		service.sampleDb = sampleDb
	} else {
		service.sampleDb = sample.NewSampleDb(environment, loggr, validatr, cachr)
	}

	if samplePublisher != nil {
		service.samplePublisher = samplePublisher
	} else {
		service.samplePublisher = pbSample.NewSamplePublisher(environment, loggr, validatr, cachr)
	}

	if sampleSqsPublisher != nil {
		service.sampleSqsPublisher = sampleSqsPublisher
	} else {
		service.sampleSqsPublisher = pbSqsSample.NewSamplePublisher(environment, loggr, validatr, cachr)
	}

	if sampleSnsPublisher != nil {
		service.sampleSnsPublisher = sampleSnsPublisher
	} else {
		service.sampleSnsPublisher = pbSnsSample.NewSampleSnsPublisher(environment, loggr, validatr, cachr)
	}

	if sampleProtoPublisher != nil {
		service.sampleProtoPublisher = sampleProtoPublisher
	} else {
		service.sampleProtoPublisher = pbSample.NewSampleProtoPublisher(environment, loggr, validatr, cachr)
	}

	return &service
}

// GetGoogle
// Gets a sample service response from Google via proxy.
func (s *SampleService) GetGoogle(ch chan *GetSampleServiceResponse, model *GetSampleServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetSampleServiceResponse{Error: modelErr}
		return
	}

	getSampleCh := make(chan *proxySample.GetSampleProxyResponse)
	defer close(getSampleCh)
	go s.sampleProxy.GetGoogle(getSampleCh, &proxySample.GetSampleProxyModel{
		Id:         model.Id,
		SampleName: model.SampleName,
	})
	sampleProxyResponse := <-getSampleCh
	if sampleProxyResponse.Error != nil {
		ch <- &GetSampleServiceResponse{Error: sampleProxyResponse.Error}
		return
	}

	ch <- &GetSampleServiceResponse{
		Id:         sampleProxyResponse.Id,
		SampleName: sampleProxyResponse.SampleName,
	}
}

// GetDatabase
// Gets a sample service response from postgresql database.
func (s *SampleService) GetDatabase(ch chan *GetSampleServiceResponse, model *GetSampleServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetSampleServiceResponse{Error: modelErr}
		return
	}

	getSampleCh := make(chan *sample.GetSampleDbResponse)
	defer close(getSampleCh)
	go s.sampleDb.GetSample(getSampleCh, &sample.GetSampleDbModel{
		Id:         model.Id,
		SampleName: model.SampleName,
	})

	sampleDbResponse := <-getSampleCh
	if sampleDbResponse.Error != nil {
		ch <- &GetSampleServiceResponse{Error: sampleDbResponse.Error}
		return
	}

	ch <- &GetSampleServiceResponse{
		Id:         sampleDbResponse.Id,
		SampleName: sampleDbResponse.SampleName,
	}
}

// GetCache
// Gets a sample service response from redis cache.
func (s *SampleService) GetCache(ch chan *GetSampleServiceResponse, model *GetSampleServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &GetSampleServiceResponse{Error: modelErr}
		return
	}

	var response GetSampleServiceResponse
	cacheValue := s.cachr.Get("dummy_cache_key")
	if cacheValue != nil {
		err := json.Unmarshal([]byte(*cacheValue), &response)
		if err != nil {
			s.loggr.Panic("Panicked while unmarshalling json to type.")
		}

		ch <- &response
		return
	}

	response = GetSampleServiceResponse{
		Id:         1,
		SampleName: "Cached new response here!",
	}

	s.cachr.Set("dummy_cache_key", response, 30*time.Second)
	ch <- &response
}

// PublishPubSubMessage
// Publishes a pub sub sample message to topic.
func (s *SampleService) PublishPubSubMessage(ch chan *PublishPubSubMessageServiceResponse, model *PublishPubSubMessageServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &PublishPubSubMessageServiceResponse{Error: modelErr}
		return
	}

	publisherCh := make(chan publisher.PublisherResponse, model.Count)
	defer close(publisherCh)
	for i := 0; i < model.Count; i++ {
		go s.samplePublisher.Publish(publisherCh, &model.Message, map[string]string{publisher.DummyAttribute: "overriding dummy attribute value here"})
	}

	// Await all responses from routines simultaneously.
	var messageIds []string
	for i := 0; i < model.Count; i++ {
		response := <-publisherCh
		if response.Error != nil {
			ch <- &PublishPubSubMessageServiceResponse{Error: response.Error}
			return
		}
		messageIds = append(messageIds, *response.MessageId)
	}

	ch <- &PublishPubSubMessageServiceResponse{
		MessageIds:   messageIds,
		IsSuccessful: true,
	}
}

// PublishPubSubProtoMessage
// Publishes a pub sub sample message based on proto to topic.
func (s *SampleService) PublishPubSubProtoMessage(ch chan *PublishPubSubMessageServiceResponse, model *PublishPubSubProtoMessageServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &PublishPubSubMessageServiceResponse{Error: modelErr}
		return
	}

	publisherCh := make(chan publisher.PublisherResponse, model.Count)
	defer close(publisherCh)
	for i := 0; i < model.Count; i++ {
		go s.sampleProtoPublisher.Publish(publisherCh, &model.Message, map[string]string{publisher.DummyAttribute: "overriding dummy attribute value here"})
	}

	// Await all responses from routines simultaneously.
	var messageIds []string
	for i := 0; i < model.Count; i++ {
		response := <-publisherCh
		if response.Error != nil {
			ch <- &PublishPubSubMessageServiceResponse{Error: response.Error}
			return
		}
		messageIds = append(messageIds, *response.MessageId)
	}

	ch <- &PublishPubSubMessageServiceResponse{
		MessageIds:   messageIds,
		IsSuccessful: true,
	}
}

// PublishSqsMessage
// Publishes a pub sub sample message to topic.
func (s *SampleService) PublishSqsMessage(ch chan *PublishSqsMessageServiceResponse, model *PublishSqsMessageServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &PublishSqsMessageServiceResponse{Error: modelErr}
		return
	}

	publisherCh := make(chan sqsPublisher.PublisherResponse, model.Count)
	defer close(publisherCh)
	for i := 0; i < model.Count; i++ {
		go s.sampleSqsPublisher.Publish(publisherCh, &model.Message, map[string]string{sqsPublisher.DummyAttribute: "overriding dummy attribute value here"})
	}

	// Await all responses from routines simultaneously.
	var messageIds []string
	for i := 0; i < model.Count; i++ {
		response := <-publisherCh
		if response.Error != nil {
			ch <- &PublishSqsMessageServiceResponse{Error: response.Error}
			return
		}
		messageIds = append(messageIds, *response.MessageId)
	}

	ch <- &PublishSqsMessageServiceResponse{
		MessageIds:   messageIds,
		IsSuccessful: true,
	}
}

// UpdateSample
// Updates a sample with details provided by google pub sub message or via http endpoint.
func (s *SampleService) UpdateSample(ch chan *UpdateSampleServiceResponse, model *UpdateSampleServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &UpdateSampleServiceResponse{Error: modelErr}
		return
	}

	// Doing some dummy stuff.
	// Launching rocket to the sky..
	// Calculating...

	response := UpdateSampleServiceResponse{IsSuccessful: true}
	ch <- &response
}

// PublishSnsMessage
// Publishes a sns sample message to topic.
func (s *SampleService) PublishSnsMessage(ch chan *PublishSnsMessageServiceResponse, model *PublishSnsMessageServiceModel) {
	modelErr := s.validatr.ValidateStruct(model)
	if modelErr != nil {
		ch <- &PublishSnsMessageServiceResponse{Error: modelErr}
		return
	}

	publisherCh := make(chan snsPublisher.PublisherResponse, model.Count)
	defer close(publisherCh)
	for i := 0; i < model.Count; i++ {
		go s.sampleSnsPublisher.Publish(publisherCh, &model.Message, map[string]string{sqsPublisher.DummyAttribute: "overriding dummy attribute value here"})
	}

	// Await all responses from routines simultaneously.
	var messageIds []string
	for i := 0; i < model.Count; i++ {
		response := <-publisherCh
		if response.Error != nil {
			ch <- &PublishSnsMessageServiceResponse{Error: response.Error}
			return
		}
		messageIds = append(messageIds, *response.MessageId)
	}

	ch <- &PublishSnsMessageServiceResponse{
		MessageIds:   messageIds,
		IsSuccessful: true,
	}
}
