version: "3.8"

volumes:
  postgres:
    external: false

services:
  logo-adapter-app:
    build:
      context: .
      dockerfile: Dockerfile
      # GitHub credentials required to get private go repositories belong to organization.
      args:
        GITHUB_USERNAME: "your_github_username"
        GITHUB_TOKEN: "your_github_personal_access_token"
    image: logo-adapter:latest
    ports:
      - "8080:8080"
    environment:
      APP_ENVIRONMENT: "Development"
    # You need to bind mount your .aws folder (created via aws-cli) created in your home folder.
    volumes:
      - type: bind
        read_only: true
        source: "your_aws_folder_contains_credentials_absolute_path"
        target: "/root/.aws"
  postgres:
    image: postgres:14.2-bullseye
    ports:
      - "5433:5432"
    volumes:
      - "postgres:/var/lib/postgresql/data"
    environment:
      POSTGRES_USER: "logo-adapter"
      POSTGRES_PASSWORD: "your_db_password"
      POSTGRES_DB: "logo-adapter"
  redis:
    image: redis:6.2.6-bullseye
    ports:
      - "6380:6379"
